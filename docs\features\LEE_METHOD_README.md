# A.T.L.A.S<PERSON> Lee Method Scanner System

## Overview

The A.T.L.A.S. Lee Method Scanner System implements an advanced 5-point TTM Squeeze pattern detection algorithm branded as "Lee Method". This system provides real-time scanning and analysis of momentum reversal patterns, specifically targeting the optimal entry point where momentum is about to shift from declining to improving.

## Lee Method Criteria (Modified 3+ Bar TTM Squeeze Algorithm)

The Lee Method identifies high-probability momentum reversal signals using modified criteria with 3+ consecutive declining bars:

### 1. TTM Squeeze Histogram Decline Pattern (MODIFIED)
- **Requirement**: MINIMUM 3 consecutive declining histogram bars (reduced from 5)
- **Implementation**: Detects hist_1 < hist_2 < hist_3 for minimum 3 consecutive bars
- **Purpose**: Identifies momentum decline phase before reversal with increased signal frequency

### 2. Histogram Rebound Signal
- **Requirement**: Less negative bounce where hist > hist[1] AND both values < 0
- **Implementation**: Current histogram improves but remains negative
- **Purpose**: Anticipates the first yellow bar after 3 red bars (momentum turning point)

### 3. EMA 5 Uptrend Confirmation
- **Requirement**: 5-period exponential moving average trending upward
- **Implementation**: EMA5 > EMA5[1] confirms short-term upward price momentum
- **Purpose**: Validates that price action supports the momentum reversal signal

### 4. EMA 8 Uptrend Confirmation
- **Requirement**: 8-period exponential moving average trending upward
- **Implementation**: EMA8 > EMA8[1] confirms medium-term upward price momentum
- **Purpose**: Provides additional trend confirmation to reduce false signals

### 5. Optional TTM Squeeze State Filter
- **Requirement**: Configurable requirement for active TTM Squeeze (BB inside KC)
- **Implementation**: Bollinger Bands inside Keltner Channels with configurable lookback
- **Purpose**: Optional filter for high-probability setups during squeeze conditions

## Technical Specifications

- **MACD Settings**: MACD(12,26,9) for histogram calculation
- **Bollinger Bands**: BB(20,2) for squeeze detection
- **Keltner Channels**: KC(20,1.5) for squeeze detection
- **EMA Periods**: 5 and 8 for trend confirmation
- **Pattern Detection**: Exactly 3 declining histogram bars + rebound signal
- **Signal Type**: Bullish momentum reversal anticipation

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    A.T.L.A.S. Lee Method System            │
├─────────────────────────────────────────────────────────────┤
│  Web Interface (atlas_interface.html)                      │
│  ├── Real-time scanner display                             │
│  ├── Signal visualization                                  │
│  └── Manual scan controls                                  │
├─────────────────────────────────────────────────────────────┤
│  API Layer (atlas_lee_method_api.py)                       │
│  ├── RESTful endpoints                                     │
│  ├── Real-time data serving                               │
│  └── Scanner control interface                            │
├─────────────────────────────────────────────────────────────┤
│  Real-time Scanner (atlas_lee_method_realtime_scanner.py)  │
│  ├── Continuous monitoring                                 │
│  ├── Batch processing                                      │
│  └── Signal management                                     │
├─────────────────────────────────────────────────────────────┤
│  Core Engine (lee_method_scanner.py)                       │
│  ├── Pattern detection algorithms                          │
│  ├── Technical indicator calculations                      │
│  └── Signal generation logic                              │
└─────────────────────────────────────────────────────────────┘
```

## File Structure

### Core Components

- **`lee_method_scanner.py`** - Core Lee Method pattern detection engine
- **`atlas_lee_method_realtime_scanner.py`** - Real-time scanning infrastructure  
- **`atlas_lee_method_api.py`** - Web API server for interface integration
- **`atlas_interface.html`** - Updated web interface with Lee Method scanner

### Utilities

- **`start_lee_method_system.py`** - System startup script
- **`test_lee_method_implementation.py`** - Comprehensive test suite
- **`LEE_METHOD_README.md`** - This documentation file

## Quick Start

### 1. Install Dependencies

```bash
pip install flask flask-cors pandas numpy requests
```

### 2. Start the System

```bash
python start_lee_method_system.py
```

This will:
- Start the Lee Method API server on `http://localhost:5001`
- Open the A.T.L.A.S. interface in your browser
- Begin real-time scanning for Lee Method patterns

### 3. Using the Interface

The right panel of the A.T.L.A.S. interface now shows:
- **Lee Method Scanner Status** - Real-time scanner activity
- **Latest Scans** - Most recent pattern detections
- **Scanner Controls** - Manual refresh and pause/resume
- **Lee Method Criteria** - Reference information

## API Endpoints

### Scanner Status
```
GET /api/lee-method/status
```
Returns current scanner status and performance metrics.

### Latest Signals
```
GET /api/lee-method/signals?limit=10
```
Returns the most recent Lee Method signals.

### Signal by Symbol
```
GET /api/lee-method/signal/{symbol}
```
Returns Lee Method signal for a specific symbol.

### Manual Scan Trigger
```
POST /api/lee-method/scan
```
Triggers an immediate scan of all monitored symbols.

### Lee Method Criteria
```
GET /api/lee-method/criteria
```
Returns detailed information about Lee Method criteria.

### Health Check
```
GET /api/lee-method/health
```
Returns API health status and system information.

## Signal Structure

Lee Method signals contain the following information:

```json
{
  "symbol": "AAPL",
  "signal_type": "bullish_momentum",
  "entry_price": 175.50,
  "target_price": 182.00,
  "stop_loss": 171.00,
  "confidence": 0.85,
  "timeframe": "daily",
  "timestamp": "2024-01-15T10:30:00",
  "histogram_sequence": [0.5, 0.3, 0.1, -0.1, 0.2],
  "momentum_bars": [0.1, 0.15, 0.20, 0.18, 0.25],
  "momentum_confirmation": true,
  "weekly_trend": "bullish",
  "daily_trend": "bullish", 
  "trend_alignment": true,
  "risk_reward_ratio": 2.0,
  "position_size_percent": 2.0
}
```

## Monitored Symbols

The system monitors these 24 popular stocks:
- **Tech**: AAPL, MSFT, GOOGL, AMZN, META, NVDA, AMD, INTC
- **Growth**: TSLA, NFLX, CRM, ORCL, ADBE, PYPL
- **Emerging**: UBER, LYFT, SHOP, SQ, ROKU, ZM, DOCU, SNOW, PLTR, COIN

## Configuration

### Scanner Settings
- **Scan Interval**: 30 seconds
- **Signal Expiry**: 1 hour
- **Batch Size**: 5 symbols per batch
- **API Timeout**: 10 seconds

### Risk Management
- **Default Risk**: 2% per trade
- **Risk/Reward Ratio**: 2:1
- **Maximum Position Size**: 10%

## Testing

Run the comprehensive test suite:

```bash
python test_lee_method_implementation.py
```

The test suite validates:
- Core Lee Method pattern detection (5-point TTM Squeeze algorithm)
- All five Lee Method criteria
- TTM Squeeze histogram calculations
- EMA trend confirmations
- Optional squeeze filter functionality
- Real-time scanner functionality
- API integration
- Signal generation accuracy

## Advantages Over Traditional TTM Squeeze

1. **Anticipates Momentum Reversal** - Detects the turning point before momentum fully reverses
2. **Optimal Entry Timing** - Targets the first less-negative histogram bar after decline
3. **Dual EMA Confirmation** - EMA 5 and EMA 8 uptrends reduce false signals
4. **Configurable Squeeze Filter** - Optional BB/KC squeeze requirement for high-probability setups
5. **Precise Pattern Recognition** - Exactly 3 declining bars + rebound signal eliminates noise
6. **Better Risk/Reward** - Earlier entry point improves risk/reward ratios

## Troubleshooting

### API Key Issues
- The system uses demo API keys by default
- For live data, obtain a valid Financial Modeling Prep API key
- Set the API key in the scanner initialization

### Scanner Not Running
- Check if port 5001 is available
- Verify all dependencies are installed
- Review logs for error messages

### No Signals Detected
- This is normal - Lee Method has strict 5-point criteria
- All 5 conditions must be met simultaneously for a signal
- TTM Squeeze rebound patterns are relatively rare
- Try adjusting the optional squeeze filter setting
- Patterns may not be present in current market conditions
- Try manual scan refresh

### Interface Not Loading
- Ensure API server is running on localhost:5001
- Check browser console for JavaScript errors
- Verify atlas_interface.html is accessible

## Support

For issues or questions about the Lee Method implementation:
1. Check the test results for system validation
2. Review API endpoint responses for data availability
3. Examine log files for detailed error information
4. Verify all three Lee Method criteria are being properly evaluated

---

**A.T.L.A.S. Lee Method Scanner System** - Advanced momentum pattern detection for professional trading analysis.
