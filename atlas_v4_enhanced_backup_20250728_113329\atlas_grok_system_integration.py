"""
A.T.L.A.S. Grok System Integration
System-level integration for Grok AI capabilities across all Atlas components
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum

# Add consolidated path to imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'ai'))

# Core imports
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'core'))
    from models import EngineStatus
    from config import get_api_config
except ImportError:
    # Fallback definitions
    from enum import Enum
    class EngineStatus(Enum):
        INITIALIZING = "initializing"
        ACTIVE = "active"
        STOPPED = "stopped"
        FAILED = "failed"

    def get_api_config():
        return {}

# Import from consolidated Grok integration
try:
    from atlas_grok_integration import (
        AtlasGrokIntegrationEngine,
        GrokAPIClient,
        GrokCapability,
        GrokTaskType,
        GrokRequest,
        GrokResponse
    )
    GROK_INTEGRATION_AVAILABLE = True
except ImportError as e:
    GROK_INTEGRATION_AVAILABLE = False
    logging.getLogger(__name__).warning(f"Grok integration not available: {e}")

logger = logging.getLogger(__name__)

class GrokSystemMode(Enum):
    """Grok system integration modes"""
    DISABLED = "disabled"
    FALLBACK_ONLY = "fallback_only"
    ENHANCED = "enhanced"
    FULL_INTEGRATION = "full_integration"

@dataclass
class GrokSystemConfig:
    """Grok system configuration"""
    mode: GrokSystemMode = GrokSystemMode.ENHANCED
    api_key: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    fallback_enabled: bool = True
    cache_enabled: bool = True
    cache_ttl: int = 300  # 5 minutes
    rate_limit_per_minute: int = 60

class GrokSystemIntegration:
    """System-wide Grok AI integration manager"""
    
    def __init__(self, config: Optional[GrokSystemConfig] = None):
        self.config = config or GrokSystemConfig()
        self.status = EngineStatus.INITIALIZING
        self.grok_engine = None
        self.logger = logging.getLogger(__name__)
        
        # System integration state
        self.integrated_components = set()
        self.active_sessions = {}
        self.performance_metrics = {
            "requests_total": 0,
            "requests_successful": 0,
            "requests_failed": 0,
            "average_response_time": 0.0,
            "cache_hits": 0,
            "fallback_activations": 0
        }
        
        # Load API configuration
        self._load_api_config()
    
    def _load_api_config(self):
        """Load Grok API configuration"""
        try:
            api_config = get_api_config()
            self.config.api_key = api_config.get("grok_api_key") or os.getenv("GROK_API_KEY")
            
            if not self.config.api_key:
                self.logger.warning("[GROK_SYS] No Grok API key found - running in fallback mode")
                self.config.mode = GrokSystemMode.FALLBACK_ONLY
            else:
                self.logger.info("[GROK_SYS] Grok API key loaded successfully")
                
        except Exception as e:
            self.logger.error(f"[GROK_SYS] API config loading failed: {e}")
            self.config.mode = GrokSystemMode.DISABLED
    
    async def initialize(self):
        """Initialize the Grok system integration"""
        try:
            self.logger.info("[GROK_SYS] Initializing Grok system integration...")
            
            if not GROK_INTEGRATION_AVAILABLE:
                self.logger.warning("[GROK_SYS] Grok integration not available - using fallback mode")
                self.config.mode = GrokSystemMode.FALLBACK_ONLY
                self.status = EngineStatus.ACTIVE
                return
            
            if self.config.mode == GrokSystemMode.DISABLED:
                self.logger.info("[GROK_SYS] Grok integration disabled")
                self.status = EngineStatus.ACTIVE
                return
            
            # Initialize Grok engine
            if self.config.api_key and self.config.mode != GrokSystemMode.FALLBACK_ONLY:
                self.grok_engine = AtlasGrokIntegrationEngine()
                await self.grok_engine.initialize()
                
                # Test connection
                test_successful = await self._test_grok_connection()
                if not test_successful and self.config.fallback_enabled:
                    self.logger.warning("[GROK_SYS] Grok connection failed - switching to fallback mode")
                    self.config.mode = GrokSystemMode.FALLBACK_ONLY
            
            self.status = EngineStatus.ACTIVE
            self.logger.info(f"[GROK_SYS] Grok system integration initialized - Mode: {self.config.mode.value}")
            
        except Exception as e:
            self.logger.error(f"[GROK_SYS] Initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise
    
    async def _test_grok_connection(self) -> bool:
        """Test Grok API connection"""
        try:
            if not self.grok_engine:
                return False
            
            # Simple test request
            test_request = GrokRequest(
                task_type=GrokTaskType.REASONING,
                content="Test connection",
                capabilities=[GrokCapability.REASONING]
            )
            
            response = await self.grok_engine.process_request(test_request)
            return response and response.success
            
        except Exception as e:
            self.logger.error(f"[GROK_SYS] Connection test failed: {e}")
            return False
    
    async def enhance_trading_analysis(self, symbol: str, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance trading analysis with Grok AI"""
        try:
            if self.config.mode == GrokSystemMode.DISABLED:
                return analysis_data
            
            if not self.grok_engine or self.config.mode == GrokSystemMode.FALLBACK_ONLY:
                return self._fallback_analysis_enhancement(analysis_data)
            
            # Create Grok request for trading analysis
            request = GrokRequest(
                task_type=GrokTaskType.MARKET_ANALYSIS,
                content=f"Enhance trading analysis for {symbol}",
                capabilities=[GrokCapability.REASONING, GrokCapability.MARKET_ANALYSIS],
                context=analysis_data
            )
            
            start_time = datetime.now()
            response = await self.grok_engine.process_request(request)
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Update metrics
            self.performance_metrics["requests_total"] += 1
            if response and response.success:
                self.performance_metrics["requests_successful"] += 1
                self._update_average_response_time(response_time)
                
                # Merge Grok enhancements with original analysis
                enhanced_analysis = analysis_data.copy()
                enhanced_analysis.update({
                    "grok_enhanced": True,
                    "grok_insights": response.content,
                    "grok_confidence": response.confidence,
                    "enhancement_timestamp": datetime.now().isoformat()
                })
                
                return enhanced_analysis
            else:
                self.performance_metrics["requests_failed"] += 1
                return self._fallback_analysis_enhancement(analysis_data)
                
        except Exception as e:
            self.logger.error(f"[GROK_SYS] Analysis enhancement failed: {e}")
            self.performance_metrics["requests_failed"] += 1
            return self._fallback_analysis_enhancement(analysis_data)
    
    def _fallback_analysis_enhancement(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback analysis enhancement without Grok"""
        self.performance_metrics["fallback_activations"] += 1
        
        enhanced_analysis = analysis_data.copy()
        enhanced_analysis.update({
            "grok_enhanced": False,
            "fallback_mode": True,
            "enhancement_timestamp": datetime.now().isoformat(),
            "fallback_insights": "Analysis completed using traditional methods"
        })
        
        return enhanced_analysis
    
    def _update_average_response_time(self, response_time: float):
        """Update average response time metric"""
        current_avg = self.performance_metrics["average_response_time"]
        total_requests = self.performance_metrics["requests_successful"]
        
        if total_requests == 1:
            self.performance_metrics["average_response_time"] = response_time
        else:
            # Calculate running average
            new_avg = ((current_avg * (total_requests - 1)) + response_time) / total_requests
            self.performance_metrics["average_response_time"] = new_avg
    
    async def enhance_market_sentiment(self, sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance market sentiment analysis with Grok AI"""
        try:
            if self.config.mode == GrokSystemMode.DISABLED or not self.grok_engine:
                return sentiment_data
            
            request = GrokRequest(
                task_type=GrokTaskType.SENTIMENT_ANALYSIS,
                content="Enhance market sentiment analysis",
                capabilities=[GrokCapability.SENTIMENT_ANALYSIS, GrokCapability.REASONING],
                context=sentiment_data
            )
            
            response = await self.grok_engine.process_request(request)
            
            if response and response.success:
                enhanced_sentiment = sentiment_data.copy()
                enhanced_sentiment.update({
                    "grok_sentiment_score": response.confidence,
                    "grok_sentiment_insights": response.content,
                    "enhanced_timestamp": datetime.now().isoformat()
                })
                return enhanced_sentiment
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error(f"[GROK_SYS] Sentiment enhancement failed: {e}")
            return sentiment_data
    
    def register_component(self, component_name: str):
        """Register a component as Grok-integrated"""
        self.integrated_components.add(component_name)
        self.logger.info(f"[GROK_SYS] Component registered: {component_name}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get Grok system integration status"""
        return {
            "status": self.status.value,
            "mode": self.config.mode.value,
            "grok_available": GROK_INTEGRATION_AVAILABLE,
            "api_key_configured": bool(self.config.api_key),
            "integrated_components": list(self.integrated_components),
            "performance_metrics": self.performance_metrics.copy(),
            "active_sessions": len(self.active_sessions)
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get detailed performance metrics"""
        metrics = self.performance_metrics.copy()
        
        if metrics["requests_total"] > 0:
            metrics["success_rate"] = metrics["requests_successful"] / metrics["requests_total"]
            metrics["failure_rate"] = metrics["requests_failed"] / metrics["requests_total"]
            metrics["fallback_rate"] = metrics["fallback_activations"] / metrics["requests_total"]
        else:
            metrics["success_rate"] = 0.0
            metrics["failure_rate"] = 0.0
            metrics["fallback_rate"] = 0.0
        
        return metrics

# Global Grok system integration instance
grok_system = GrokSystemIntegration()

# Export main components
__all__ = [
    'GrokSystemMode',
    'GrokSystemConfig',
    'GrokSystemIntegration',
    'grok_system'
]
