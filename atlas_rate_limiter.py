"""
A.T.L.A.S. Advanced Rate Limiting Manager
Intelligent rate limiting, retry mechanisms, and API optimization for real-time scanning
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
import random
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class APIProvider(Enum):
    """API provider types"""
    FMP = "fmp"
    ALPACA = "alpaca"
    YFINANCE = "yfinance"
    POLYGON = "polygon"
    ALPHA_VANTAGE = "alpha_vantage"


@dataclass
class RateLimitConfig:
    """Rate limit configuration for API providers"""
    requests_per_minute: int = 60
    requests_per_second: int = 5
    burst_limit: int = 10
    cooldown_seconds: int = 60
    retry_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 30.0
    jitter: bool = True


@dataclass
class APIRequest:
    """API request tracking"""
    provider: APIProvider
    endpoint: str
    params: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    attempt: int = 0
    priority: int = 1  # 1=high, 2=medium, 3=low


class RequestQueue:
    """Priority-based request queue with intelligent scheduling"""
    
    def __init__(self):
        self.queues = {1: deque(), 2: deque(), 3: deque()}  # Priority queues
        self.lock = asyncio.Lock()
    
    async def put(self, request: APIRequest):
        """Add request to appropriate priority queue"""
        async with self.lock:
            self.queues[request.priority].append(request)
    
    async def get(self) -> Optional[APIRequest]:
        """Get next request based on priority"""
        async with self.lock:
            # Check high priority first, then medium, then low
            for priority in [1, 2, 3]:
                if self.queues[priority]:
                    return self.queues[priority].popleft()
            return None
    
    async def size(self) -> int:
        """Get total queue size"""
        async with self.lock:
            return sum(len(q) for q in self.queues.values())


class AtlasRateLimiter:
    """Advanced rate limiting manager with intelligent throttling and retry mechanisms"""
    
    def __init__(self):
        self.configs = {
            APIProvider.FMP: RateLimitConfig(
                requests_per_minute=3000,  # FMP paid tier - much higher capacity
                requests_per_second=50,    # Aggressive but safe for paid tier
                burst_limit=100,           # Higher burst for batch processing
                cooldown_seconds=30
            ),
            APIProvider.ALPACA: RateLimitConfig(
                requests_per_minute=1000,  # Alpaca paid tier
                requests_per_second=20,
                burst_limit=50,
                cooldown_seconds=15
            ),
            APIProvider.YFINANCE: RateLimitConfig(
                requests_per_minute=300,   # More aggressive for yfinance
                requests_per_second=5,
                burst_limit=15,
                cooldown_seconds=60
            ),
            APIProvider.POLYGON: RateLimitConfig(
                requests_per_minute=1000,  # Polygon paid tier
                requests_per_second=20,
                burst_limit=40,
                cooldown_seconds=30
            ),
            APIProvider.ALPHA_VANTAGE: RateLimitConfig(
                requests_per_minute=75,    # Alpha Vantage premium
                requests_per_second=5,
                burst_limit=10,
                cooldown_seconds=120
            )
        }
        
        # Request tracking
        self.request_history = defaultdict(deque)  # provider -> timestamps
        self.burst_counters = defaultdict(int)     # provider -> current burst count
        self.cooldown_until = defaultdict(float)   # provider -> cooldown end time
        self.failed_requests = defaultdict(int)    # provider -> failure count
        
        # Enhanced request queue and processing
        self.request_queue = RequestQueue()
        self.processing = False
        self.session_cache = {}

        # Intelligent batching system
        self.batch_queues = defaultdict(list)  # provider -> batch requests
        self.batch_timers = {}  # provider -> timer
        self.batch_sizes = {
            APIProvider.FMP: 10,        # Batch 10 requests for FMP
            APIProvider.ALPACA: 8,      # Batch 8 requests for Alpaca
            APIProvider.YFINANCE: 5,    # Batch 5 requests for yfinance
            APIProvider.POLYGON: 8,     # Batch 8 requests for Polygon
            APIProvider.ALPHA_VANTAGE: 3 # Batch 3 requests for Alpha Vantage
        }
        self.batch_timeout = 2.0  # Max wait time before processing batch

        # Adaptive throttling
        self.provider_performance = defaultdict(lambda: {
            'avg_response_time': 1.0,
            'success_rate': 1.0,
            'last_success': time.time(),
            'consecutive_failures': 0,
            'throttle_factor': 1.0
        })
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'rate_limited_requests': 0,
            'average_response_time': 0.0,
            'cache_hits': 0
        }
        
        # Response cache
        self.cache = {}
        self.cache_ttl = {}
        self.default_cache_duration = 30  # 30 seconds default
        
    async def start_processing(self):
        """Start the request processing loop"""
        if not self.processing:
            self.processing = True
            asyncio.create_task(self._process_queue())
            logger.info("Rate limiter processing started")
    
    async def stop_processing(self):
        """Stop the request processing loop"""
        self.processing = False

        # Process any remaining batches
        await self._flush_all_batches()

        logger.info("Rate limiter processing stopped")

    async def add_to_batch(self, request: APIRequest) -> bool:
        """Add request to intelligent batch queue"""
        try:
            provider = request.provider

            # Add to batch queue
            self.batch_queues[provider].append(request)

            # Check if batch is ready to process
            batch_size = self.batch_sizes.get(provider, 5)

            if len(self.batch_queues[provider]) >= batch_size:
                # Process batch immediately when full
                await self._process_batch(provider)
                return True
            else:
                # Set timer for batch timeout if not already set
                if provider not in self.batch_timers:
                    self.batch_timers[provider] = asyncio.create_task(
                        self._batch_timeout_handler(provider)
                    )
                return True

        except Exception as e:
            logger.error(f"Error adding request to batch: {e}")
            return False

    async def _batch_timeout_handler(self, provider: APIProvider):
        """Handle batch timeout - process partial batch"""
        try:
            await asyncio.sleep(self.batch_timeout)

            # Process batch if it still has requests
            if self.batch_queues[provider]:
                await self._process_batch(provider)

        except asyncio.CancelledError:
            pass  # Timer was cancelled, batch was processed early
        except Exception as e:
            logger.error(f"Error in batch timeout handler for {provider.value}: {e}")

    async def _process_batch(self, provider: APIProvider):
        """Process a batch of requests for a provider"""
        try:
            batch = self.batch_queues[provider].copy()
            self.batch_queues[provider].clear()

            # Cancel timeout timer if exists
            if provider in self.batch_timers:
                self.batch_timers[provider].cancel()
                del self.batch_timers[provider]

            if not batch:
                return

            # Apply adaptive throttling
            throttle_delay = self._calculate_throttle_delay(provider)
            if throttle_delay > 0:
                await asyncio.sleep(throttle_delay)

            # Process batch requests concurrently (but within rate limits)
            semaphore_limit = min(len(batch), self.configs[provider].requests_per_second)
            semaphore = asyncio.Semaphore(semaphore_limit)

            async def process_single_request(request):
                async with semaphore:
                    return await self._execute_request(request)

            # Execute batch
            start_time = time.time()
            results = await asyncio.gather(
                *[process_single_request(req) for req in batch],
                return_exceptions=True
            )
            batch_time = time.time() - start_time

            # Update performance metrics
            await self._update_batch_performance(provider, batch, results, batch_time)

            logger.debug(f"Processed batch of {len(batch)} requests for {provider.value} in {batch_time:.2f}s")

        except Exception as e:
            logger.error(f"Error processing batch for {provider.value}: {e}")

    async def _flush_all_batches(self):
        """Flush all pending batches"""
        try:
            for provider in list(self.batch_queues.keys()):
                if self.batch_queues[provider]:
                    await self._process_batch(provider)
        except Exception as e:
            logger.error(f"Error flushing batches: {e}")

    def _calculate_throttle_delay(self, provider: APIProvider) -> float:
        """Calculate adaptive throttle delay based on provider performance"""
        try:
            perf = self.provider_performance[provider]

            # Base delay from configuration
            base_delay = 1.0 / self.configs[provider].requests_per_second

            # Apply throttle factor based on recent performance
            throttle_delay = base_delay * perf['throttle_factor']

            # Additional delay for consecutive failures
            if perf['consecutive_failures'] > 0:
                failure_penalty = min(perf['consecutive_failures'] * 0.5, 5.0)
                throttle_delay += failure_penalty

            return throttle_delay

        except Exception as e:
            logger.error(f"Error calculating throttle delay: {e}")
            return 1.0

    async def _update_batch_performance(self, provider: APIProvider, batch: List[APIRequest],
                                      results: List[Any], batch_time: float):
        """Update performance metrics based on batch results"""
        try:
            perf = self.provider_performance[provider]

            # Count successes and failures
            successes = sum(1 for result in results if not isinstance(result, Exception))
            failures = len(results) - successes

            # Update success rate (exponential moving average)
            batch_success_rate = successes / len(results) if results else 0
            perf['success_rate'] = 0.8 * perf['success_rate'] + 0.2 * batch_success_rate

            # Update average response time
            avg_batch_time = batch_time / len(batch) if batch else batch_time
            perf['avg_response_time'] = 0.8 * perf['avg_response_time'] + 0.2 * avg_batch_time

            # Update consecutive failures
            if failures > 0:
                perf['consecutive_failures'] += failures
            else:
                perf['consecutive_failures'] = max(0, perf['consecutive_failures'] - 1)
                perf['last_success'] = time.time()

            # Adjust throttle factor based on performance
            if perf['success_rate'] > 0.95 and perf['avg_response_time'] < 2.0:
                # Good performance - reduce throttling
                perf['throttle_factor'] = max(0.5, perf['throttle_factor'] * 0.95)
            elif perf['success_rate'] < 0.8 or perf['avg_response_time'] > 5.0:
                # Poor performance - increase throttling
                perf['throttle_factor'] = min(3.0, perf['throttle_factor'] * 1.1)

            # Update global metrics
            self.metrics['total_requests'] += len(batch)
            self.metrics['successful_requests'] += successes
            self.metrics['failed_requests'] += failures

            if successes > 0:
                # Update average response time (global)
                current_avg = self.metrics['average_response_time']
                total_requests = self.metrics['total_requests']
                self.metrics['average_response_time'] = (
                    (current_avg * (total_requests - len(batch)) + batch_time) / total_requests
                )

        except Exception as e:
            logger.error(f"Error updating batch performance: {e}")

    def get_provider_stats(self, provider: APIProvider) -> Dict[str, Any]:
        """Get performance statistics for a provider"""
        try:
            perf = self.provider_performance[provider]
            config = self.configs[provider]

            return {
                'provider': provider.value,
                'success_rate': perf['success_rate'],
                'avg_response_time': perf['avg_response_time'],
                'consecutive_failures': perf['consecutive_failures'],
                'throttle_factor': perf['throttle_factor'],
                'last_success': perf['last_success'],
                'requests_per_minute_limit': config.requests_per_minute,
                'requests_per_second_limit': config.requests_per_second,
                'current_batch_size': len(self.batch_queues[provider]),
                'is_healthy': perf['success_rate'] > 0.8 and perf['consecutive_failures'] < 5
            }

        except Exception as e:
            logger.error(f"Error getting provider stats: {e}")
            return {'provider': provider.value, 'error': str(e)}
    
    def _is_rate_limited(self, provider: APIProvider) -> bool:
        """Check if provider is currently rate limited"""
        config = self.configs[provider]
        now = time.time()
        
        # Check cooldown
        if now < self.cooldown_until[provider]:
            return True
        
        # Check burst limit
        if self.burst_counters[provider] >= config.burst_limit:
            return True
        
        # Check requests per minute
        history = self.request_history[provider]
        minute_ago = now - 60
        while history and history[0] < minute_ago:
            history.popleft()
        
        if len(history) >= config.requests_per_minute:
            return True
        
        # Check requests per second
        second_ago = now - 1
        recent_requests = sum(1 for ts in history if ts > second_ago)
        if recent_requests >= config.requests_per_second:
            return True
        
        return False
    
    def _calculate_delay(self, provider: APIProvider, attempt: int) -> float:
        """Calculate delay for retry with exponential backoff"""
        config = self.configs[provider]
        
        # Base delay with exponential backoff
        delay = config.base_delay * (2 ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        # Add jitter to prevent thundering herd
        if config.jitter:
            delay += random.uniform(0, delay * 0.1)
        
        return delay
    
    def _get_cache_key(self, provider: APIProvider, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate cache key for request"""
        # Sort params for consistent key generation
        sorted_params = json.dumps(params, sort_keys=True)
        return f"{provider.value}:{endpoint}:{sorted_params}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached response is still valid"""
        if cache_key not in self.cache:
            return False
        
        if cache_key not in self.cache_ttl:
            return False
        
        return time.time() < self.cache_ttl[cache_key]
    
    async def _get_session(self, provider: APIProvider) -> aiohttp.ClientSession:
        """Get or create HTTP session for provider"""
        if provider not in self.session_cache:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            connector = aiohttp.TCPConnector(limit=100, limit_per_host=20)
            self.session_cache[provider] = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={'User-Agent': 'A.T.L.A.S. Trading System v4.0'}
            )
        
        return self.session_cache[provider]
    
    async def _execute_request(self, request: APIRequest) -> Tuple[bool, Any]:
        """Execute API request with proper error handling"""
        provider = request.provider
        config = self.configs[provider]
        
        try:
            session = await self._get_session(provider)
            start_time = time.time()
            
            # Build full URL
            base_urls = {
                APIProvider.FMP: "https://financialmodelingprep.com/api/v3",
                APIProvider.ALPACA: "https://paper-api.alpaca.markets/v2",
                APIProvider.YFINANCE: "https://query1.finance.yahoo.com/v8",
                APIProvider.POLYGON: "https://api.polygon.io/v2",
                APIProvider.ALPHA_VANTAGE: "https://www.alphavantage.co/query"
            }
            
            url = f"{base_urls[provider]}/{request.endpoint.lstrip('/')}"
            
            async with session.get(url, params=request.params) as response:
                response_time = time.time() - start_time
                
                # Update metrics
                self.metrics['total_requests'] += 1
                self.metrics['average_response_time'] = (
                    (self.metrics['average_response_time'] * (self.metrics['total_requests'] - 1) + response_time) /
                    self.metrics['total_requests']
                )
                
                if response.status == 200:
                    data = await response.json()
                    self.metrics['successful_requests'] += 1
                    
                    # Cache successful response
                    cache_key = self._get_cache_key(provider, request.endpoint, request.params)
                    self.cache[cache_key] = data
                    self.cache_ttl[cache_key] = time.time() + self.default_cache_duration
                    
                    return True, data
                
                elif response.status == 429:  # Rate limited
                    self.metrics['rate_limited_requests'] += 1
                    logger.warning(f"Rate limited by {provider.value}: {response.status}")
                    
                    # Set cooldown
                    self.cooldown_until[provider] = time.time() + config.cooldown_seconds
                    return False, f"Rate limited: {response.status}"
                
                else:
                    self.metrics['failed_requests'] += 1
                    logger.error(f"API error from {provider.value}: {response.status}")
                    return False, f"API error: {response.status}"
        
        except asyncio.TimeoutError:
            self.metrics['failed_requests'] += 1
            logger.error(f"Timeout for {provider.value} request")
            return False, "Request timeout"
        
        except Exception as e:
            self.metrics['failed_requests'] += 1
            logger.error(f"Request error for {provider.value}: {e}")
            return False, str(e)
    
    async def _process_queue(self):
        """Process queued requests with rate limiting"""
        while self.processing:
            try:
                request = await self.request_queue.get()
                if not request:
                    await asyncio.sleep(0.1)
                    continue
                
                provider = request.provider
                config = self.configs[provider]
                
                # Check if we can process this request
                if self._is_rate_limited(provider):
                    # Put request back in queue with lower priority
                    request.priority = min(request.priority + 1, 3)
                    await self.request_queue.put(request)
                    await asyncio.sleep(0.5)
                    continue
                
                # Execute request
                success, result = await self._execute_request(request)
                
                if success:
                    # Update tracking
                    now = time.time()
                    self.request_history[provider].append(now)
                    self.burst_counters[provider] += 1
                    self.failed_requests[provider] = 0  # Reset failure count
                    
                    # Reset burst counter after burst window
                    if self.burst_counters[provider] >= config.burst_limit:
                        await asyncio.sleep(1.0)  # Brief pause after burst
                        self.burst_counters[provider] = 0
                
                else:
                    # Handle failure
                    self.failed_requests[provider] += 1
                    request.attempt += 1
                    
                    if request.attempt < config.retry_attempts:
                        # Retry with exponential backoff
                        delay = self._calculate_delay(provider, request.attempt)
                        logger.info(f"Retrying {provider.value} request in {delay:.2f}s (attempt {request.attempt})")
                        
                        await asyncio.sleep(delay)
                        await self.request_queue.put(request)
                    else:
                        logger.error(f"Max retries exceeded for {provider.value} request")
                
                # Small delay between requests
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in request processing: {e}")
                await asyncio.sleep(1.0)
    
    async def make_request(self, provider: APIProvider, endpoint: str, params: Dict[str, Any], 
                          priority: int = 1, cache_duration: Optional[int] = None) -> Optional[Any]:
        """Make rate-limited API request with caching"""
        
        # Check cache first
        cache_key = self._get_cache_key(provider, endpoint, params)
        if self._is_cache_valid(cache_key):
            self.metrics['cache_hits'] += 1
            logger.debug(f"Cache hit for {provider.value} request")
            return self.cache[cache_key]
        
        # Create request
        request = APIRequest(
            provider=provider,
            endpoint=endpoint,
            params=params,
            priority=priority
        )
        
        # Add to queue
        await self.request_queue.put(request)
        
        # Wait for processing (with timeout)
        timeout = 30.0  # 30 second timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]
            await asyncio.sleep(0.1)
        
        logger.warning(f"Request timeout for {provider.value}")
        return None
    
    async def batch_request(self, requests: List[Tuple[APIProvider, str, Dict[str, Any]]], 
                           priority: int = 2) -> List[Optional[Any]]:
        """Execute multiple requests efficiently"""
        
        # Add all requests to queue
        for provider, endpoint, params in requests:
            request = APIRequest(
                provider=provider,
                endpoint=endpoint,
                params=params,
                priority=priority
            )
            await self.request_queue.put(request)
        
        # Wait for all responses
        results = []
        timeout = 60.0  # 60 second timeout for batch
        start_time = time.time()
        
        for provider, endpoint, params in requests:
            cache_key = self._get_cache_key(provider, endpoint, params)
            
            # Wait for this specific response
            while time.time() - start_time < timeout:
                if self._is_cache_valid(cache_key):
                    results.append(self.cache[cache_key])
                    break
                await asyncio.sleep(0.1)
            else:
                results.append(None)  # Timeout
        
        return results
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self.metrics,
            'queue_size': asyncio.create_task(self.request_queue.size()),
            'cache_size': len(self.cache),
            'providers_in_cooldown': len([p for p, t in self.cooldown_until.items() if time.time() < t])
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.stop_processing()
        
        # Close all sessions
        for session in self.session_cache.values():
            await session.close()
        
        self.session_cache.clear()
        self.cache.clear()
        self.cache_ttl.clear()


# Global rate limiter instance
rate_limiter = AtlasRateLimiter()
