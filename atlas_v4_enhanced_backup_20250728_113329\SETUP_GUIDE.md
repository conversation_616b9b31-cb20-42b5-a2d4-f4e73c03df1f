# 🚀 A.T.L.A.S. v5.0 Setup Guide

This guide will help you set up A.T.L.A.S. v5.0 on your local machine for development or production use.

## 📋 Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: At least 2GB free space
- **Internet**: Stable connection for real-time market data

### Required API Keys

You'll need accounts and API keys from these services:

1. **Alpaca Markets** (Required)
   - Sign up at: https://alpaca.markets/
   - Create a paper trading account (free)
   - Generate API keys from the dashboard

2. **Financial Modeling Prep** (Required)
   - Sign up at: https://financialmodelingprep.com/
   - Free tier available (250 requests/day)
   - Get your API key from the dashboard

3. **Grok AI** (Optional but recommended)
   - Sign up at: https://x.ai/
   - Enhances AI responses and market analysis
   - Get API key from your account settings

## 🔧 Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/atlas-v5.git
cd atlas-v5
```

### 2. Create Virtual Environment

**Windows:**
```bash
python -m venv atlas_env
atlas_env\Scripts\activate
```

**macOS/Linux:**
```bash
python3 -m venv atlas_env
source atlas_env/bin/activate
```

### 3. Install Dependencies

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Configure Environment Variables

1. Copy the template file:
   ```bash
   cp .env.template .env
   ```

2. Edit `.env` with your API keys:
   ```bash
   # Trading APIs (Required)
   ALPACA_API_KEY=your_alpaca_api_key_here
   ALPACA_SECRET_KEY=your_alpaca_secret_key_here
   ALPACA_BASE_URL=https://paper-api.alpaca.markets  # Paper trading
   
   FMP_API_KEY=your_fmp_api_key_here
   
   # AI Enhancement (Optional)
   GROK_API_KEY=your_grok_api_key_here
   
   # System Configuration
   ENVIRONMENT=development
   DEBUG=true
   PAPER_TRADING=true
   ```

### 5. Verify Installation

Run the system health check:
```bash
python -c "
import sys
print(f'Python version: {sys.version}')
try:
    import fastapi, uvicorn, pandas, numpy
    print('✅ Core dependencies installed successfully')
except ImportError as e:
    print(f'❌ Missing dependency: {e}')
"
```

## 🚀 Starting the System

### Method 1: Production Server (Recommended)

```bash
python atlas_production_server.py
```

The system will start on http://localhost:8002

### Method 2: Development Mode

```bash
uvicorn atlas_production_server:app --reload --host 0.0.0.0 --port 8002
```

## 🌐 Accessing the Interface

### Web Interface
- Open your browser to: http://localhost:8002
- The interface will load with real-time market data
- Start with commands like: "I want to make $500 in 2 weeks"

### API Endpoints
- Health check: http://localhost:8002/api/v1/health
- Chat interface: POST to http://localhost:8002/api/v1/chat
- Trading positions: http://localhost:8002/api/v1/trading/positions

### WebSocket Connection
- Real-time updates: ws://localhost:8002/ws/scanner

## 🧪 Testing the Installation

### 1. Basic Functionality Test
```bash
python test_flexible_trading_targets.py
```

### 2. WebSocket Test
```bash
python test_websocket_connection.py
```

### 3. Full System Diagnostic
```bash
python atlas_connection_diagnostic.py
```

## 🔧 Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# Solution: Reinstall dependencies
pip install --force-reinstall -r requirements.txt
```

**2. API Key Errors**
- Verify your API keys are correct in `.env`
- Check that Alpaca account is set to paper trading
- Ensure FMP API key has sufficient quota

**3. Port Already in Use**
```bash
# Find process using port 8002
netstat -ano | findstr :8002  # Windows
lsof -i :8002  # macOS/Linux

# Kill the process or use a different port
python atlas_production_server.py --port 8003
```

**4. WebSocket Connection Issues**
- Check firewall settings
- Ensure no proxy blocking WebSocket connections
- Try accessing from localhost instead of external IP

### Performance Optimization

**1. For Better Performance:**
```bash
# Install optional performance packages
pip install uvloop  # Linux/macOS only
pip install orjson  # Faster JSON processing
```

**2. Memory Usage:**
- Close unused browser tabs
- Restart the system if memory usage grows
- Consider increasing system RAM for heavy usage

## 📊 Configuration Options

### Trading Settings (in .env)
```bash
# Risk Management
MAX_DAILY_LOSS=0.02  # 2% maximum daily loss
MAX_POSITION_SIZE=0.10  # 10% maximum position size
REQUIRE_STOP_LOSS=true

# Scanner Settings
MAX_SCAN_RESULTS=50
MIN_SIGNAL_STRENGTH=moderate
SCANNER_INTERVAL=60  # seconds
```

### AI Settings
```bash
# Grok AI Configuration
GROK_MODEL=grok-beta
GROK_MAX_TOKENS=4000
GROK_TEMPERATURE=0.7
```

## 🔒 Security Best Practices

1. **Never commit API keys to version control**
2. **Use paper trading initially** - Set `PAPER_TRADING=true`
3. **Keep dependencies updated** - Run `pip install --upgrade -r requirements.txt` regularly
4. **Monitor API usage** - Check your API quotas regularly
5. **Use strong passwords** for your trading accounts

## 📈 Next Steps

1. **Start with Paper Trading**: Test strategies without real money
2. **Learn the Interface**: Try different natural language commands
3. **Review Trading Plans**: Analyze generated strategies before executing
4. **Monitor Performance**: Use the built-in analytics and reporting
5. **Join the Community**: Participate in discussions and share feedback

## 🆘 Getting Help

- **Documentation**: Check the `/docs` folder for detailed guides
- **GitHub Issues**: Report bugs or request features
- **Community**: Join our Discord/Slack for real-time help
- **Email Support**: <EMAIL>

## 🎉 You're Ready!

Your A.T.L.A.S. v5.0 system is now configured and ready for trading. Start with simple commands like:

- "I want to make $200 in 1 week"
- "Scan the market for opportunities"
- "Show me my current positions"
- "What's AAPL trading at?"

Happy trading! 🚀📈
