#!/usr/bin/env python3
"""
Test script for A.T.L.A.S. Morning Briefing System
Demonstrates briefing generation and chat integration
"""

import asyncio
import logging
import sys

# Add current directory to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

async def test_morning_briefing():
    """Test the morning briefing system"""
    logger.info("🧪 Testing A.T.L.A.S. Morning Briefing System")
    logger.info("=" * 60)
    
    try:
        # Import the briefing system
        from atlas_morning_briefing import morning_briefing
        from atlas_chat_briefing_integration import chat_integration
        
        logger.info("✅ Successfully imported briefing systems")
        
        # Test 1: Generate morning briefing
        logger.info("\n🔍 Test 1: Generating morning briefing...")
        briefing_text = await morning_briefing.get_briefing_for_chat()
        
        print("\n" + "="*80)
        print("📊 MORNING BRIEFING OUTPUT:")
        print("="*80)
        print(briefing_text)
        print("="*80)
        
        # Test 2: Test chat message handling
        logger.info("\n🔍 Test 2: Testing chat message handling...")
        
        test_messages = [
            "show me the morning briefing",
            "what's the market doing today?",
            "any trade setups?",
            "market overview please",
            "hello there",  # Should not trigger briefing
            "give me market analysis"
        ]
        
        for message in test_messages:
            response = await chat_integration.process_user_message(message)
            if response:
                logger.info(f"✅ Message '{message}' triggered briefing")
            else:
                logger.info(f"ℹ️  Message '{message}' did not trigger briefing")
        
        # Test 3: Check system status
        logger.info("\n🔍 Test 3: Checking system status...")
        status = await chat_integration.get_status()
        
        logger.info("📊 System Status:")
        for key, value in status.items():
            logger.info(f"   {key}: {value}")
        
        logger.info("\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

async def demo_chat_interaction():
    """Demonstrate interactive chat functionality"""
    logger.info("\n🎭 Interactive Chat Demo")
    logger.info("Type messages to test briefing triggers (or 'quit' to exit)")
    logger.info("Try: 'morning briefing', 'market update', 'trade setups', etc.")
    
    try:
        from atlas_chat_briefing_integration import chat_integration
        
        while True:
            try:
                user_input = input("\n💬 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not user_input:
                    continue
                
                # Process the message
                response = await chat_integration.process_user_message(user_input)
                
                if response:
                    print(f"\n🤖 A.T.L.A.S.: {response[:200]}...")
                    print("   [Full briefing would be displayed in chat interface]")
                else:
                    print("\n🤖 A.T.L.A.S.: I didn't recognize that as a briefing request.")
                    print("   Try: 'morning briefing', 'market update', or 'trade setups'")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Error in chat demo: {e}")
        
        logger.info("👋 Chat demo ended")
        
    except Exception as e:
        logger.error(f"❌ Chat demo failed: {e}")

async def main():
    """Main test function"""
    print("🌅 A.T.L.A.S. Morning Briefing System Test")
    print("=" * 50)
    
    # Run basic tests
    success = await test_morning_briefing()
    
    if success:
        print("\n" + "="*50)
        print("🎯 INTEGRATION INSTRUCTIONS")
        print("="*50)
        print("""
To integrate the morning briefing into your chat system:

1. **Automatic Briefings at Market Open:**
   ```python
   from atlas_chat_briefing_integration import chat_integration
   
   # Start monitoring for market open
   await chat_integration.start()
   ```

2. **Handle User Requests in Chat:**
   ```python
   # In your chat message handler
   async def handle_user_message(message, user_id):
       # Check if it's a briefing request
       briefing_response = await chat_integration.process_user_message(message, user_id)
       
       if briefing_response:
           # Send briefing to user
           await send_to_chat(briefing_response, user_id)
       else:
           # Handle other message types
           await handle_other_messages(message, user_id)
   ```

3. **Manual Briefing Generation:**
   ```python
   from atlas_morning_briefing import morning_briefing
   
   # Get current briefing
   briefing_text = await morning_briefing.get_briefing_for_chat()
   await send_to_chat(briefing_text, user_id)
   ```

4. **Keywords that trigger briefings:**
   - "morning briefing", "market briefing", "market snapshot"
   - "trade setups", "market analysis", "market overview"
   - "what's the market doing?", "any trade ideas?"
   - "market update", "daily briefing"
        """)
        
        # Offer interactive demo
        demo_choice = input("\n🎭 Would you like to try the interactive chat demo? (y/n): ").strip().lower()
        if demo_choice in ['y', 'yes']:
            await demo_chat_interaction()
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(main())
