"""
A.T.L.A.S. Enhanced Intelligence Engine
Comprehensive web search and news capabilities leveraging Grok AI
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict, field

# A.T.L.A.S. imports
from models import EngineStatus
from atlas_grok_integration import (
    AtlasGrokIntegrationEngine, GrokRequest, GrokResponse,
    GrokTaskType, GrokCapability, MARKET_SEARCH_CONFIGS
)
# from atlas_ml_analytics import AtlasMLAnalytics  # Temporarily disabled due to import issues

logger = logging.getLogger(__name__)

@dataclass
class IntelligenceReport:
    """Comprehensive intelligence report structure"""
    symbol: str
    company_name: str
    report_timestamp: datetime
    report_type: str  # 'ceo_communications', 'social_sentiment', 'news_analysis', 'comprehensive'
    
    # Core intelligence data
    ceo_communications: List[Dict[str, Any]] = field(default_factory=list)
    social_sentiment: Dict[str, Any] = field(default_factory=dict)
    news_analysis: Dict[str, Any] = field(default_factory=dict)
    web_search_results: List[Dict[str, Any]] = field(default_factory=list)
    
    # Analysis results
    overall_sentiment: str = "neutral"
    sentiment_score: float = 0.0
    confidence: float = 0.0
    key_themes: List[str] = field(default_factory=list)
    market_impact_score: float = 0.0
    urgency_level: str = "normal"  # 'low', 'normal', 'high', 'critical'
    
    # Recommendations
    trading_recommendation: str = ""
    risk_assessment: str = ""
    next_catalysts: List[str] = field(default_factory=list)

# Enhanced search configurations for intelligence gathering
ENHANCED_SEARCH_CONFIGS = {
    "ceo_communications": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 20,
            "return_citations": True,
            "sources": [
                {
                    "type": "news",
                    "country": "US",
                    "allowed_websites": [
                        "sec.gov", "investor.gov", "bloomberg.com", "reuters.com",
                        "cnbc.com", "wsj.com", "marketwatch.com", "yahoo.com"
                    ],
                    "keywords": ["CEO", "earnings call", "executive", "announcement", "guidance"]
                },
                {
                    "type": "web",
                    "country": "US",
                    "allowed_websites": [
                        "sec.gov", "investor.apple.com", "ir.tesla.com", 
                        "microsoft.com/investor", "abc.xyz/investor"
                    ]
                }
            ]
        }
    },
    "executive_social": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 25,
            "return_citations": True,
            "sources": [
                {
                    "type": "x",
                    "included_x_handles": [
                        "tim_cook", "elonmusk", "satyanadella", "sundarpichai",
                        "ajassy", "jeffbezos", "warrenbuffett", "chamath"
                    ],
                    "post_favorite_count": 10,
                    "post_view_count": 100
                }
            ]
        }
    },
    "comprehensive_news": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 30,
            "return_citations": True,
            "sources": [
                {
                    "type": "news",
                    "country": "US",
                    "allowed_websites": [
                        "bloomberg.com", "reuters.com", "marketwatch.com",
                        "wsj.com", "cnbc.com", "yahoo.com", "barrons.com",
                        "fool.com", "seekingalpha.com", "benzinga.com"
                    ]
                },
                {
                    "type": "web",
                    "country": "US",
                    "allowed_websites": [
                        "sec.gov", "investor.gov", "federalreserve.gov"
                    ]
                }
            ]
        }
    },
    "internet_search": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 15,
            "return_citations": True,
            "sources": [
                {
                    "type": "web",
                    "country": "US",
                    "safe_search": False
                },
                {
                    "type": "news",
                    "country": "US",
                    "safe_search": False
                }
            ]
        }
    }
}

class AtlasEnhancedIntelligenceEngine:
    """Enhanced intelligence engine leveraging Grok AI for comprehensive market intelligence"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.grok_engine = None
        self.ml_analytics = None
        
        # Executive tracking data
        self.executive_profiles = {
            "AAPL": {"ceo": "Tim Cook", "twitter": "@tim_cook", "company": "Apple Inc."},
            "TSLA": {"ceo": "Elon Musk", "twitter": "@elonmusk", "company": "Tesla Inc."},
            "MSFT": {"ceo": "Satya Nadella", "twitter": "@satyanadella", "company": "Microsoft Corporation"},
            "GOOGL": {"ceo": "Sundar Pichai", "twitter": "@sundarpichai", "company": "Alphabet Inc."},
            "AMZN": {"ceo": "Andy Jassy", "twitter": "@ajassy", "company": "Amazon.com Inc."},
            "META": {"ceo": "Mark Zuckerberg", "twitter": "@zuck", "company": "Meta Platforms Inc."},
            "NVDA": {"ceo": "Jensen Huang", "twitter": "@jensenhuang", "company": "NVIDIA Corporation"},
            "BRK.B": {"ceo": "Warren Buffett", "twitter": "@warrenbuffett", "company": "Berkshire Hathaway"}
        }
        
    async def initialize(self) -> bool:
        """Initialize the enhanced intelligence engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize Grok integration engine
            self.grok_engine = AtlasGrokIntegrationEngine()
            success = await self.grok_engine.initialize()
            
            if not success:
                logger.error("❌ Failed to initialize Grok integration")
                self.status = EngineStatus.ERROR
                return False
            
            # Initialize ML analytics for sentiment analysis (temporarily disabled)
            self.ml_analytics = None
            # self.ml_analytics = AtlasMLAnalytics()
            # await self.ml_analytics.initialize()
            
            # Merge enhanced search configs with existing ones
            MARKET_SEARCH_CONFIGS.update(ENHANCED_SEARCH_CONFIGS)
            
            self.status = "RUNNING"  # EngineStatus.RUNNING
            logger.info("✅ Enhanced Intelligence Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Enhanced Intelligence Engine: {e}")
            self.status = "ERROR"  # EngineStatus.ERROR
            return False
    
    async def get_ceo_communications(self, symbol: str, days_back: int = 7) -> Dict[str, Any]:
        """Get CEO communications and executive announcements"""
        try:
            if symbol not in self.executive_profiles:
                return {"error": f"No executive profile found for {symbol}"}
            
            profile = self.executive_profiles[symbol]
            ceo_name = profile["ceo"]
            company_name = profile["company"]
            
            # Search for CEO communications
            query = f"Recent announcements, interviews, and communications from {ceo_name} CEO of {company_name} {symbol} in the last {days_back} days"
            
            result = await self.grok_engine.grok_client.make_live_search_request(
                query=query,
                search_config="ceo_communications",
                symbol=symbol
            )
            
            if result.success:
                # Analyze sentiment of CEO communications
                sentiment_analysis = await self._analyze_content_sentiment(result.content)
                
                return {
                    "symbol": symbol,
                    "ceo_name": ceo_name,
                    "company_name": company_name,
                    "communications": result.content,
                    "sources": result.citations,
                    "sentiment": sentiment_analysis,
                    "confidence": result.confidence,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": f"Failed to retrieve CEO communications: {result.error_message}"}
                
        except Exception as e:
            logger.error(f"Error getting CEO communications for {symbol}: {e}")
            return {"error": str(e)}
    
    async def monitor_executive_social_media(self, symbol: str) -> Dict[str, Any]:
        """Monitor social media activity from executives and financial influencers"""
        try:
            if symbol not in self.executive_profiles:
                return {"error": f"No executive profile found for {symbol}"}
            
            profile = self.executive_profiles[symbol]
            ceo_name = profile["ceo"]
            twitter_handle = profile.get("twitter", "")
            
            # Search for executive social media activity
            query = f"Recent tweets and social media posts from {ceo_name} {twitter_handle} about {symbol} and company updates"
            
            result = await self.grok_engine.grok_client.make_live_search_request(
                query=query,
                search_config="executive_social",
                symbol=symbol
            )
            
            if result.success:
                # Analyze social sentiment
                sentiment_analysis = await self._analyze_content_sentiment(result.content)
                
                return {
                    "symbol": symbol,
                    "executive": ceo_name,
                    "social_activity": result.content,
                    "sources": result.citations,
                    "sentiment": sentiment_analysis,
                    "confidence": result.confidence,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": f"Failed to retrieve social media data: {result.error_message}"}
                
        except Exception as e:
            logger.error(f"Error monitoring social media for {symbol}: {e}")
            return {"error": str(e)}
    
    async def search_internet(self, query: str, symbol: str = None) -> Dict[str, Any]:
        """Search the internet for trading-relevant information"""
        try:
            # Enhance query for trading relevance
            enhanced_query = f"Financial and trading information: {query}"
            if symbol:
                enhanced_query += f" related to {symbol} stock"
            
            result = await self.grok_engine.grok_client.make_live_search_request(
                query=enhanced_query,
                search_config="internet_search",
                symbol=symbol
            )
            
            if result.success:
                return {
                    "query": query,
                    "symbol": symbol,
                    "results": result.content,
                    "sources": result.citations,
                    "confidence": result.confidence,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": f"Internet search failed: {result.error_message}"}
                
        except Exception as e:
            logger.error(f"Error in internet search: {e}")
            return {"error": str(e)}
    
    async def get_comprehensive_news_analysis(self, symbol: str, days_back: int = 3) -> Dict[str, Any]:
        """Get comprehensive news analysis from multiple financial sources"""
        try:
            if symbol not in self.executive_profiles:
                company_name = f"{symbol} stock"
            else:
                company_name = self.executive_profiles[symbol]["company"]

            # Search for comprehensive news
            query = f"Latest financial news, analyst reports, earnings updates, and market analysis for {company_name} {symbol} in the last {days_back} days"

            result = await self.grok_engine.grok_client.make_live_search_request(
                query=query,
                search_config="comprehensive_news",
                symbol=symbol
            )

            if result.success:
                # Analyze news sentiment
                sentiment_analysis = await self._analyze_content_sentiment(result.content)

                return {
                    "symbol": symbol,
                    "company_name": company_name,
                    "news_analysis": result.content,
                    "sources": result.citations,
                    "sentiment": sentiment_analysis,
                    "confidence": result.confidence,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"error": f"Failed to retrieve news analysis: {result.error_message}"}

        except Exception as e:
            logger.error(f"Error getting news analysis for {symbol}: {e}")
            return {"error": str(e)}

    async def generate_comprehensive_intelligence_report(self, symbol: str, days_back: int = 7) -> IntelligenceReport:
        """Generate comprehensive multi-source intelligence report"""
        try:
            logger.info(f"🔍 Generating comprehensive intelligence report for {symbol}")

            # Get company information
            company_name = self.executive_profiles.get(symbol, {}).get("company", f"{symbol} Corporation")

            # Gather intelligence from all sources in parallel
            tasks = [
                self.get_ceo_communications(symbol, days_back),
                self.monitor_executive_social_media(symbol),
                self.get_comprehensive_news_analysis(symbol, days_back),
                self._get_market_sentiment_analysis(symbol)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)
            ceo_comms, social_data, news_data, market_sentiment = results

            # Process results and handle exceptions
            ceo_communications = ceo_comms if not isinstance(ceo_comms, Exception) else {}
            social_sentiment = social_data if not isinstance(social_data, Exception) else {}
            news_analysis = news_data if not isinstance(news_data, Exception) else {}
            market_data = market_sentiment if not isinstance(market_sentiment, Exception) else {}

            # Calculate overall sentiment and confidence
            overall_sentiment, sentiment_score, confidence = self._calculate_overall_sentiment([
                ceo_communications.get("sentiment", {}),
                social_sentiment.get("sentiment", {}),
                news_analysis.get("sentiment", {}),
                market_data.get("sentiment", {})
            ])

            # Extract key themes
            key_themes = self._extract_key_themes([
                ceo_communications.get("communications", ""),
                social_sentiment.get("social_activity", ""),
                news_analysis.get("news_analysis", "")
            ])

            # Calculate market impact score
            market_impact_score = self._calculate_market_impact_score(
                sentiment_score, confidence, len(key_themes)
            )

            # Determine urgency level
            urgency_level = self._determine_urgency_level(market_impact_score, sentiment_score)

            # Generate trading recommendation
            trading_recommendation = await self._generate_trading_recommendation(
                symbol, overall_sentiment, market_impact_score, key_themes
            )

            # Create comprehensive report
            report = IntelligenceReport(
                symbol=symbol,
                company_name=company_name,
                report_timestamp=datetime.now(),
                report_type="comprehensive",
                ceo_communications=[ceo_communications] if ceo_communications else [],
                social_sentiment=social_sentiment,
                news_analysis=news_analysis,
                overall_sentiment=overall_sentiment,
                sentiment_score=sentiment_score,
                confidence=confidence,
                key_themes=key_themes,
                market_impact_score=market_impact_score,
                urgency_level=urgency_level,
                trading_recommendation=trading_recommendation,
                risk_assessment=self._generate_risk_assessment(sentiment_score, confidence),
                next_catalysts=self._identify_next_catalysts(key_themes)
            )

            logger.info(f"✅ Generated comprehensive intelligence report for {symbol}")
            return report

        except Exception as e:
            logger.error(f"Error generating intelligence report for {symbol}: {e}")
            # Return error report
            return IntelligenceReport(
                symbol=symbol,
                company_name=company_name,
                report_timestamp=datetime.now(),
                report_type="error",
                trading_recommendation=f"Error generating report: {str(e)}"
            )

    async def _get_market_sentiment_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get market sentiment analysis using existing market analysis config"""
        try:
            query = f"Current market sentiment, trader discussions, and institutional activity for {symbol}"

            result = await self.grok_engine.grok_client.make_live_search_request(
                query=query,
                search_config="social_sentiment",  # Use existing config
                symbol=symbol
            )

            if result.success:
                sentiment_analysis = await self._analyze_content_sentiment(result.content)
                return {
                    "symbol": symbol,
                    "market_sentiment": result.content,
                    "sources": result.citations,
                    "sentiment": sentiment_analysis,
                    "confidence": result.confidence
                }
            else:
                return {"error": f"Failed to get market sentiment: {result.error_message}"}

        except Exception as e:
            logger.error(f"Error getting market sentiment for {symbol}: {e}")
            return {"error": str(e)}

    def _calculate_overall_sentiment(self, sentiment_data_list: List[Dict[str, Any]]) -> tuple:
        """Calculate overall sentiment from multiple sources"""
        try:
            valid_sentiments = []
            total_score = 0.0
            total_confidence = 0.0

            for sentiment_data in sentiment_data_list:
                if sentiment_data and "sentiment_score" in sentiment_data:
                    score = sentiment_data.get("sentiment_score", 0.0)
                    confidence = sentiment_data.get("confidence", 0.0)

                    if confidence > 0.3:  # Only include confident predictions
                        valid_sentiments.append(sentiment_data)
                        total_score += score * confidence  # Weight by confidence
                        total_confidence += confidence

            if not valid_sentiments:
                return "neutral", 0.0, 0.0

            # Calculate weighted average
            avg_score = total_score / total_confidence if total_confidence > 0 else 0.0
            avg_confidence = total_confidence / len(valid_sentiments)

            # Determine overall sentiment label
            if avg_score > 0.1:
                overall_sentiment = "positive"
            elif avg_score < -0.1:
                overall_sentiment = "negative"
            else:
                overall_sentiment = "neutral"

            return overall_sentiment, avg_score, avg_confidence

        except Exception as e:
            logger.error(f"Error calculating overall sentiment: {e}")
            return "neutral", 0.0, 0.0

    async def _analyze_content_sentiment(self, content: str) -> Dict[str, Any]:
        """Analyze sentiment of content using ML analytics"""
        try:
            if self.ml_analytics:
                return await self.ml_analytics.analyze_sentiment(content)
            else:
                # Fallback basic sentiment analysis
                return {
                    "sentiment": "neutral",
                    "sentiment_score": 0.0,
                    "confidence": 0.5,
                    "model": "fallback"
                }
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return {
                "sentiment": "neutral",
                "sentiment_score": 0.0,
                "confidence": 0.0,
                "model": "error",
                "error": str(e)
            }

    def _extract_key_themes(self, content_list: List[str]) -> List[str]:
        """Extract key themes from content using keyword analysis"""
        try:
            # Common financial keywords and themes
            theme_keywords = {
                "earnings": ["earnings", "revenue", "profit", "eps", "guidance"],
                "growth": ["growth", "expansion", "increase", "rising", "up"],
                "challenges": ["challenges", "decline", "down", "loss", "concern"],
                "innovation": ["innovation", "technology", "ai", "digital", "future"],
                "market": ["market", "competition", "share", "position"],
                "regulation": ["regulation", "compliance", "legal", "government"],
                "partnership": ["partnership", "acquisition", "merger", "deal"],
                "product": ["product", "launch", "release", "new", "update"]
            }

            themes = []
            combined_content = " ".join(content_list).lower()

            for theme, keywords in theme_keywords.items():
                if any(keyword in combined_content for keyword in keywords):
                    themes.append(theme)

            return themes[:5]  # Return top 5 themes

        except Exception as e:
            logger.error(f"Error extracting themes: {e}")
            return []

    def _calculate_market_impact_score(self, sentiment_score: float, confidence: float, theme_count: int) -> float:
        """Calculate market impact score based on sentiment and themes"""
        try:
            # Base score from sentiment
            base_score = abs(sentiment_score) * confidence

            # Theme multiplier (more themes = higher impact)
            theme_multiplier = 1.0 + (theme_count * 0.1)

            # Calculate final score (0-10 scale)
            impact_score = min(base_score * theme_multiplier * 10, 10.0)

            return round(impact_score, 2)

        except Exception as e:
            logger.error(f"Error calculating market impact: {e}")
            return 0.0

    def _determine_urgency_level(self, market_impact_score: float, sentiment_score: float) -> str:
        """Determine urgency level based on impact and sentiment"""
        try:
            if market_impact_score >= 8.0 or abs(sentiment_score) >= 0.8:
                return "critical"
            elif market_impact_score >= 6.0 or abs(sentiment_score) >= 0.5:
                return "high"
            elif market_impact_score >= 3.0 or abs(sentiment_score) >= 0.2:
                return "normal"
            else:
                return "low"

        except Exception as e:
            logger.error(f"Error determining urgency: {e}")
            return "normal"

    async def _generate_trading_recommendation(self, symbol: str, sentiment: str, impact_score: float, themes: List[str]) -> str:
        """Generate trading recommendation based on intelligence analysis"""
        try:
            # Use Grok to generate sophisticated trading recommendation
            analysis_prompt = f"""
            Based on comprehensive intelligence analysis for {symbol}:
            - Overall Sentiment: {sentiment}
            - Market Impact Score: {impact_score}/10
            - Key Themes: {', '.join(themes)}

            Provide a concise trading recommendation considering:
            1. Risk-reward profile
            2. Entry/exit strategies
            3. Position sizing suggestions
            4. Key catalysts to watch

            Keep response under 200 words and focus on actionable insights.
            """

            request = GrokRequest(
                task_type=GrokTaskType.TRADING_ANALYSIS,
                capability=GrokCapability.REASONING,
                prompt=analysis_prompt,
                temperature=0.3
            )

            response = await self.grok_engine.grok_client.make_request(request)

            if response.success:
                return response.content
            else:
                # Fallback recommendation
                return self._generate_fallback_recommendation(sentiment, impact_score, themes)

        except Exception as e:
            logger.error(f"Error generating trading recommendation: {e}")
            return self._generate_fallback_recommendation(sentiment, impact_score, themes)

    def _generate_fallback_recommendation(self, sentiment: str, impact_score: float, themes: List[str]) -> str:
        """Generate fallback trading recommendation"""
        theme_context = f" Key themes: {', '.join(themes)}" if themes else ""

        if sentiment == "positive" and impact_score >= 6.0:
            return f"BULLISH: Strong positive sentiment with high impact ({impact_score}/10).{theme_context} Consider long positions with appropriate risk management."
        elif sentiment == "negative" and impact_score >= 6.0:
            return f"BEARISH: Strong negative sentiment with high impact ({impact_score}/10).{theme_context} Consider defensive positioning or short opportunities."
        else:
            return f"NEUTRAL: Mixed or low-impact signals ({impact_score}/10).{theme_context} Monitor for clearer directional catalysts."

    def _generate_risk_assessment(self, sentiment_score: float, confidence: float) -> str:
        """Generate risk assessment based on sentiment analysis"""
        if confidence < 0.5:
            return "HIGH RISK: Low confidence in sentiment analysis. Exercise caution."
        elif abs(sentiment_score) >= 0.7:
            return "MEDIUM RISK: Strong sentiment signals detected. Monitor for volatility."
        else:
            return "LOW RISK: Stable sentiment environment with moderate signals."

    def _identify_next_catalysts(self, themes: List[str]) -> List[str]:
        """Identify potential next catalysts based on themes"""
        catalyst_map = {
            "earnings": "Next earnings announcement",
            "product": "Product launch or update",
            "regulation": "Regulatory decision or announcement",
            "partnership": "Partnership or M&A activity",
            "innovation": "Technology or innovation showcase",
            "market": "Market share or competitive developments"
        }

        catalysts = []
        for theme in themes:
            if theme in catalyst_map:
                catalysts.append(catalyst_map[theme])

        # Add generic catalysts if none found
        if not catalysts:
            catalysts = ["Earnings announcement", "Market developments", "Industry news"]

        return catalysts[:3]  # Return top 3 catalysts
