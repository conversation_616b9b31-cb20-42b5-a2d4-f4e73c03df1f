"""
A.T.L.A.S. Grok API Integration Module
Advanced integration of xAI's Grok 4 model for enhanced AI capabilities
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple, Literal
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import base64

# Core imports
from models import EngineStatus
from config import get_api_config

# HTTP client for API calls
try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

# Pydantic for structured outputs
try:
    from pydantic import BaseModel, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    BaseModel = None
    Field = None

logger = logging.getLogger(__name__)

# ============================================================================
# GROK INTEGRATION ENUMS AND MODELS
# ============================================================================

class GrokCapability(Enum):
    """Grok AI capabilities"""
    REASONING = "reasoning"
    VISION = "vision"
    MULTIMODAL = "multimodal"
    CODE_GENERATION = "code_generation"
    REAL_TIME_SEARCH = "real_time_search"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    LOGICAL_INFERENCE = "logical_inference"
    PATTERN_RECOGNITION = "pattern_recognition"

class GrokTaskType(Enum):
    """Types of tasks Grok can perform"""
    CAUSAL_ANALYSIS = "causal_analysis"
    MARKET_PSYCHOLOGY = "market_psychology"
    IMAGE_ANALYSIS = "image_analysis"
    DATA_FUSION = "data_fusion"
    CODE_OPTIMIZATION = "code_optimization"
    REAL_TIME_SENTIMENT = "real_time_sentiment"
    LOGICAL_REASONING = "logical_reasoning"
    PATTERN_DETECTION = "pattern_detection"

    # Multi-Agent specific task types
    AGENT_COORDINATION = "agent_coordination"
    CROSS_VALIDATION = "cross_validation"
    CONSENSUS_BUILDING = "consensus_building"
    CONFLICT_RESOLUTION = "conflict_resolution"
    MULTI_AGENT_SYNTHESIS = "multi_agent_synthesis"

@dataclass
class GrokRequest:
    """Enhanced Grok API request structure with advanced capabilities"""
    task_type: GrokTaskType
    capability: GrokCapability
    prompt: str
    context: Dict[str, Any] = field(default_factory=dict)
    temperature: float = 0.2
    max_tokens: Optional[int] = None
    image_data: Optional[bytes] = None
    system_prompt: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Advanced features
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[Union[str, Dict[str, Any]]] = "auto"
    parallel_function_calling: bool = True
    structured_output_schema: Optional[Dict[str, Any]] = None
    search_parameters: Optional[Dict[str, Any]] = None
    reasoning_effort: Optional[Literal["low", "high"]] = None
    multiple_images: Optional[List[bytes]] = None
    image_detail: Literal["auto", "low", "high"] = "auto"

@dataclass
class GrokResponse:
    """Enhanced Grok API response structure"""
    success: bool
    content: str
    confidence: float
    task_type: GrokTaskType
    capability: GrokCapability
    processing_time: float
    tokens_used: Optional[int] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    fallback_used: bool = False

    # Advanced response features
    tool_calls: Optional[List[Dict[str, Any]]] = None
    structured_output: Optional[Dict[str, Any]] = None
    reasoning_content: Optional[str] = None
    citations: Optional[List[str]] = None
    search_sources_used: Optional[int] = None
    function_call_results: Optional[List[Dict[str, Any]]] = None

@dataclass
class GrokAnalysisResult:
    """Enhanced analysis result with Grok insights"""
    original_result: Dict[str, Any]
    grok_enhancement: GrokResponse
    combined_confidence: float
    improvement_metrics: Dict[str, float]
    reasoning_chain: List[str]
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class MultiAgentGrokRequest:
    """Multi-agent specific Grok request structure"""
    agent_id: str
    agent_role: str
    task_type: GrokTaskType
    capability: GrokCapability
    prompt: str
    agent_context: Dict[str, Any] = field(default_factory=dict)
    other_agent_outputs: Dict[str, Any] = field(default_factory=dict)
    coordination_mode: str = "collaborative"  # collaborative, competitive, consensus
    priority_level: int = 1  # 1-5, higher is more priority
    requires_consensus: bool = False
    temperature: float = 0.2
    max_tokens: Optional[int] = None

@dataclass
class MultiAgentGrokResponse:
    """Multi-agent specific Grok response structure"""
    agent_id: str
    agent_role: str
    success: bool
    content: str
    confidence: float
    task_type: GrokTaskType
    capability: GrokCapability
    processing_time: float
    consensus_score: Optional[float] = None
    conflict_indicators: List[str] = field(default_factory=list)
    coordination_suggestions: List[str] = field(default_factory=list)
    tokens_used: Optional[int] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

# ============================================================================
# STRUCTURED OUTPUT MODELS (PYDANTIC)
# ============================================================================

if PYDANTIC_AVAILABLE:
    class TradingSignal(BaseModel):
        """Structured trading signal output"""
        symbol: str = Field(description="Stock symbol (e.g., AAPL, TSLA)")
        action: Literal["BUY", "SELL", "HOLD"] = Field(description="Recommended trading action")
        confidence: float = Field(ge=0.0, le=1.0, description="Confidence score between 0 and 1")
        target_price: Optional[float] = Field(None, description="Target price for the trade")
        stop_loss: Optional[float] = Field(None, description="Stop loss price")
        reasoning: str = Field(description="Detailed reasoning for the trading signal")
        risk_level: Literal["LOW", "MEDIUM", "HIGH"] = Field(description="Risk assessment")
        time_horizon: Literal["INTRADAY", "SHORT_TERM", "MEDIUM_TERM", "LONG_TERM"] = Field(description="Expected time horizon")
        entry_price: Optional[float] = Field(None, description="Recommended entry price")
        position_size: Optional[float] = Field(None, description="Recommended position size as percentage of portfolio")

    class MarketSentiment(BaseModel):
        """Market sentiment analysis structure"""
        overall_sentiment: Literal["BULLISH", "BEARISH", "NEUTRAL"] = Field(description="Overall market sentiment")
        sentiment_score: float = Field(ge=-1.0, le=1.0, description="Sentiment score from -1 (bearish) to 1 (bullish)")
        key_factors: List[str] = Field(description="Key factors influencing sentiment")
        news_impact: Literal["POSITIVE", "NEGATIVE", "NEUTRAL"] = Field(description="Impact of recent news")
        social_sentiment: Optional[float] = Field(None, ge=-1.0, le=1.0, description="Social media sentiment score")

    class TechnicalAnalysis(BaseModel):
        """Technical analysis structured output"""
        symbol: str = Field(description="Stock symbol")
        trend: Literal["UPTREND", "DOWNTREND", "SIDEWAYS"] = Field(description="Current price trend")
        support_levels: List[float] = Field(description="Key support price levels")
        resistance_levels: List[float] = Field(description="Key resistance price levels")
        rsi: Optional[float] = Field(None, ge=0.0, le=100.0, description="RSI indicator value")
        macd_signal: Literal["BUY", "SELL", "NEUTRAL"] = Field(description="MACD signal")
        volume_analysis: str = Field(description="Volume pattern analysis")
        pattern_detected: Optional[str] = Field(None, description="Chart pattern if detected")

    class MarketAnalysis(BaseModel):
        """Comprehensive market analysis structure"""
        timestamp: datetime = Field(description="Analysis timestamp")
        market_conditions: str = Field(description="Current market conditions description")
        overall_sentiment: MarketSentiment = Field(description="Market sentiment analysis")
        signals: List[TradingSignal] = Field(description="Trading signals generated")
        technical_analysis: Optional[TechnicalAnalysis] = Field(None, description="Technical analysis if requested")
        risk_assessment: str = Field(description="Overall risk assessment")
        confidence: float = Field(ge=0.0, le=1.0, description="Overall analysis confidence")
        data_sources: List[str] = Field(description="Data sources used in analysis")

    class NewsAnalysis(BaseModel):
        """News impact analysis structure"""
        headline: str = Field(description="News headline")
        summary: str = Field(description="News summary")
        sentiment: Literal["POSITIVE", "NEGATIVE", "NEUTRAL"] = Field(description="News sentiment")
        impact_score: float = Field(ge=0.0, le=1.0, description="Expected market impact score")
        affected_symbols: List[str] = Field(description="Stock symbols likely to be affected")
        category: str = Field(description="News category (earnings, merger, regulatory, etc.)")
        urgency: Literal["LOW", "MEDIUM", "HIGH"] = Field(description="News urgency level")
        source_credibility: float = Field(ge=0.0, le=1.0, description="Source credibility score")

else:
    # Fallback classes when Pydantic is not available
    TradingSignal = dict
    MarketSentiment = dict
    TechnicalAnalysis = dict
    MarketAnalysis = dict
    NewsAnalysis = dict

# ============================================================================
# TRADING FUNCTION DEFINITIONS FOR FUNCTION CALLING
# ============================================================================

ATLAS_TRADING_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "execute_trade",
            "description": "Execute buy/sell orders through A.T.L.A.S. trading system",
            "parameters": {
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, TSLA, SPY)"
                    },
                    "action": {
                        "type": "string",
                        "enum": ["buy", "sell"],
                        "description": "Trading action to execute"
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "Number of shares to trade",
                        "minimum": 1
                    },
                    "order_type": {
                        "type": "string",
                        "enum": ["market", "limit"],
                        "description": "Order type"
                    },
                    "limit_price": {
                        "type": "number",
                        "description": "Limit price (required for limit orders)"
                    }
                },
                "required": ["symbol", "action", "quantity", "order_type"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_market_data",
            "description": "Retrieve real-time market data and analysis",
            "parameters": {
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol to analyze"
                    },
                    "data_type": {
                        "type": "string",
                        "enum": ["price", "volume", "technical", "news", "options", "fundamentals"],
                        "description": "Type of market data to retrieve"
                    },
                    "timeframe": {
                        "type": "string",
                        "enum": ["1m", "5m", "15m", "1h", "1d", "1w"],
                        "description": "Data timeframe"
                    }
                },
                "required": ["symbol", "data_type"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "analyze_portfolio",
            "description": "Analyze current portfolio performance and risk",
            "parameters": {
                "type": "object",
                "properties": {
                    "analysis_type": {
                        "type": "string",
                        "enum": ["performance", "risk", "allocation", "rebalancing"],
                        "description": "Type of portfolio analysis"
                    },
                    "timeframe": {
                        "type": "string",
                        "enum": ["1d", "1w", "1m", "3m", "6m", "1y"],
                        "description": "Analysis timeframe"
                    }
                },
                "required": ["analysis_type"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "scan_market_opportunities",
            "description": "Scan market for trading opportunities using A.T.L.A.S. scanners",
            "parameters": {
                "type": "object",
                "properties": {
                    "scanner_type": {
                        "type": "string",
                        "enum": ["lee_method", "ttm_squeeze", "momentum", "breakout", "reversal"],
                        "description": "Type of market scanner to use"
                    },
                    "market_cap": {
                        "type": "string",
                        "enum": ["small", "mid", "large", "all"],
                        "description": "Market cap filter"
                    },
                    "sector": {
                        "type": "string",
                        "description": "Sector filter (optional)"
                    }
                },
                "required": ["scanner_type"]
            }
        }
    }
]

# ============================================================================
# MARKET-FOCUSED SEARCH CONFIGURATIONS
# ============================================================================

MARKET_SEARCH_CONFIGS = {
    "real_time_news": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 15,
            "return_citations": True,
            "sources": [
                {
                    "type": "news",
                    "country": "US",
                    "safe_search": False,
                    "excluded_websites": ["reddit.com", "stocktwits.com"]
                },
                {
                    "type": "web",
                    "country": "US",
                    "allowed_websites": [
                        "bloomberg.com", "reuters.com", "marketwatch.com",
                        "yahoo.com", "sec.gov", "cnbc.com", "wsj.com"
                    ]
                }
            ]
        }
    },
    "social_sentiment": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 20,
            "return_citations": True,
            "sources": [
                {
                    "type": "x",
                    "included_x_handles": [
                        "DeItaone", "zerohedge", "business", "markets",
                        "BloombergTV", "CNBC", "MarketWatch", "YahooFinance"
                    ],
                    "post_favorite_count": 50,
                    "post_view_count": 500
                }
            ]
        }
    },
    "earnings_news": {
        "search_parameters": {
            "mode": "on",
            "max_search_results": 10,
            "return_citations": True,
            "sources": [
                {
                    "type": "rss",
                    "links": ["https://feeds.bloomberg.com/markets/news.rss"]
                },
                {
                    "type": "news",
                    "country": "US",
                    "allowed_websites": ["sec.gov", "investor.gov"]
                }
            ]
        }
    },
    "market_analysis": {
        "search_parameters": {
            "mode": "auto",
            "max_search_results": 25,
            "return_citations": True,
            "sources": [
                {
                    "type": "web",
                    "country": "US",
                    "allowed_websites": [
                        "bloomberg.com", "reuters.com", "marketwatch.com",
                        "yahoo.com", "cnbc.com", "wsj.com", "barrons.com"
                    ]
                },
                {
                    "type": "news",
                    "country": "US",
                    "safe_search": False
                },
                {
                    "type": "x",
                    "included_x_handles": ["DeItaone", "zerohedge", "business"],
                    "post_favorite_count": 100
                }
            ]
        }
    }
}

# ============================================================================
# GROK API CLIENT
# ============================================================================

class GrokAPIClient:
    """Advanced Grok API client with comprehensive error handling"""
    
    def __init__(self):
        self.config = get_api_config("grok")
        self.client = None
        self.status = EngineStatus.INITIALIZING
        self.request_count = 0
        self.error_count = 0
        self.last_request_time = None
        self.rate_limit_remaining = 1000
        self.rate_limit_reset = None
        
        # Performance tracking
        self.response_times = []
        self.success_rate = 1.0
        
        logger.info("[GROK] Grok API Client initialized")

    async def initialize(self):
        """Initialize Grok API client"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            if not HTTPX_AVAILABLE:
                logger.warning("[GROK] httpx not available - Grok integration disabled")
                self.status = EngineStatus.FAILED
                return False
            
            if not self.config.get("available", False):
                logger.warning("[GROK] Grok API not configured - running in fallback mode")
                self.status = EngineStatus.ACTIVE  # Allow fallback operation
                return False
            
            # Initialize HTTP client
            self.client = httpx.AsyncClient(
                base_url=self.config.get("base_url", "https://api.x.ai/v1"),
                headers={
                    "Authorization": f"Bearer {self.config.get('api_key')}",
                    "Content-Type": "application/json",
                    "User-Agent": "ATLAS-v5.0-Trading-System"
                },
                timeout=30.0
            )
            
            # Test connection
            test_success = await self._test_connection()
            
            if test_success:
                self.status = EngineStatus.ACTIVE
                logger.info("[OK] Grok API Client fully initialized")
                return True
            else:
                self.status = EngineStatus.FAILED
                logger.error("[ERROR] Grok API connection test failed")
                return False
                
        except Exception as e:
            logger.error(f"Grok API client initialization failed: {e}")
            self.status = EngineStatus.FAILED
            return False

    async def _test_connection(self) -> bool:
        """Test Grok API connection"""
        try:
            if not self.client:
                return False
            
            test_payload = {
                "messages": [{"role": "user", "content": "Test connection"}],
                "model": self.config.get("model", "grok-3-latest"),
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            response = await self.client.post("/chat/completions", json=test_payload)
            
            if response.status_code == 200:
                logger.info("[OK] Grok API connection test successful")
                return True
            else:
                logger.error(f"Grok API test failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Grok API connection test error: {e}")
            return False

    async def make_request(self, request: GrokRequest) -> GrokResponse:
        """Make enhanced request to Grok API"""
        start_time = time.time()
        
        try:
            if not self.client or self.status != EngineStatus.ACTIVE:
                return self._create_fallback_response(request, "Grok API not available")
            
            # Check rate limits
            if not await self._check_rate_limits():
                return self._create_fallback_response(request, "Rate limit exceeded")
            
            # Prepare request payload
            payload = await self._prepare_payload(request)
            
            # Make API call
            response = await self.client.post("/chat/completions", json=payload)
            
            # Update metrics
            processing_time = time.time() - start_time
            self.response_times.append(processing_time)
            self.request_count += 1
            self.last_request_time = datetime.now()
            
            # Process response
            if response.status_code == 200:
                return await self._process_success_response(response, request, processing_time)
            else:
                self.error_count += 1
                return await self._process_error_response(response, request, processing_time)
                
        except Exception as e:
            self.error_count += 1
            processing_time = time.time() - start_time
            logger.error(f"Grok API request failed: {e}")
            return self._create_error_response(request, str(e), processing_time)

    async def _prepare_payload(self, request: GrokRequest) -> Dict[str, Any]:
        """Enhanced payload preparation with advanced features"""
        messages = []

        # Add system prompt if provided
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})

        # Handle multimodal requests (single or multiple images)
        if request.capability == GrokCapability.VISION:
            content = [{"type": "text", "text": request.prompt}]

            # Handle multiple images
            if request.multiple_images:
                for image_data in request.multiple_images:
                    image_b64 = base64.b64encode(image_data).decode('utf-8')
                    content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_b64}",
                            "detail": request.image_detail
                        }
                    })
            # Handle single image (backward compatibility)
            elif request.image_data:
                image_b64 = base64.b64encode(request.image_data).decode('utf-8')
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_b64}",
                        "detail": request.image_detail
                    }
                })

            messages.append({"role": "user", "content": content})
        else:
            messages.append({"role": "user", "content": request.prompt})

        # Determine model based on capability and reasoning
        model = self._select_optimal_model(request)

        payload = {
            "messages": messages,
            "model": model,
            "temperature": request.temperature,
            "stream": False
        }

        # Add optional parameters
        if request.max_tokens:
            payload["max_tokens"] = request.max_tokens

        # Function calling support
        if request.tools:
            payload["tools"] = request.tools
            payload["tool_choice"] = request.tool_choice
            payload["parallel_function_calling"] = request.parallel_function_calling

        # Structured outputs support
        if request.structured_output_schema:
            payload["response_format"] = {
                "type": "json_schema",
                "json_schema": request.structured_output_schema
            }

        # Live search support
        if request.search_parameters:
            payload.update(request.search_parameters)

        # Reasoning effort (for reasoning models)
        if request.reasoning_effort and "reasoning" in model.lower():
            payload["reasoning_effort"] = request.reasoning_effort

        return payload

    def _select_optimal_model(self, request: GrokRequest) -> str:
        """Select optimal model based on request capabilities"""
        base_model = self.config.get("model", "grok-3-latest")

        # Use reasoning models for logical tasks
        if (request.capability == GrokCapability.REASONING or
            request.task_type == GrokTaskType.LOGICAL_REASONING):
            if request.reasoning_effort == "high":
                return "grok-3-mini"  # Better for complex reasoning
            else:
                return "grok-3-mini-fast"  # Faster for simple reasoning

        # Use vision models for image analysis
        elif request.capability == GrokCapability.VISION:
            return "grok-2-vision-1212"

        # Use latest model for general tasks
        else:
            return base_model

    async def _check_rate_limits(self) -> bool:
        """Check if we can make a request within rate limits"""
        try:
            if self.rate_limit_reset and datetime.now() > self.rate_limit_reset:
                self.rate_limit_remaining = 1000  # Reset limit
                self.rate_limit_reset = None

            return self.rate_limit_remaining > 0

        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return True  # Allow request if check fails

    async def _process_success_response(self, response, request: GrokRequest, processing_time: float) -> GrokResponse:
        """Enhanced processing of successful API response with advanced features"""
        try:
            data = response.json()

            # Update rate limit info
            if 'x-ratelimit-remaining' in response.headers:
                self.rate_limit_remaining = int(response.headers['x-ratelimit-remaining'])
            if 'x-ratelimit-reset' in response.headers:
                self.rate_limit_reset = datetime.fromtimestamp(int(response.headers['x-ratelimit-reset']))

            # Extract basic response data
            content = ""
            tokens_used = None
            tool_calls = None
            reasoning_content = None
            citations = None
            search_sources_used = None
            structured_output = None

            if data.get("choices") and len(data["choices"]) > 0:
                choice = data["choices"][0]
                message = choice.get("message", {})

                # Extract content
                content = message.get("content", "")

                # Extract tool calls
                if message.get("tool_calls"):
                    tool_calls = message["tool_calls"]

                # Extract reasoning content (for reasoning models)
                if message.get("reasoning_content"):
                    reasoning_content = message["reasoning_content"]

            # Extract usage information
            if data.get("usage"):
                usage = data["usage"]
                tokens_used = usage.get("total_tokens")
                search_sources_used = usage.get("num_sources_used")

            # Extract citations (for live search)
            if data.get("citations"):
                citations = data["citations"]

            # Extract structured output
            if request.structured_output_schema and content:
                try:
                    structured_output = json.loads(content)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse structured output as JSON")

            # Calculate confidence based on response quality
            confidence = self._calculate_response_confidence(content, request)

            return GrokResponse(
                success=True,
                content=content,
                confidence=confidence,
                task_type=request.task_type,
                capability=request.capability,
                processing_time=processing_time,
                tokens_used=tokens_used,
                tool_calls=tool_calls,
                structured_output=structured_output,
                reasoning_content=reasoning_content,
                citations=citations,
                search_sources_used=search_sources_used,
                metadata={
                    'model': self._select_optimal_model(request),
                    'temperature': request.temperature,
                    'request_id': data.get("id", "unknown"),
                    'has_tool_calls': tool_calls is not None,
                    'has_reasoning': reasoning_content is not None,
                    'has_citations': citations is not None,
                    'search_enabled': request.search_parameters is not None
                }
            )

        except Exception as e:
            logger.error(f"Response processing failed: {e}")
            return self._create_error_response(request, f"Response processing error: {str(e)}", processing_time)

    async def _process_error_response(self, response, request: GrokRequest, processing_time: float) -> GrokResponse:
        """Process error API response"""
        try:
            error_data = response.json() if response.content else {}
            error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")

            logger.error(f"Grok API error {response.status_code}: {error_message}")

            return GrokResponse(
                success=False,
                content="",
                confidence=0.0,
                task_type=request.task_type,
                capability=request.capability,
                processing_time=processing_time,
                error_message=error_message,
                metadata={'status_code': response.status_code}
            )

        except Exception as e:
            return self._create_error_response(request, f"Error response processing failed: {str(e)}", processing_time)

    def _create_fallback_response(self, request: GrokRequest, reason: str) -> GrokResponse:
        """Create fallback response when Grok API is unavailable"""
        return GrokResponse(
            success=False,
            content="",
            confidence=0.0,
            task_type=request.task_type,
            capability=request.capability,
            processing_time=0.0,
            error_message=reason,
            fallback_used=True,
            metadata={'fallback_reason': reason}
        )

    def _create_error_response(self, request: GrokRequest, error: str, processing_time: float) -> GrokResponse:
        """Create error response"""
        return GrokResponse(
            success=False,
            content="",
            confidence=0.0,
            task_type=request.task_type,
            capability=request.capability,
            processing_time=processing_time,
            error_message=error,
            metadata={'error_type': 'client_error'}
        )

    def _calculate_response_confidence(self, content: str, request: GrokRequest) -> float:
        """Calculate confidence score for response"""
        try:
            base_confidence = 0.8

            # Adjust based on content length and quality
            if len(content) < 10:
                base_confidence *= 0.5
            elif len(content) > 100:
                base_confidence *= 1.1

            # Adjust based on task type
            task_confidence_multipliers = {
                GrokTaskType.CAUSAL_ANALYSIS: 0.9,
                GrokTaskType.MARKET_PSYCHOLOGY: 0.85,
                GrokTaskType.IMAGE_ANALYSIS: 0.8,
                GrokTaskType.CODE_OPTIMIZATION: 0.95,
                GrokTaskType.REAL_TIME_SENTIMENT: 0.75,
                GrokTaskType.LOGICAL_REASONING: 0.9
            }

            multiplier = task_confidence_multipliers.get(request.task_type, 0.8)
            base_confidence *= multiplier

            return min(base_confidence, 1.0)

        except Exception as e:
            logger.error(f"Confidence calculation failed: {e}")
            return 0.5

    def get_client_status(self) -> Dict[str, Any]:
        """Get Grok API client status"""
        avg_response_time = sum(self.response_times[-100:]) / len(self.response_times[-100:]) if self.response_times else 0
        self.success_rate = (self.request_count - self.error_count) / max(self.request_count, 1)

        return {
            'status': self.status.value,
            'available': self.status == EngineStatus.ACTIVE,
            'request_count': self.request_count,
            'error_count': self.error_count,
            'success_rate': self.success_rate,
            'avg_response_time': avg_response_time,
            'rate_limit_remaining': self.rate_limit_remaining,
            'last_request': self.last_request_time.isoformat() if self.last_request_time else None
        }

    # ============================================================================
    # ADVANCED FEATURE METHODS
    # ============================================================================

    async def make_live_search_request(self, query: str, search_config: str = "market_analysis",
                                     symbol: Optional[str] = None) -> GrokResponse:
        """Make a request with live search capabilities for market data"""

        # Get search configuration
        search_params = MARKET_SEARCH_CONFIGS.get(search_config, MARKET_SEARCH_CONFIGS["market_analysis"])

        # Customize search for specific symbol
        if symbol:
            query = f"Latest news and analysis for {symbol}: {query}"

            # Add symbol-specific search terms
            if search_params.get("search_parameters", {}).get("sources"):
                for source in search_params["search_parameters"]["sources"]:
                    if source.get("type") == "x":
                        # Add symbol to X search context
                        source["symbol_context"] = symbol

        request = GrokRequest(
            task_type=GrokTaskType.REAL_TIME_SENTIMENT,
            capability=GrokCapability.REAL_TIME_SEARCH,
            prompt=query,
            search_parameters=search_params,
            temperature=0.1,
            max_tokens=2000
        )

        return await self.make_request(request)

    async def make_structured_analysis_request(self, symbol: str, analysis_type: str = "trading_signal") -> GrokResponse:
        """Make a request with structured output for trading analysis"""

        # Define schema based on analysis type
        schema = None
        if analysis_type == "trading_signal" and PYDANTIC_AVAILABLE:
            schema = {
                "name": "TradingSignalSchema",
                "schema": TradingSignal.model_json_schema()
            }
        elif analysis_type == "market_analysis" and PYDANTIC_AVAILABLE:
            schema = {
                "name": "MarketAnalysisSchema",
                "schema": MarketAnalysis.model_json_schema()
            }

        prompt = f"""
        Analyze {symbol} and provide a comprehensive {analysis_type.replace('_', ' ')}.
        Consider current market conditions, technical indicators, news sentiment, and risk factors.
        Provide specific, actionable recommendations with clear reasoning.
        """

        request = GrokRequest(
            task_type=GrokTaskType.PATTERN_DETECTION,
            capability=GrokCapability.REASONING,
            prompt=prompt,
            structured_output_schema=schema,
            search_parameters=MARKET_SEARCH_CONFIGS["market_analysis"],
            temperature=0.2,
            max_tokens=1500
        )

        return await self.make_request(request)

    async def make_function_calling_request(self, query: str, available_tools: List[str] = None) -> GrokResponse:
        """Make a request with function calling capabilities"""

        # Select tools based on request
        if available_tools is None:
            tools = ATLAS_TRADING_TOOLS  # Use all available tools
        else:
            tools = [tool for tool in ATLAS_TRADING_TOOLS
                    if tool["function"]["name"] in available_tools]

        request = GrokRequest(
            task_type=GrokTaskType.LOGICAL_REASONING,
            capability=GrokCapability.REASONING,
            prompt=query,
            tools=tools,
            tool_choice="auto",
            parallel_function_calling=True,
            temperature=0.1,
            max_tokens=1000
        )

        return await self.make_request(request)

    async def make_enhanced_reasoning_request(self, query: str, effort: str = "high") -> GrokResponse:
        """Make a request with enhanced reasoning capabilities"""

        request = GrokRequest(
            task_type=GrokTaskType.LOGICAL_REASONING,
            capability=GrokCapability.REASONING,
            prompt=f"Think step-by-step about this trading scenario: {query}",
            reasoning_effort=effort,
            temperature=0.1,
            max_tokens=2000
        )

        return await self.make_request(request)

    async def make_multi_image_analysis_request(self, images: List[bytes], analysis_prompt: str) -> GrokResponse:
        """Make a request with multiple image analysis"""

        request = GrokRequest(
            task_type=GrokTaskType.IMAGE_ANALYSIS,
            capability=GrokCapability.VISION,
            prompt=analysis_prompt,
            multiple_images=images,
            image_detail="high",
            temperature=0.1,
            max_tokens=2000
        )

        return await self.make_request(request)

# ============================================================================
# GROK INTEGRATION ENGINE
# ============================================================================

class AtlasGrokIntegrationEngine:
    """Main Grok integration engine for A.T.L.A.S. v5.0"""

    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.grok_client = GrokAPIClient()

        # Enhancement modules
        self.reasoning_enhancer = None
        self.vision_enhancer = None
        self.multimodal_enhancer = None
        self.code_optimizer = None
        self.sentiment_enhancer = None

        # Performance tracking
        self.enhancement_metrics = {}
        self.integration_cache = {}
        self.max_cache_size = 1000

        logger.info("[GROK] Grok Integration Engine initialized")

    async def initialize(self):
        """Initialize Grok integration engine"""
        try:
            self.status = EngineStatus.INITIALIZING

            # Initialize Grok API client
            client_success = await self.grok_client.initialize()

            # Initialize enhancement modules
            await self._initialize_enhancement_modules()

            # Initialize performance tracking
            await self._initialize_performance_tracking()

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Grok Integration Engine fully initialized")

            return client_success  # Return whether Grok API is actually available

        except Exception as e:
            logger.error(f"Grok integration engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_enhancement_modules(self):
        """Initialize specialized enhancement modules"""
        try:
            self.reasoning_enhancer = GrokReasoningEnhancer(self.grok_client)
            self.vision_enhancer = GrokVisionEnhancer(self.grok_client)
            self.multimodal_enhancer = GrokMultimodalEnhancer(self.grok_client)
            self.code_optimizer = GrokCodeOptimizer(self.grok_client)
            self.sentiment_enhancer = GrokSentimentEnhancer(self.grok_client)

            logger.info("[OK] Grok enhancement modules initialized")

        except Exception as e:
            logger.error(f"Enhancement modules initialization failed: {e}")
            # Continue without enhancement modules - fallback will be used

    async def _initialize_performance_tracking(self):
        """Initialize performance tracking"""
        self.enhancement_metrics = {
            'causal_reasoning_improvements': [],
            'vision_analysis_improvements': [],
            'multimodal_fusion_improvements': [],
            'code_optimization_improvements': [],
            'sentiment_analysis_improvements': [],
            'total_enhancements': 0,
            'average_improvement': 0.0
        }

    async def enhance_causal_reasoning(self, original_result: Dict[str, Any],
                                     symbol: str, intervention: Dict[str, Any]) -> GrokAnalysisResult:
        """Enhance causal reasoning analysis with Grok"""
        try:
            if not self.reasoning_enhancer:
                return self._create_fallback_result(original_result, "Reasoning enhancer not available")

            # Check cache
            cache_key = f"causal_{symbol}_{hash(str(intervention))}"
            if cache_key in self.integration_cache:
                cached = self.integration_cache[cache_key]
                if (datetime.now() - cached.timestamp).seconds < 1800:  # 30 min cache
                    return cached

            # Enhance with Grok
            enhanced_result = await self.reasoning_enhancer.enhance_causal_analysis(
                original_result, symbol, intervention
            )

            # Cache result
            self._cache_result(cache_key, enhanced_result)

            # Update metrics
            self._update_enhancement_metrics('causal_reasoning_improvements', enhanced_result)

            return enhanced_result

        except Exception as e:
            logger.error(f"Causal reasoning enhancement failed: {e}")
            return self._create_fallback_result(original_result, f"Enhancement error: {str(e)}")

    async def enhance_market_psychology(self, original_result: Dict[str, Any],
                                      symbol: str, market_data: Dict[str, Any]) -> GrokAnalysisResult:
        """Enhance market psychology analysis with Grok"""
        try:
            if not self.reasoning_enhancer:
                return self._create_fallback_result(original_result, "Reasoning enhancer not available")

            # Check cache
            cache_key = f"psychology_{symbol}_{hash(str(market_data))}"
            if cache_key in self.integration_cache:
                cached = self.integration_cache[cache_key]
                if (datetime.now() - cached.timestamp).seconds < 900:  # 15 min cache
                    return cached

            # Enhance with Grok
            enhanced_result = await self.reasoning_enhancer.enhance_psychology_analysis(
                original_result, symbol, market_data
            )

            # Cache result
            self._cache_result(cache_key, enhanced_result)

            # Update metrics
            self._update_enhancement_metrics('psychology_improvements', enhanced_result)

            return enhanced_result

        except Exception as e:
            logger.error(f"Market psychology enhancement failed: {e}")
            return self._create_fallback_result(original_result, f"Enhancement error: {str(e)}")

    async def enhance_image_analysis(self, original_result: Dict[str, Any],
                                   image_data: bytes) -> GrokAnalysisResult:
        """Enhance image analysis with Grok vision"""
        try:
            if not self.vision_enhancer:
                return self._create_fallback_result(original_result, "Vision enhancer not available")

            # Check cache (based on image hash)
            image_hash = hashlib.md5(image_data).hexdigest()
            cache_key = f"image_{image_hash}"
            if cache_key in self.integration_cache:
                cached = self.integration_cache[cache_key]
                if (datetime.now() - cached.timestamp).seconds < 3600:  # 1 hour cache
                    return cached

            # Enhance with Grok
            enhanced_result = await self.vision_enhancer.enhance_image_analysis(
                original_result, image_data
            )

            # Cache result
            self._cache_result(cache_key, enhanced_result)

            # Update metrics
            self._update_enhancement_metrics('vision_analysis_improvements', enhanced_result)

            return enhanced_result

        except Exception as e:
            logger.error(f"Image analysis enhancement failed: {e}")
            return self._create_fallback_result(original_result, f"Enhancement error: {str(e)}")

    def _cache_result(self, key: str, result: GrokAnalysisResult):
        """Cache analysis result"""
        try:
            if len(self.integration_cache) >= self.max_cache_size:
                # Remove oldest entries
                oldest_keys = sorted(self.integration_cache.keys(),
                                   key=lambda k: self.integration_cache[k].timestamp)[:100]
                for old_key in oldest_keys:
                    del self.integration_cache[old_key]

            self.integration_cache[key] = result

        except Exception as e:
            logger.error(f"Cache operation failed: {e}")

    def _create_fallback_result(self, original_result: Dict[str, Any], reason: str) -> GrokAnalysisResult:
        """Create fallback result when Grok enhancement is not available"""
        fallback_response = GrokResponse(
            success=False,
            content="",
            confidence=0.0,
            task_type=GrokTaskType.LOGICAL_REASONING,
            capability=GrokCapability.REASONING,
            processing_time=0.0,
            error_message=reason,
            fallback_used=True
        )

        return GrokAnalysisResult(
            original_result=original_result,
            grok_enhancement=fallback_response,
            combined_confidence=original_result.get('confidence', 0.5),
            improvement_metrics={'enhancement_available': False},
            reasoning_chain=[f"Fallback: {reason}"]
        )

    def _update_enhancement_metrics(self, metric_type: str, result: GrokAnalysisResult):
        """Update enhancement performance metrics"""
        try:
            if metric_type not in self.enhancement_metrics:
                self.enhancement_metrics[metric_type] = []

            improvement = result.improvement_metrics.get('confidence_improvement', 0.0)
            self.enhancement_metrics[metric_type].append(improvement)
            self.enhancement_metrics['total_enhancements'] += 1

            # Calculate average improvement
            all_improvements = []
            for improvements in self.enhancement_metrics.values():
                if isinstance(improvements, list):
                    all_improvements.extend(improvements)

            if all_improvements:
                self.enhancement_metrics['average_improvement'] = sum(all_improvements) / len(all_improvements)

        except Exception as e:
            logger.error(f"Metrics update failed: {e}")

    def get_engine_status(self) -> Dict[str, Any]:
        """Get Grok integration engine status"""
        return {
            'status': self.status.value,
            'grok_client': self.grok_client.get_client_status(),
            'enhancement_modules': {
                'reasoning_enhancer': self.reasoning_enhancer is not None,
                'vision_enhancer': self.vision_enhancer is not None,
                'multimodal_enhancer': self.multimodal_enhancer is not None,
                'code_optimizer': self.code_optimizer is not None,
                'sentiment_enhancer': self.sentiment_enhancer is not None
            },
            'performance_metrics': self.enhancement_metrics,
            'cache_size': len(self.integration_cache)
        }

    # ============================================================================
    # ADVANCED CHART ANALYSIS METHODS
    # ============================================================================

    async def analyze_trading_chart(self, chart_image: bytes, symbol: str,
                                  analysis_type: str = "technical") -> GrokAnalysisResult:
        """Advanced single chart analysis with Grok vision capabilities"""
        try:
            prompt = f"""
            Analyze this {analysis_type} chart for {symbol}. Provide detailed analysis including:

            1. **Trend Analysis**: Current trend direction, strength, and duration
            2. **Support & Resistance**: Key price levels with historical significance
            3. **Technical Indicators**: RSI, MACD, moving averages, volume patterns
            4. **Chart Patterns**: Head & shoulders, triangles, flags, wedges, etc.
            5. **Entry/Exit Points**: Optimal buy/sell levels with risk management
            6. **Price Targets**: Short-term and medium-term price objectives
            7. **Risk Assessment**: Potential downside and stop-loss recommendations

            Focus on actionable trading insights with specific price levels and timeframes.
            """

            request = GrokRequest(
                task_type=GrokTaskType.IMAGE_ANALYSIS,
                capability=GrokCapability.VISION,
                prompt=prompt,
                image_data=chart_image,
                image_detail="high",
                temperature=0.1,
                max_tokens=2000,
                context={'symbol': symbol, 'analysis_type': analysis_type}
            )

            grok_response = await self.grok_client.make_request(request)

            # Create enhanced result
            original_result = {
                'symbol': symbol,
                'analysis_type': analysis_type,
                'confidence': 0.7,
                'basic_analysis': 'Chart analysis completed'
            }

            improvement_metrics = {
                'confidence_improvement': max(0, grok_response.confidence - 0.7),
                'detail_enhancement': 0.9 if grok_response.success else 0.0,
                'vision_accuracy': 0.85 if grok_response.success else 0.0
            }

            reasoning_chain = self._extract_reasoning_chain(grok_response.content)
            combined_confidence = self._combine_confidences(0.7, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Chart analysis failed: {e}")
            return self._create_fallback_result({'symbol': symbol}, f"Chart analysis error: {str(e)}")

    async def analyze_multiple_charts(self, chart_images: List[bytes], symbol: str,
                                    timeframes: List[str] = None) -> GrokAnalysisResult:
        """Advanced multi-timeframe chart analysis"""
        try:
            if timeframes is None:
                timeframes = ["1D", "4H", "1H", "15M"][:len(chart_images)]

            prompt = f"""
            Analyze these multiple timeframe charts for {symbol}:
            {', '.join([f"Chart {i+1}: {tf}" for i, tf in enumerate(timeframes)])}

            Provide comprehensive multi-timeframe analysis:

            1. **Trend Alignment**: How trends align across timeframes
            2. **Confluence Zones**: Areas where multiple timeframes show support/resistance
            3. **Entry Timing**: Best timeframe for entry based on alignment
            4. **Risk Management**: Multi-timeframe stop-loss strategy
            5. **Target Progression**: How targets align across timeframes
            6. **Divergences**: Any conflicts between timeframes and their implications

            Synthesize insights from all timeframes for optimal trading strategy.
            """

            request = GrokRequest(
                task_type=GrokTaskType.IMAGE_ANALYSIS,
                capability=GrokCapability.VISION,
                prompt=prompt,
                multiple_images=chart_images,
                image_detail="high",
                temperature=0.1,
                max_tokens=2500,
                context={'symbol': symbol, 'timeframes': timeframes}
            )

            grok_response = await self.grok_client.make_request(request)

            original_result = {
                'symbol': symbol,
                'timeframes': timeframes,
                'chart_count': len(chart_images),
                'confidence': 0.6
            }

            improvement_metrics = {
                'confidence_improvement': max(0, grok_response.confidence - 0.6),
                'multi_timeframe_synthesis': 0.9 if grok_response.success else 0.0,
                'analysis_depth': len(chart_images) / 4.0  # Normalize by max expected charts
            }

            reasoning_chain = self._extract_reasoning_chain(grok_response.content)
            combined_confidence = self._combine_confidences(0.6, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Multi-chart analysis failed: {e}")
            return self._create_fallback_result({'symbol': symbol}, f"Multi-chart analysis error: {str(e)}")

    async def analyze_earnings_documents(self, document_images: List[bytes],
                                       symbol: str) -> GrokAnalysisResult:
        """Analyze earnings reports, financial documents, and presentations"""
        try:
            prompt = f"""
            Analyze these financial documents for {symbol}:

            Extract and analyze:
            1. **Key Financial Metrics**: Revenue, EPS, margins, growth rates
            2. **Forward Guidance**: Management projections and outlook
            3. **Risk Factors**: Identified risks and challenges
            4. **Market Opportunities**: Growth drivers and expansion plans
            5. **Competitive Position**: Market share and competitive advantages
            6. **Cash Flow Analysis**: Operating, investing, financing cash flows
            7. **Trading Implications**: How this impacts stock valuation and trading

            Provide actionable trading insights based on fundamental analysis.
            """

            request = GrokRequest(
                task_type=GrokTaskType.IMAGE_ANALYSIS,
                capability=GrokCapability.VISION,
                prompt=prompt,
                multiple_images=document_images,
                image_detail="high",
                temperature=0.1,
                max_tokens=3000,
                context={'symbol': symbol, 'document_type': 'earnings'}
            )

            grok_response = await self.grok_client.make_request(request)

            original_result = {
                'symbol': symbol,
                'document_count': len(document_images),
                'analysis_type': 'fundamental',
                'confidence': 0.75
            }

            improvement_metrics = {
                'confidence_improvement': max(0, grok_response.confidence - 0.75),
                'document_comprehension': 0.9 if grok_response.success else 0.0,
                'fundamental_insight': 0.85 if grok_response.success else 0.0
            }

            reasoning_chain = self._extract_reasoning_chain(grok_response.content)
            combined_confidence = self._combine_confidences(0.75, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Document analysis failed: {e}")
            return self._create_fallback_result({'symbol': symbol}, f"Document analysis error: {str(e)}")

    # ============================================================================
    # ENHANCED REASONING METHODS
    # ============================================================================

    async def enhanced_market_reasoning(self, market_scenario: str, symbol: str = None,
                                      reasoning_effort: str = "high") -> GrokAnalysisResult:
        """Advanced reasoning analysis for complex market scenarios"""
        try:
            prompt = f"""
            As an expert quantitative analyst and market strategist, analyze this market scenario step-by-step:

            SCENARIO: {market_scenario}
            {f"FOCUS SYMBOL: {symbol}" if symbol else ""}

            Please think through this systematically:

            1. **Market Context Analysis**
               - Current market regime (bull/bear/sideways)
               - Key macro factors influencing the scenario
               - Sector-specific considerations

            2. **Probability Assessment**
               - Likelihood of different outcomes
               - Key risk factors and their probabilities
               - Historical precedents and their relevance

            3. **Causal Chain Analysis**
               - Primary drivers of the scenario
               - Secondary and tertiary effects
               - Feedback loops and amplification factors

            4. **Trading Strategy Implications**
               - Optimal positioning for each outcome
               - Risk management considerations
               - Entry/exit timing strategies

            5. **Confidence Assessment**
               - Certainty level of your analysis
               - Key assumptions and their validity
               - Potential blind spots or unknown factors

            Provide detailed reasoning for each step and conclude with actionable recommendations.
            """

            request = GrokRequest(
                task_type=GrokTaskType.LOGICAL_REASONING,
                capability=GrokCapability.REASONING,
                prompt=prompt,
                reasoning_effort=reasoning_effort,
                temperature=0.1,
                max_tokens=3000,
                context={'scenario': market_scenario, 'symbol': symbol}
            )

            grok_response = await self.grok_client.make_request(request)

            original_result = {
                'scenario': market_scenario,
                'symbol': symbol,
                'basic_reasoning': 'Standard market analysis completed',
                'confidence': 0.6
            }

            improvement_metrics = {
                'confidence_improvement': max(0, grok_response.confidence - 0.6),
                'reasoning_depth': 0.9 if grok_response.reasoning_content else 0.5,
                'logical_coherence': 0.85 if grok_response.success else 0.0,
                'actionability': 0.8 if grok_response.success else 0.0
            }

            # Extract reasoning chain from reasoning content if available
            reasoning_chain = []
            if grok_response.reasoning_content:
                reasoning_chain = self._extract_reasoning_steps(grok_response.reasoning_content)
            else:
                reasoning_chain = self._extract_reasoning_chain(grok_response.content)

            combined_confidence = self._combine_confidences(0.6, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Enhanced reasoning failed: {e}")
            return self._create_fallback_result({'scenario': market_scenario}, f"Reasoning error: {str(e)}")

    async def what_if_scenario_analysis(self, base_scenario: str, interventions: List[str],
                                      symbol: str = None) -> GrokAnalysisResult:
        """Advanced what-if scenario analysis with multiple interventions"""
        try:
            interventions_text = "\n".join([f"- {intervention}" for intervention in interventions])

            prompt = f"""
            Conduct a comprehensive what-if analysis for this trading scenario:

            BASE SCENARIO: {base_scenario}
            {f"SYMBOL: {symbol}" if symbol else ""}

            POTENTIAL INTERVENTIONS/CHANGES:
            {interventions_text}

            For each intervention, analyze:

            1. **Direct Impact Assessment**
               - Immediate market reaction
               - Price movement magnitude and direction
               - Volume and volatility implications

            2. **Cascade Effect Analysis**
               - Secondary market effects
               - Cross-asset correlations
               - Sector spillover effects

            3. **Probability-Weighted Outcomes**
               - Most likely scenario (>50% probability)
               - Alternative scenarios (20-50% probability)
               - Tail risk scenarios (<20% probability)

            4. **Optimal Response Strategy**
               - Best trading approach for each scenario
               - Portfolio adjustments needed
               - Risk mitigation strategies

            5. **Sensitivity Analysis**
               - Which factors have the highest impact
               - Threshold levels for different outcomes
               - Key monitoring indicators

            Conclude with a decision matrix showing optimal actions for each scenario.
            """

            request = GrokRequest(
                task_type=GrokTaskType.CAUSAL_ANALYSIS,
                capability=GrokCapability.LOGICAL_INFERENCE,
                prompt=prompt,
                reasoning_effort="high",
                temperature=0.15,
                max_tokens=3500,
                context={
                    'base_scenario': base_scenario,
                    'interventions': interventions,
                    'symbol': symbol
                }
            )

            grok_response = await self.grok_client.make_request(request)

            original_result = {
                'base_scenario': base_scenario,
                'interventions_count': len(interventions),
                'symbol': symbol,
                'confidence': 0.65
            }

            improvement_metrics = {
                'confidence_improvement': max(0, grok_response.confidence - 0.65),
                'scenario_coverage': len(interventions) / 5.0,  # Normalize by typical max
                'causal_depth': 0.9 if grok_response.success else 0.0,
                'decision_support': 0.85 if grok_response.success else 0.0
            }

            reasoning_chain = []
            if grok_response.reasoning_content:
                reasoning_chain = self._extract_reasoning_steps(grok_response.reasoning_content)
            else:
                reasoning_chain = self._extract_reasoning_chain(grok_response.content)

            combined_confidence = self._combine_confidences(0.65, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"What-if analysis failed: {e}")
            return self._create_fallback_result({'scenario': base_scenario}, f"What-if analysis error: {str(e)}")

    def _extract_reasoning_steps(self, reasoning_content: str) -> List[str]:
        """Extract structured reasoning steps from reasoning content"""
        try:
            steps = []
            lines = reasoning_content.split('\n')
            current_step = ""

            for line in lines:
                line = line.strip()
                if not line:
                    if current_step:
                        steps.append(current_step.strip())
                        current_step = ""
                    continue

                # Look for step indicators
                if any(indicator in line.lower() for indicator in
                      ['step', 'first', 'second', 'third', 'next', 'then', 'therefore', 'conclusion']):
                    if current_step:
                        steps.append(current_step.strip())
                    current_step = line
                else:
                    current_step += " " + line

            # Add final step
            if current_step:
                steps.append(current_step.strip())

            return steps[:10]  # Limit to 10 key steps

        except Exception as e:
            logger.error(f"Reasoning step extraction failed: {e}")
            return ["Reasoning steps extraction failed"]

# ============================================================================
# SPECIALIZED ENHANCEMENT CLASSES
# ============================================================================

class GrokReasoningEnhancer:
    """Grok-powered reasoning enhancement for causal analysis and market psychology"""

    def __init__(self, grok_client: GrokAPIClient):
        self.grok_client = grok_client
        self.reasoning_templates = self._load_reasoning_templates()

    def _load_reasoning_templates(self) -> Dict[str, str]:
        """Load reasoning prompt templates"""
        return {
            'causal_analysis': """
As an expert financial analyst with advanced causal reasoning capabilities, enhance this causal analysis:

ORIGINAL ANALYSIS:
{original_analysis}

MARKET CONTEXT:
- Symbol: {symbol}
- Intervention: {intervention}
- Market Variables: {market_variables}

Please provide:
1. Enhanced causal reasoning with logical chain of inference
2. Additional causal relationships not captured in original analysis
3. Confidence assessment of causal links
4. Risk factors and alternative explanations
5. Actionable insights for trading decisions

Focus on logical reasoning and evidence-based conclusions.
""",
            'psychology_analysis': """
As an expert in behavioral finance and market psychology, enhance this market psychology analysis:

ORIGINAL ANALYSIS:
{original_analysis}

MARKET CONTEXT:
- Symbol: {symbol}
- Market Data: {market_data}
- Current Sentiment: {current_sentiment}

Please provide:
1. Enhanced psychological analysis with deeper behavioral insights
2. Market participant behavior predictions
3. Emotional state analysis and implications
4. Crowd psychology dynamics
5. Contrarian vs momentum opportunities

Focus on human psychology and behavioral patterns in markets.
"""
        }

    async def enhance_causal_analysis(self, original_result: Dict[str, Any],
                                    symbol: str, intervention: Dict[str, Any]) -> GrokAnalysisResult:
        """Enhance causal analysis with Grok reasoning"""
        try:
            # Prepare enhanced prompt
            prompt = self.reasoning_templates['causal_analysis'].format(
                original_analysis=json.dumps(original_result, indent=2),
                symbol=symbol,
                intervention=json.dumps(intervention, indent=2),
                market_variables=", ".join(intervention.keys())
            )

            # Create Grok request
            request = GrokRequest(
                task_type=GrokTaskType.CAUSAL_ANALYSIS,
                capability=GrokCapability.LOGICAL_INFERENCE,
                prompt=prompt,
                temperature=0.1,  # Low temperature for logical reasoning
                context={'symbol': symbol, 'intervention': intervention}
            )

            # Get Grok enhancement
            grok_response = await self.grok_client.make_request(request)

            # Calculate improvement metrics
            improvement_metrics = self._calculate_causal_improvement(original_result, grok_response)

            # Extract reasoning chain
            reasoning_chain = self._extract_reasoning_chain(grok_response.content)

            # Calculate combined confidence
            original_confidence = original_result.get('confidence', 0.5)
            combined_confidence = self._combine_confidences(original_confidence, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Causal analysis enhancement failed: {e}")
            raise

    async def enhance_psychology_analysis(self, original_result: Dict[str, Any],
                                        symbol: str, market_data: Dict[str, Any]) -> GrokAnalysisResult:
        """Enhance market psychology analysis with Grok reasoning"""
        try:
            # Prepare enhanced prompt
            prompt = self.reasoning_templates['psychology_analysis'].format(
                original_analysis=json.dumps(original_result, indent=2),
                symbol=symbol,
                market_data=json.dumps(market_data, indent=2),
                current_sentiment=original_result.get('dominant_emotion', 'neutral')
            )

            # Create Grok request
            request = GrokRequest(
                task_type=GrokTaskType.MARKET_PSYCHOLOGY,
                capability=GrokCapability.LOGICAL_INFERENCE,
                prompt=prompt,
                temperature=0.2,  # Slightly higher for psychology analysis
                context={'symbol': symbol, 'market_data': market_data}
            )

            # Get Grok enhancement
            grok_response = await self.grok_client.make_request(request)

            # Calculate improvement metrics
            improvement_metrics = self._calculate_psychology_improvement(original_result, grok_response)

            # Extract reasoning chain
            reasoning_chain = self._extract_reasoning_chain(grok_response.content)

            # Calculate combined confidence
            original_confidence = original_result.get('confidence', 0.5)
            combined_confidence = self._combine_confidences(original_confidence, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Psychology analysis enhancement failed: {e}")
            raise

    def _calculate_causal_improvement(self, original: Dict[str, Any], grok_response: GrokResponse) -> Dict[str, float]:
        """Calculate improvement metrics for causal analysis"""
        try:
            metrics = {
                'confidence_improvement': 0.0,
                'reasoning_depth': 0.0,
                'causal_links_identified': 0.0,
                'actionable_insights': 0.0
            }

            if grok_response.success and grok_response.content:
                # Analyze content for improvements
                content = grok_response.content.lower()

                # Count reasoning indicators
                reasoning_indicators = ['because', 'therefore', 'consequently', 'leads to', 'causes', 'results in']
                reasoning_count = sum(content.count(indicator) for indicator in reasoning_indicators)
                metrics['reasoning_depth'] = min(reasoning_count / 10.0, 1.0)

                # Count causal relationships
                causal_indicators = ['correlation', 'causation', 'relationship', 'influence', 'impact']
                causal_count = sum(content.count(indicator) for indicator in causal_indicators)
                metrics['causal_links_identified'] = min(causal_count / 5.0, 1.0)

                # Count actionable insights
                action_indicators = ['recommend', 'suggest', 'should', 'consider', 'opportunity', 'risk']
                action_count = sum(content.count(indicator) for indicator in action_indicators)
                metrics['actionable_insights'] = min(action_count / 3.0, 1.0)

                # Overall confidence improvement
                original_conf = original.get('confidence', 0.5)
                if grok_response.confidence > original_conf:
                    metrics['confidence_improvement'] = grok_response.confidence - original_conf

            return metrics

        except Exception as e:
            logger.error(f"Causal improvement calculation failed: {e}")
            return {'confidence_improvement': 0.0}

    def _calculate_psychology_improvement(self, original: Dict[str, Any], grok_response: GrokResponse) -> Dict[str, float]:
        """Calculate improvement metrics for psychology analysis"""
        try:
            metrics = {
                'confidence_improvement': 0.0,
                'behavioral_insights': 0.0,
                'emotional_analysis': 0.0,
                'participant_predictions': 0.0
            }

            if grok_response.success and grok_response.content:
                content = grok_response.content.lower()

                # Count behavioral insights
                behavior_indicators = ['behavior', 'psychology', 'sentiment', 'emotion', 'fear', 'greed']
                behavior_count = sum(content.count(indicator) for indicator in behavior_indicators)
                metrics['behavioral_insights'] = min(behavior_count / 8.0, 1.0)

                # Count emotional analysis
                emotion_indicators = ['fear', 'greed', 'panic', 'euphoria', 'anxiety', 'confidence']
                emotion_count = sum(content.count(indicator) for indicator in emotion_indicators)
                metrics['emotional_analysis'] = min(emotion_count / 6.0, 1.0)

                # Count participant predictions
                participant_indicators = ['retail', 'institutional', 'traders', 'investors', 'participants']
                participant_count = sum(content.count(indicator) for indicator in participant_indicators)
                metrics['participant_predictions'] = min(participant_count / 5.0, 1.0)

                # Overall confidence improvement
                original_conf = original.get('confidence', 0.5)
                if grok_response.confidence > original_conf:
                    metrics['confidence_improvement'] = grok_response.confidence - original_conf

            return metrics

        except Exception as e:
            logger.error(f"Psychology improvement calculation failed: {e}")
            return {'confidence_improvement': 0.0}

    def _extract_reasoning_chain(self, content: str) -> List[str]:
        """Extract reasoning chain from Grok response"""
        try:
            reasoning_chain = []

            # Split content into sentences and look for reasoning patterns
            sentences = content.split('.')

            for sentence in sentences:
                sentence = sentence.strip()
                if any(indicator in sentence.lower() for indicator in
                      ['because', 'therefore', 'consequently', 'leads to', 'causes', 'results in']):
                    reasoning_chain.append(sentence)

            return reasoning_chain[:10]  # Limit to top 10 reasoning steps

        except Exception as e:
            logger.error(f"Reasoning chain extraction failed: {e}")
            return ["Reasoning chain extraction failed"]

    def _combine_confidences(self, original_conf: float, grok_conf: float) -> float:
        """Combine original and Grok confidence scores"""
        try:
            # Weighted average with slight bias toward Grok if it's higher
            if grok_conf > original_conf:
                return (original_conf * 0.4) + (grok_conf * 0.6)
            else:
                return (original_conf * 0.6) + (grok_conf * 0.4)

        except Exception as e:
            logger.error(f"Confidence combination failed: {e}")
            return original_conf

class GrokVisionEnhancer:
    """Grok-powered vision enhancement for image analysis"""

    def __init__(self, grok_client: GrokAPIClient):
        self.grok_client = grok_client
        self.vision_templates = self._load_vision_templates()

    def _load_vision_templates(self) -> Dict[str, str]:
        """Load vision analysis prompt templates"""
        return {
            'chart_analysis': """
As an expert technical analyst with advanced pattern recognition capabilities, analyze this financial chart image and enhance the existing analysis:

ORIGINAL ANALYSIS:
{original_analysis}

Please provide enhanced analysis including:
1. Advanced chart pattern recognition (triangles, flags, head & shoulders, etc.)
2. Support and resistance level identification with precision
3. Volume analysis and price action confirmation
4. Trend analysis with multiple timeframe perspective
5. Entry/exit points with risk management levels
6. Market structure analysis (higher highs, lower lows, etc.)

Focus on actionable technical insights that complement the original analysis.
""",
            'pattern_detection': """
As a pattern recognition expert, analyze this financial chart for advanced patterns:

ORIGINAL DETECTION:
{original_analysis}

Enhance the analysis with:
1. Complex pattern identification (Elliott Wave, Fibonacci, etc.)
2. Pattern completion probability
3. Target price projections
4. Pattern failure scenarios
5. Volume confirmation analysis
6. Multi-timeframe pattern alignment

Provide specific, measurable insights for trading decisions.
"""
        }

    async def enhance_image_analysis(self, original_result: Dict[str, Any],
                                   image_data: bytes) -> GrokAnalysisResult:
        """Enhance image analysis with Grok vision"""
        try:
            # Determine analysis type
            analysis_type = original_result.get('image_type', 'chart_analysis')
            template_key = 'chart_analysis' if 'chart' in analysis_type else 'pattern_detection'

            # Prepare enhanced prompt
            prompt = self.vision_templates[template_key].format(
                original_analysis=json.dumps(original_result, indent=2)
            )

            # Create Grok request with image
            request = GrokRequest(
                task_type=GrokTaskType.IMAGE_ANALYSIS,
                capability=GrokCapability.VISION,
                prompt=prompt,
                image_data=image_data,
                temperature=0.1,  # Low temperature for precise analysis
                context={'analysis_type': analysis_type}
            )

            # Get Grok enhancement
            grok_response = await self.grok_client.make_request(request)

            # Calculate improvement metrics
            improvement_metrics = self._calculate_vision_improvement(original_result, grok_response)

            # Extract pattern insights
            reasoning_chain = self._extract_pattern_insights(grok_response.content)

            # Calculate combined confidence
            original_confidence = original_result.get('confidence', 0.5)
            combined_confidence = self._combine_confidences(original_confidence, grok_response.confidence)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Vision analysis enhancement failed: {e}")
            raise

    def _calculate_vision_improvement(self, original: Dict[str, Any], grok_response: GrokResponse) -> Dict[str, float]:
        """Calculate improvement metrics for vision analysis"""
        try:
            metrics = {
                'confidence_improvement': 0.0,
                'pattern_recognition': 0.0,
                'technical_precision': 0.0,
                'actionable_insights': 0.0
            }

            if grok_response.success and grok_response.content:
                content = grok_response.content.lower()

                # Count pattern recognition improvements
                pattern_indicators = ['pattern', 'triangle', 'flag', 'head', 'shoulder', 'support', 'resistance']
                pattern_count = sum(content.count(indicator) for indicator in pattern_indicators)
                metrics['pattern_recognition'] = min(pattern_count / 10.0, 1.0)

                # Count technical precision indicators
                precision_indicators = ['level', 'price', 'target', 'stop', 'entry', 'exit']
                precision_count = sum(content.count(indicator) for indicator in precision_indicators)
                metrics['technical_precision'] = min(precision_count / 8.0, 1.0)

                # Count actionable insights
                action_indicators = ['buy', 'sell', 'hold', 'target', 'stop', 'risk']
                action_count = sum(content.count(indicator) for indicator in action_indicators)
                metrics['actionable_insights'] = min(action_count / 6.0, 1.0)

                # Overall confidence improvement
                original_conf = original.get('confidence', 0.5)
                if grok_response.confidence > original_conf:
                    metrics['confidence_improvement'] = grok_response.confidence - original_conf

            return metrics

        except Exception as e:
            logger.error(f"Vision improvement calculation failed: {e}")
            return {'confidence_improvement': 0.0}

    def _extract_pattern_insights(self, content: str) -> List[str]:
        """Extract pattern insights from Grok response"""
        try:
            insights = []

            # Split content and look for pattern-related insights
            sentences = content.split('.')

            for sentence in sentences:
                sentence = sentence.strip()
                if any(indicator in sentence.lower() for indicator in
                      ['pattern', 'support', 'resistance', 'trend', 'target', 'breakout']):
                    insights.append(sentence)

            return insights[:8]  # Limit to top 8 insights

        except Exception as e:
            logger.error(f"Pattern insights extraction failed: {e}")
            return ["Pattern insights extraction failed"]

    def _combine_confidences(self, original_conf: float, grok_conf: float) -> float:
        """Combine original and Grok confidence scores"""
        try:
            # For vision analysis, give more weight to Grok if it provides detailed analysis
            if grok_conf > 0.7:
                return (original_conf * 0.3) + (grok_conf * 0.7)
            else:
                return (original_conf * 0.6) + (grok_conf * 0.4)

        except Exception as e:
            logger.error(f"Confidence combination failed: {e}")
            return original_conf

class GrokMultimodalEnhancer:
    """Grok-powered multimodal enhancement for data fusion"""

    def __init__(self, grok_client: GrokAPIClient):
        self.grok_client = grok_client

    async def enhance_data_fusion(self, original_result: Dict[str, Any],
                                modalities: List[str], symbol: str) -> GrokAnalysisResult:
        """Enhance multimodal data fusion with Grok"""
        try:
            # Prepare fusion enhancement prompt
            prompt = f"""
As an expert in multimodal financial analysis, enhance this data fusion result:

ORIGINAL FUSION RESULT:
{json.dumps(original_result, indent=2)}

MODALITIES ANALYZED: {', '.join(modalities)}
SYMBOL: {symbol}

Please provide:
1. Cross-modal correlation insights
2. Conflicting signal resolution
3. Weighted importance of each modality
4. Integrated trading signals
5. Risk assessment across modalities
6. Confidence calibration

Focus on synthesizing insights across all data sources for optimal trading decisions.
"""

            # Create Grok request
            request = GrokRequest(
                task_type=GrokTaskType.DATA_FUSION,
                capability=GrokCapability.MULTIMODAL,
                prompt=prompt,
                temperature=0.2,
                context={'modalities': modalities, 'symbol': symbol}
            )

            # Get Grok enhancement
            grok_response = await self.grok_client.make_request(request)

            # Calculate improvement metrics
            improvement_metrics = {
                'confidence_improvement': max(0, grok_response.confidence - original_result.get('confidence', 0.5)),
                'modality_integration': len(modalities) / 5.0,  # Normalize by max expected modalities
                'signal_coherence': 0.8 if grok_response.success else 0.0
            }

            # Extract fusion insights
            reasoning_chain = [f"Multimodal fusion across {len(modalities)} data sources"]
            if grok_response.success:
                reasoning_chain.extend(grok_response.content.split('\n')[:5])

            # Calculate combined confidence
            original_confidence = original_result.get('confidence', 0.5)
            combined_confidence = (original_confidence * 0.4) + (grok_response.confidence * 0.6)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Multimodal enhancement failed: {e}")
            raise

class GrokCodeOptimizer:
    """Grok-powered code optimization for ML models"""

    def __init__(self, grok_client: GrokAPIClient):
        self.grok_client = grok_client

    async def optimize_ml_code(self, code: str, optimization_target: str) -> GrokResponse:
        """Optimize ML code with Grok"""
        try:
            prompt = f"""
As an expert ML engineer and Python optimization specialist, optimize this code:

CODE TO OPTIMIZE:
```python
{code}
```

OPTIMIZATION TARGET: {optimization_target}

Please provide:
1. Optimized code with performance improvements
2. Explanation of optimizations made
3. Expected performance gains
4. Potential risks or trade-offs
5. Testing recommendations

Focus on maintainable, efficient code that improves {optimization_target}.
"""

            request = GrokRequest(
                task_type=GrokTaskType.CODE_OPTIMIZATION,
                capability=GrokCapability.CODE_GENERATION,
                prompt=prompt,
                temperature=0.1,  # Low temperature for precise code
                context={'optimization_target': optimization_target}
            )

            return await self.grok_client.make_request(request)

        except Exception as e:
            logger.error(f"Code optimization failed: {e}")
            raise

class GrokSentimentEnhancer:
    """Grok-powered sentiment enhancement for real-time analysis"""

    def __init__(self, grok_client: GrokAPIClient):
        self.grok_client = grok_client

    async def enhance_sentiment_analysis(self, original_result: Dict[str, Any],
                                       text_data: str, symbol: str) -> GrokAnalysisResult:
        """Enhance sentiment analysis with Grok"""
        try:
            prompt = f"""
As an expert in financial sentiment analysis and market psychology, enhance this sentiment analysis:

ORIGINAL ANALYSIS:
{json.dumps(original_result, indent=2)}

TEXT DATA: {text_data[:1000]}...
SYMBOL: {symbol}

Please provide:
1. Enhanced sentiment classification with nuanced emotions
2. Market impact assessment
3. Sentiment trend analysis
4. Key phrase extraction and importance
5. Contrarian vs consensus sentiment indicators
6. Trading signal implications

Focus on actionable sentiment insights for trading decisions.
"""

            request = GrokRequest(
                task_type=GrokTaskType.REAL_TIME_SENTIMENT,
                capability=GrokCapability.SENTIMENT_ANALYSIS,
                prompt=prompt,
                temperature=0.2,
                context={'symbol': symbol, 'text_length': len(text_data)}
            )

            grok_response = await self.grok_client.make_request(request)

            # Calculate improvement metrics
            improvement_metrics = {
                'confidence_improvement': max(0, grok_response.confidence - original_result.get('confidence', 0.5)),
                'sentiment_precision': 0.8 if grok_response.success else 0.0,
                'market_relevance': 0.9 if symbol in text_data else 0.5
            }

            # Extract sentiment insights
            reasoning_chain = [f"Enhanced sentiment analysis for {symbol}"]
            if grok_response.success:
                reasoning_chain.extend(grok_response.content.split('\n')[:4])

            # Calculate combined confidence
            original_confidence = original_result.get('confidence', 0.5)
            combined_confidence = (original_confidence * 0.3) + (grok_response.confidence * 0.7)

            return GrokAnalysisResult(
                original_result=original_result,
                grok_enhancement=grok_response,
                combined_confidence=combined_confidence,
                improvement_metrics=improvement_metrics,
                reasoning_chain=reasoning_chain
            )

        except Exception as e:
            logger.error(f"Sentiment enhancement failed: {e}")
            raise

    # ============================================================================
    # MULTI-AGENT SUPPORT METHODS
    # ============================================================================

    async def process_multi_agent_request(self, request: MultiAgentGrokRequest) -> MultiAgentGrokResponse:
        """Process a multi-agent specific Grok request"""
        try:
            start_time = time.time()

            # Create enhanced prompt with multi-agent context
            enhanced_prompt = self._create_multi_agent_prompt(request)

            # Create standard Grok request
            grok_request = GrokRequest(
                task_type=request.task_type,
                capability=request.capability,
                prompt=enhanced_prompt,
                context=request.agent_context,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )

            # Make the request
            grok_response = await self.grok_client.make_request(grok_request)

            # Calculate consensus score if other agent outputs are provided
            consensus_score = None
            conflict_indicators = []
            coordination_suggestions = []

            if request.other_agent_outputs:
                consensus_score = self._calculate_consensus_score(
                    grok_response.content, request.other_agent_outputs
                )
                conflict_indicators = self._identify_conflicts(
                    grok_response.content, request.other_agent_outputs
                )
                coordination_suggestions = self._generate_coordination_suggestions(
                    request.agent_role, request.other_agent_outputs
                )

            processing_time = time.time() - start_time

            return MultiAgentGrokResponse(
                agent_id=request.agent_id,
                agent_role=request.agent_role,
                success=grok_response.success,
                content=grok_response.content,
                confidence=grok_response.confidence,
                task_type=request.task_type,
                capability=request.capability,
                processing_time=processing_time,
                consensus_score=consensus_score,
                conflict_indicators=conflict_indicators,
                coordination_suggestions=coordination_suggestions,
                tokens_used=grok_response.tokens_used,
                error_message=grok_response.error_message,
                metadata=grok_response.metadata
            )

        except Exception as e:
            logger.error(f"Multi-agent Grok request failed for agent {request.agent_id}: {e}")
            return MultiAgentGrokResponse(
                agent_id=request.agent_id,
                agent_role=request.agent_role,
                success=False,
                content="",
                confidence=0.0,
                task_type=request.task_type,
                capability=request.capability,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )

    def _create_multi_agent_prompt(self, request: MultiAgentGrokRequest) -> str:
        """Create enhanced prompt with multi-agent context"""
        base_prompt = request.prompt

        # Add agent role context
        role_context = f"\nYou are acting as the {request.agent_role} in a multi-agent trading system."

        # Add coordination context
        coordination_context = ""
        if request.coordination_mode == "collaborative":
            coordination_context = "\nWork collaboratively with other agents to achieve the best overall result."
        elif request.coordination_mode == "competitive":
            coordination_context = "\nProvide your best independent analysis, considering but not deferring to other agents."
        elif request.coordination_mode == "consensus":
            coordination_context = "\nFocus on finding common ground and consensus with other agent outputs."

        # Add other agent outputs context
        other_agents_context = ""
        if request.other_agent_outputs:
            other_agents_context = "\n\nOther agent outputs for context:\n"
            for agent_name, output in request.other_agent_outputs.items():
                other_agents_context += f"{agent_name}: {str(output)[:200]}...\n"

        # Add priority context
        priority_context = f"\nPriority level: {request.priority_level}/5"

        enhanced_prompt = (
            base_prompt +
            role_context +
            coordination_context +
            other_agents_context +
            priority_context
        )

        return enhanced_prompt

    def _calculate_consensus_score(self, current_output: str, other_outputs: Dict[str, Any]) -> float:
        """Calculate consensus score between current output and other agent outputs"""
        try:
            # Simple consensus calculation based on keyword overlap
            current_keywords = set(current_output.lower().split())

            consensus_scores = []
            for agent_name, output in other_outputs.items():
                if isinstance(output, dict):
                    output_text = str(output.get('content', ''))
                else:
                    output_text = str(output)

                other_keywords = set(output_text.lower().split())

                # Calculate Jaccard similarity
                intersection = len(current_keywords.intersection(other_keywords))
                union = len(current_keywords.union(other_keywords))

                if union > 0:
                    similarity = intersection / union
                    consensus_scores.append(similarity)

            return sum(consensus_scores) / len(consensus_scores) if consensus_scores else 0.0

        except Exception as e:
            logger.warning(f"Consensus score calculation failed: {e}")
            return 0.0

    def _identify_conflicts(self, current_output: str, other_outputs: Dict[str, Any]) -> List[str]:
        """Identify potential conflicts between agent outputs"""
        conflicts = []

        try:
            # Look for conflicting sentiment indicators
            current_lower = current_output.lower()

            for agent_name, output in other_outputs.items():
                if isinstance(output, dict):
                    output_text = str(output.get('content', '')).lower()
                else:
                    output_text = str(output).lower()

                # Check for opposing sentiment
                if ("bullish" in current_lower and "bearish" in output_text) or \
                   ("bearish" in current_lower and "bullish" in output_text):
                    conflicts.append(f"Sentiment conflict with {agent_name}")

                # Check for opposing recommendations
                if ("buy" in current_lower and "sell" in output_text) or \
                   ("sell" in current_lower and "buy" in output_text):
                    conflicts.append(f"Trading recommendation conflict with {agent_name}")

                # Check for risk level conflicts
                if ("high risk" in current_lower and "low risk" in output_text) or \
                   ("low risk" in current_lower and "high risk" in output_text):
                    conflicts.append(f"Risk assessment conflict with {agent_name}")

        except Exception as e:
            logger.warning(f"Conflict identification failed: {e}")

        return conflicts

    def _generate_coordination_suggestions(self, agent_role: str, other_outputs: Dict[str, Any]) -> List[str]:
        """Generate suggestions for better agent coordination"""
        suggestions = []

        try:
            # Role-specific coordination suggestions
            if agent_role == "data_validator":
                suggestions.append("Ensure data quality metrics are shared with pattern detection agent")
                suggestions.append("Validate data sources used by other agents")

            elif agent_role == "pattern_detector":
                suggestions.append("Share pattern confidence scores with risk management agent")
                suggestions.append("Coordinate with sentiment analysis for pattern confirmation")

            elif agent_role == "analysis_engine":
                suggestions.append("Integrate technical patterns from pattern detection agent")
                suggestions.append("Consider risk metrics from risk management agent")

            elif agent_role == "risk_manager":
                suggestions.append("Incorporate pattern strength in risk calculations")
                suggestions.append("Adjust position sizing based on sentiment analysis")

            elif agent_role == "trade_executor":
                suggestions.append("Ensure all agent outputs are validated before execution")
                suggestions.append("Coordinate timing with market conditions from other agents")

            elif agent_role == "validation_supervisor":
                suggestions.append("Cross-validate all agent outputs for consistency")
                suggestions.append("Flag any conflicts for resolution before final recommendation")

            # General coordination suggestions
            if len(other_outputs) > 2:
                suggestions.append("Consider implementing weighted consensus mechanism")

            if any("error" in str(output).lower() for output in other_outputs.values()):
                suggestions.append("Some agents reported errors - consider fallback strategies")

        except Exception as e:
            logger.warning(f"Coordination suggestion generation failed: {e}")

        return suggestions

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    # Core enums and models
    "GrokCapability",
    "GrokTaskType",
    "GrokRequest",
    "GrokResponse",
    "GrokAnalysisResult",

    # Multi-agent specific models
    "MultiAgentGrokRequest",
    "MultiAgentGrokResponse",

    # Structured output models (Pydantic)
    "TradingSignal",
    "MarketSentiment",
    "TechnicalAnalysis",
    "MarketAnalysis",
    "NewsAnalysis",

    # Trading tools and configurations
    "ATLAS_TRADING_TOOLS",
    "MARKET_SEARCH_CONFIGS",

    # Core API client and engine
    "GrokAPIClient",
    "AtlasGrokIntegrationEngine",

    # Specialized enhancement classes
    "GrokReasoningEnhancer",
    "GrokVisionEnhancer",
    "GrokMultimodalEnhancer",
    "GrokCodeOptimizer",
    "GrokSentimentEnhancer",

    # Availability flags
    "PYDANTIC_AVAILABLE",
    "HTTPX_AVAILABLE"
]
