#!/usr/bin/env python3
"""
Atlas Enhanced Infrastructure - Advanced Caching, Load Balancing, and Performance
Provides Redis caching, load balancing, Docker support, and performance optimization
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import hashlib
import os

logger = logging.getLogger(__name__)

# Try to import Redis for advanced caching
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

class AtlasAdvancedCache:
    """Advanced multi-level caching system"""
    
    def __init__(self):
        self.redis_client = None
        self.local_cache = {}  # L1 cache (in-memory)
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'redis_hits': 0,
            'local_hits': 0
        }
        
    async def initialize(self):
        """Initialize caching system"""
        try:
            if REDIS_AVAILABLE:
                # Try to connect to Redis
                try:
                    self.redis_client = redis.Redis(
                        host=os.getenv('REDIS_HOST', 'localhost'),
                        port=int(os.getenv('REDIS_PORT', 6379)),
                        db=0,
                        decode_responses=True
                    )
                    # Test connection
                    await self.redis_client.ping()
                    logger.info("✅ Redis cache connected")
                except Exception as e:
                    logger.warning(f"⚠️ Redis not available, using local cache only: {e}")
                    self.redis_client = None
            else:
                logger.warning("⚠️ Redis library not installed, using local cache only")
            
            logger.info("✅ Advanced caching system initialized")
            
        except Exception as e:
            logger.error(f"❌ Cache initialization failed: {e}")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache (L1 -> L2 -> None)"""
        try:
            # L1 Cache (local memory) - fastest
            if key in self.local_cache:
                data, expiry = self.local_cache[key]
                if datetime.now() < expiry:
                    self.cache_stats['hits'] += 1
                    self.cache_stats['local_hits'] += 1
                    return data
                else:
                    del self.local_cache[key]
            
            # L2 Cache (Redis) - network but persistent
            if self.redis_client:
                try:
                    cached_data = await self.redis_client.get(key)
                    if cached_data:
                        data = json.loads(cached_data)
                        # Promote to L1 cache
                        self.local_cache[key] = (data, datetime.now() + timedelta(minutes=5))
                        self.cache_stats['hits'] += 1
                        self.cache_stats['redis_hits'] += 1
                        return data
                except Exception as e:
                    logger.warning(f"Redis get failed for {key}: {e}")
            
            # Cache miss
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"Cache get error for {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl_seconds: int = 300):
        """Set value in cache (both L1 and L2)"""
        try:
            # L1 Cache (local memory)
            expiry = datetime.now() + timedelta(seconds=ttl_seconds)
            self.local_cache[key] = (value, expiry)
            
            # L2 Cache (Redis)
            if self.redis_client:
                try:
                    await self.redis_client.setex(
                        key, 
                        ttl_seconds, 
                        json.dumps(value, default=str)
                    )
                except Exception as e:
                    logger.warning(f"Redis set failed for {key}: {e}")
            
        except Exception as e:
            logger.error(f"Cache set error for {key}: {e}")
    
    async def delete(self, key: str):
        """Delete key from all cache levels"""
        try:
            # L1 Cache
            if key in self.local_cache:
                del self.local_cache[key]
            
            # L2 Cache
            if self.redis_client:
                try:
                    await self.redis_client.delete(key)
                except Exception as e:
                    logger.warning(f"Redis delete failed for {key}: {e}")
                    
        except Exception as e:
            logger.error(f"Cache delete error for {key}: {e}")
    
    async def clear_all(self):
        """Clear all caches"""
        try:
            # L1 Cache
            self.local_cache.clear()
            
            # L2 Cache
            if self.redis_client:
                try:
                    await self.redis_client.flushdb()
                except Exception as e:
                    logger.warning(f"Redis clear failed: {e}")
            
            logger.info("✅ All caches cleared")
            
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_requests': total_requests,
            'cache_hits': self.cache_stats['hits'],
            'cache_misses': self.cache_stats['misses'],
            'hit_rate_percent': round(hit_rate, 2),
            'local_hits': self.cache_stats['local_hits'],
            'redis_hits': self.cache_stats['redis_hits'],
            'redis_available': self.redis_client is not None,
            'local_cache_size': len(self.local_cache)
        }

class AtlasLoadBalancer:
    """Simple load balancer for multiple Atlas instances"""
    
    def __init__(self):
        self.instances = []
        self.current_index = 0
        self.health_checks = {}
        
    def add_instance(self, host: str, port: int, weight: int = 1):
        """Add a server instance"""
        instance = {
            'host': host,
            'port': port,
            'weight': weight,
            'healthy': True,
            'url': f"http://{host}:{port}"
        }
        self.instances.append(instance)
        self.health_checks[f"{host}:{port}"] = {
            'last_check': datetime.now(),
            'consecutive_failures': 0
        }
        logger.info(f"✅ Added instance: {host}:{port}")
    
    def get_next_instance(self) -> Optional[Dict[str, Any]]:
        """Get next available instance using round-robin"""
        if not self.instances:
            return None
        
        # Filter healthy instances
        healthy_instances = [i for i in self.instances if i['healthy']]
        
        if not healthy_instances:
            logger.warning("⚠️ No healthy instances available")
            return None
        
        # Round-robin selection
        instance = healthy_instances[self.current_index % len(healthy_instances)]
        self.current_index += 1
        
        return instance
    
    async def health_check_all(self):
        """Perform health checks on all instances"""
        try:
            import aiohttp
            
            for instance in self.instances:
                instance_key = f"{instance['host']}:{instance['port']}"
                
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            f"{instance['url']}/api/v1/health",
                            timeout=aiohttp.ClientTimeout(total=5)
                        ) as response:
                            if response.status == 200:
                                instance['healthy'] = True
                                self.health_checks[instance_key]['consecutive_failures'] = 0
                            else:
                                raise Exception(f"HTTP {response.status}")
                                
                except Exception as e:
                    self.health_checks[instance_key]['consecutive_failures'] += 1
                    
                    # Mark unhealthy after 3 consecutive failures
                    if self.health_checks[instance_key]['consecutive_failures'] >= 3:
                        instance['healthy'] = False
                        logger.warning(f"⚠️ Instance {instance_key} marked unhealthy: {e}")
                
                self.health_checks[instance_key]['last_check'] = datetime.now()
                
        except Exception as e:
            logger.error(f"Health check error: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get load balancer status"""
        healthy_count = len([i for i in self.instances if i['healthy']])
        
        return {
            'total_instances': len(self.instances),
            'healthy_instances': healthy_count,
            'unhealthy_instances': len(self.instances) - healthy_count,
            'instances': [
                {
                    'host': i['host'],
                    'port': i['port'],
                    'healthy': i['healthy'],
                    'weight': i['weight']
                }
                for i in self.instances
            ],
            'current_index': self.current_index
        }

class AtlasPerformanceMonitor:
    """Performance monitoring and optimization"""
    
    def __init__(self):
        self.metrics = {
            'requests_total': 0,
            'requests_per_second': 0,
            'average_response_time': 0,
            'error_rate': 0,
            'active_connections': 0
        }
        self.request_times = []
        self.error_count = 0
        self.start_time = datetime.now()
        
    def record_request(self, response_time: float, success: bool = True):
        """Record a request for performance tracking"""
        self.metrics['requests_total'] += 1
        self.request_times.append(response_time)
        
        if not success:
            self.error_count += 1
        
        # Keep only last 1000 requests for moving average
        if len(self.request_times) > 1000:
            self.request_times = self.request_times[-1000:]
        
        # Update metrics
        self._update_metrics()
    
    def _update_metrics(self):
        """Update performance metrics"""
        if self.request_times:
            self.metrics['average_response_time'] = sum(self.request_times) / len(self.request_times)
        
        # Calculate requests per second
        uptime_seconds = (datetime.now() - self.start_time).total_seconds()
        if uptime_seconds > 0:
            self.metrics['requests_per_second'] = self.metrics['requests_total'] / uptime_seconds
        
        # Calculate error rate
        if self.metrics['requests_total'] > 0:
            self.metrics['error_rate'] = (self.error_count / self.metrics['requests_total']) * 100
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            'requests_total': self.metrics['requests_total'],
            'requests_per_second': round(self.metrics['requests_per_second'], 2),
            'average_response_time_ms': round(self.metrics['average_response_time'] * 1000, 2),
            'error_rate_percent': round(self.metrics['error_rate'], 2),
            'uptime_seconds': (datetime.now() - self.start_time).total_seconds(),
            'memory_usage_mb': self._get_memory_usage(),
            'timestamp': datetime.now().isoformat()
        }
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            return round(process.memory_info().rss / 1024 / 1024, 2)
        except ImportError:
            return 0.0

class AtlasDockerManager:
    """Docker containerization support"""
    
    def __init__(self):
        self.container_info = {
            'containerized': self._is_containerized(),
            'image_name': os.getenv('ATLAS_IMAGE_NAME', 'atlas-v4-enhanced'),
            'version': os.getenv('ATLAS_VERSION', '4.0.0'),
            'environment': os.getenv('ATLAS_ENV', 'development')
        }
    
    def _is_containerized(self) -> bool:
        """Check if running in a Docker container"""
        try:
            # Check for Docker-specific files
            return (
                os.path.exists('/.dockerenv') or
                os.path.exists('/proc/1/cgroup') and 'docker' in open('/proc/1/cgroup').read()
            )
        except:
            return False
    
    def get_container_info(self) -> Dict[str, Any]:
        """Get container information"""
        return {
            'containerized': self.container_info['containerized'],
            'image_name': self.container_info['image_name'],
            'version': self.container_info['version'],
            'environment': self.container_info['environment'],
            'hostname': os.getenv('HOSTNAME', 'unknown'),
            'pod_name': os.getenv('POD_NAME', 'unknown'),
            'node_name': os.getenv('NODE_NAME', 'unknown')
        }
    
    def generate_dockerfile(self) -> str:
        """Generate Dockerfile for Atlas deployment"""
        return """
# Atlas V4 Enhanced - Production Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 atlas && chown -R atlas:atlas /app
USER atlas

# Expose port
EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8002/api/v1/health || exit 1

# Start application
CMD ["python", "atlas_production_server.py"]
        """.strip()
    
    def generate_docker_compose(self) -> str:
        """Generate docker-compose.yml for Atlas deployment"""
        return """
version: '3.8'

services:
  atlas-app:
    build: .
    ports:
      - "8002:8002"
    environment:
      - ATLAS_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - atlas-app
    restart: unless-stopped

volumes:
  redis_data:
        """.strip()

class AtlasInfrastructureManager:
    """Main infrastructure management class"""
    
    def __init__(self):
        self.cache = AtlasAdvancedCache()
        self.load_balancer = AtlasLoadBalancer()
        self.performance_monitor = AtlasPerformanceMonitor()
        self.docker_manager = AtlasDockerManager()
        
    async def initialize(self):
        """Initialize all infrastructure components"""
        try:
            await self.cache.initialize()
            
            # Add default instance if none configured
            if not self.load_balancer.instances:
                self.load_balancer.add_instance('localhost', 8002)
            
            logger.info("✅ Infrastructure manager initialized")
            
        except Exception as e:
            logger.error(f"❌ Infrastructure initialization failed: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        return {
            'cache': self.cache.get_stats(),
            'load_balancer': self.load_balancer.get_status(),
            'performance': self.performance_monitor.get_metrics(),
            'container': self.docker_manager.get_container_info(),
            'timestamp': datetime.now().isoformat()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        try:
            # Check cache
            cache_healthy = True
            try:
                await self.cache.set('health_check', 'ok', 10)
                cache_result = await self.cache.get('health_check')
                cache_healthy = cache_result == 'ok'
            except:
                cache_healthy = False
            
            # Check load balancer
            lb_healthy = len([i for i in self.load_balancer.instances if i['healthy']]) > 0
            
            # Overall health
            overall_healthy = cache_healthy and lb_healthy
            
            return {
                'status': 'healthy' if overall_healthy else 'unhealthy',
                'components': {
                    'cache': 'healthy' if cache_healthy else 'unhealthy',
                    'load_balancer': 'healthy' if lb_healthy else 'unhealthy',
                    'performance_monitor': 'healthy'
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# Global instance
infrastructure_manager = AtlasInfrastructureManager()
