"""
A.T.L.A.S. v5.0 Expanded Stock Universe
Comprehensive multi-cap stock selection with liquidity and quality filters
"""

import logging
import asyncio
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import json

from sp500_symbols import (
    get_expanded_universe_symbols, get_symbols_by_market_cap, 
    get_symbols_by_growth_sector, SP500_SYMBOLS
)

logger = logging.getLogger(__name__)

class MarketCapCategory(Enum):
    """Market capitalization categories"""
    MEGA_CAP = "mega"      # >$200B
    LARGE_CAP = "large"    # $10B - $200B  
    MID_CAP = "mid"        # $2B - $10B
    SMALL_CAP = "small"    # $300M - $2B
    MICRO_CAP = "micro"    # $50M - $300M

class LiquidityTier(Enum):
    """Liquidity classification tiers"""
    ULTRA_HIGH = "ultra_high"    # >10M daily volume
    HIGH = "high"                # 1M - 10M daily volume
    MEDIUM = "medium"            # 100K - 1M daily volume
    LOW = "low"                  # 10K - 100K daily volume
    VERY_LOW = "very_low"        # <10K daily volume

@dataclass
class StockMetrics:
    """Stock quality and liquidity metrics"""
    symbol: str
    market_cap_category: MarketCapCategory
    liquidity_tier: LiquidityTier
    avg_daily_volume: float
    market_cap: Optional[float]
    sector: str
    is_sp500: bool
    quality_score: float  # 0-100 composite score
    last_updated: datetime

class AtlasExpandedUniverse:
    """Manages expanded stock universe with quality filtering"""
    
    def __init__(self):
        self.universe_symbols: Set[str] = set()
        self.stock_metrics: Dict[str, StockMetrics] = {}
        self.quality_filters = {
            'min_daily_volume': 50000,      # Minimum 50K daily volume
            'min_market_cap': 50_000_000,   # Minimum $50M market cap
            'min_quality_score': 60,        # Minimum quality score of 60/100
            'exclude_penny_stocks': True,   # Exclude stocks under $1
            'require_options': False        # Don't require options availability
        }
        self.sector_limits = {
            'technology': 200,
            'healthcare': 150, 
            'financial': 120,
            'consumer_discretionary': 100,
            'industrials': 80,
            'energy': 60,
            'materials': 40,
            'utilities': 30,
            'real_estate': 25,
            'communication_services': 50,
            'consumer_staples': 40
        }
        
    async def initialize_expanded_universe(self) -> Dict[str, Any]:
        """Initialize the expanded stock universe with quality filtering"""
        try:
            logger.info("🚀 Initializing A.T.L.A.S. Expanded Stock Universe...")
            
            # Get base expanded universe
            base_symbols = get_expanded_universe_symbols()
            logger.info(f"📊 Base universe contains {len(base_symbols)} symbols")
            
            # Apply quality filters (simulated for now - would use real data in production)
            filtered_symbols = await self._apply_quality_filters(base_symbols)
            logger.info(f"✅ After quality filtering: {len(filtered_symbols)} symbols")
            
            # Ensure sector diversification
            diversified_symbols = await self._ensure_sector_diversification(filtered_symbols)
            logger.info(f"🎯 After sector diversification: {len(diversified_symbols)} symbols")
            
            # Prioritize by liquidity and quality
            prioritized_symbols = await self._prioritize_symbols(diversified_symbols)
            
            self.universe_symbols = set(prioritized_symbols)
            
            # Generate summary statistics
            summary = await self._generate_universe_summary()
            
            logger.info(f"🎉 Expanded universe initialized with {len(self.universe_symbols)} symbols")
            return summary
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize expanded universe: {e}")
            # Fallback to S&P 500
            self.universe_symbols = set(SP500_SYMBOLS)
            return {"status": "fallback", "symbol_count": len(SP500_SYMBOLS)}
    
    async def _apply_quality_filters(self, symbols: List[str]) -> List[str]:
        """Apply quality and liquidity filters to symbol list"""
        filtered = []
        
        for symbol in symbols:
            # Simulate quality metrics (in production, fetch from APIs)
            quality_score = await self._calculate_quality_score(symbol)
            
            if quality_score >= self.quality_filters['min_quality_score']:
                filtered.append(symbol)
                
                # Store metrics for later use
                self.stock_metrics[symbol] = StockMetrics(
                    symbol=symbol,
                    market_cap_category=self._determine_market_cap_category(symbol),
                    liquidity_tier=self._determine_liquidity_tier(symbol),
                    avg_daily_volume=self._estimate_daily_volume(symbol),
                    market_cap=self._estimate_market_cap(symbol),
                    sector=self._determine_sector(symbol),
                    is_sp500=symbol in SP500_SYMBOLS,
                    quality_score=quality_score,
                    last_updated=datetime.now()
                )
        
        return filtered
    
    async def _ensure_sector_diversification(self, symbols: List[str]) -> List[str]:
        """Ensure proper sector diversification within limits"""
        sector_counts = {}
        diversified = []
        
        # Prioritize S&P 500 stocks first
        sp500_symbols = [s for s in symbols if s in SP500_SYMBOLS]
        other_symbols = [s for s in symbols if s not in SP500_SYMBOLS]
        
        # Process S&P 500 symbols first (always include)
        for symbol in sp500_symbols:
            sector = self._determine_sector(symbol)
            sector_counts[sector] = sector_counts.get(sector, 0) + 1
            diversified.append(symbol)
        
        # Add other symbols within sector limits
        for symbol in other_symbols:
            sector = self._determine_sector(symbol)
            current_count = sector_counts.get(sector, 0)
            sector_limit = self.sector_limits.get(sector, 50)
            
            if current_count < sector_limit:
                diversified.append(symbol)
                sector_counts[sector] = current_count + 1
        
        return diversified
    
    async def _prioritize_symbols(self, symbols: List[str]) -> List[str]:
        """Prioritize symbols by quality score and liquidity"""
        # Sort by quality score (descending) and liquidity
        def priority_key(symbol):
            metrics = self.stock_metrics.get(symbol)
            if not metrics:
                return (0, 0)  # Lowest priority
            
            # Priority factors: quality score, liquidity, S&P 500 membership
            quality_factor = metrics.quality_score
            liquidity_factor = min(metrics.avg_daily_volume / 1000000, 10)  # Cap at 10M
            sp500_bonus = 20 if metrics.is_sp500 else 0
            
            return (quality_factor + liquidity_factor + sp500_bonus, metrics.avg_daily_volume)
        
        return sorted(symbols, key=priority_key, reverse=True)
    
    async def _calculate_quality_score(self, symbol: str) -> float:
        """Calculate composite quality score for symbol (0-100)"""
        # Simulated scoring - in production would use real metrics
        base_score = 70.0
        
        # S&P 500 bonus
        if symbol in SP500_SYMBOLS:
            base_score += 15
        
        # Market cap category bonus
        if symbol in get_symbols_by_market_cap('large'):
            base_score += 10
        elif symbol in get_symbols_by_market_cap('mid'):
            base_score += 5
        
        # Growth sector bonus
        if symbol in get_symbols_by_growth_sector('biotech'):
            base_score += 5
        elif symbol in get_symbols_by_growth_sector('fintech'):
            base_score += 5
        elif symbol in get_symbols_by_growth_sector('clean_energy'):
            base_score += 3
        
        # Add some randomness to simulate real scoring
        import random
        random.seed(hash(symbol))  # Consistent randomness per symbol
        score_variance = random.uniform(-10, 10)
        
        return min(100, max(0, base_score + score_variance))
    
    def _determine_market_cap_category(self, symbol: str) -> MarketCapCategory:
        """Determine market cap category for symbol"""
        if symbol in get_symbols_by_market_cap('large'):
            return MarketCapCategory.LARGE_CAP
        elif symbol in get_symbols_by_market_cap('mid'):
            return MarketCapCategory.MID_CAP
        elif symbol in get_symbols_by_market_cap('small'):
            return MarketCapCategory.SMALL_CAP
        elif symbol in get_symbols_by_market_cap('micro'):
            return MarketCapCategory.MICRO_CAP
        else:
            return MarketCapCategory.LARGE_CAP  # Default for S&P 500
    
    def _determine_liquidity_tier(self, symbol: str) -> LiquidityTier:
        """Determine liquidity tier for symbol"""
        volume = self._estimate_daily_volume(symbol)
        
        if volume >= 10_000_000:
            return LiquidityTier.ULTRA_HIGH
        elif volume >= 1_000_000:
            return LiquidityTier.HIGH
        elif volume >= 100_000:
            return LiquidityTier.MEDIUM
        elif volume >= 10_000:
            return LiquidityTier.LOW
        else:
            return LiquidityTier.VERY_LOW
    
    def _estimate_daily_volume(self, symbol: str) -> float:
        """Estimate daily trading volume for symbol"""
        # Simulated volume estimation - in production would fetch real data
        import random
        random.seed(hash(symbol))
        
        if symbol in SP500_SYMBOLS[:50]:  # Top 50 S&P 500
            return random.uniform(5_000_000, 50_000_000)
        elif symbol in SP500_SYMBOLS:
            return random.uniform(500_000, 5_000_000)
        elif symbol in get_symbols_by_market_cap('mid'):
            return random.uniform(100_000, 2_000_000)
        elif symbol in get_symbols_by_market_cap('small'):
            return random.uniform(50_000, 500_000)
        else:
            return random.uniform(10_000, 200_000)
    
    def _estimate_market_cap(self, symbol: str) -> Optional[float]:
        """Estimate market capitalization for symbol"""
        # Simulated market cap - in production would fetch real data
        import random
        random.seed(hash(symbol))
        
        if symbol in get_symbols_by_market_cap('large'):
            return random.uniform(10_000_000_000, 200_000_000_000)
        elif symbol in get_symbols_by_market_cap('mid'):
            return random.uniform(2_000_000_000, 10_000_000_000)
        elif symbol in get_symbols_by_market_cap('small'):
            return random.uniform(300_000_000, 2_000_000_000)
        elif symbol in get_symbols_by_market_cap('micro'):
            return random.uniform(50_000_000, 300_000_000)
        else:
            return random.uniform(1_000_000_000, 100_000_000_000)
    
    def _determine_sector(self, symbol: str) -> str:
        """Determine sector for symbol"""
        # Simplified sector mapping - in production would use real sector data
        tech_symbols = {'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'ADBE', 'CRM', 'PLTR', 'COIN'}
        healthcare_symbols = {'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'DHR', 'BMY', 'AMGN'}
        financial_symbols = {'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SPGI', 'V', 'MA'}
        
        if symbol in tech_symbols or symbol in get_symbols_by_growth_sector('fintech'):
            return 'technology'
        elif symbol in healthcare_symbols or symbol in get_symbols_by_growth_sector('biotech'):
            return 'healthcare'
        elif symbol in financial_symbols:
            return 'financial'
        elif symbol in get_symbols_by_growth_sector('clean_energy'):
            return 'energy'
        else:
            return 'consumer_discretionary'  # Default
    
    async def _generate_universe_summary(self) -> Dict[str, Any]:
        """Generate comprehensive summary of the expanded universe"""
        total_symbols = len(self.universe_symbols)
        
        # Count by market cap
        market_cap_counts = {}
        for category in MarketCapCategory:
            count = sum(1 for s in self.universe_symbols 
                       if self.stock_metrics.get(s, {}).market_cap_category == category)
            market_cap_counts[category.value] = count
        
        # Count by sector
        sector_counts = {}
        for symbol in self.universe_symbols:
            sector = self._determine_sector(symbol)
            sector_counts[sector] = sector_counts.get(sector, 0) + 1
        
        # Count by liquidity tier
        liquidity_counts = {}
        for tier in LiquidityTier:
            count = sum(1 for s in self.universe_symbols 
                       if self.stock_metrics.get(s, {}).liquidity_tier == tier)
            liquidity_counts[tier.value] = count
        
        return {
            "status": "success",
            "total_symbols": total_symbols,
            "sp500_symbols": sum(1 for s in self.universe_symbols if s in SP500_SYMBOLS),
            "market_cap_distribution": market_cap_counts,
            "sector_distribution": sector_counts,
            "liquidity_distribution": liquidity_counts,
            "avg_quality_score": sum(m.quality_score for m in self.stock_metrics.values()) / len(self.stock_metrics) if self.stock_metrics else 0,
            "last_updated": datetime.now().isoformat()
        }
    
    def get_universe_symbols(self) -> List[str]:
        """Get all symbols in the expanded universe"""
        return sorted(list(self.universe_symbols))
    
    def get_symbols_by_criteria(self, 
                               market_cap: Optional[str] = None,
                               liquidity: Optional[str] = None,
                               sector: Optional[str] = None,
                               min_quality_score: Optional[float] = None) -> List[str]:
        """Get symbols matching specific criteria"""
        filtered = []
        
        for symbol in self.universe_symbols:
            metrics = self.stock_metrics.get(symbol)
            if not metrics:
                continue
            
            # Apply filters
            if market_cap and metrics.market_cap_category.value != market_cap:
                continue
            if liquidity and metrics.liquidity_tier.value != liquidity:
                continue
            if sector and self._determine_sector(symbol) != sector:
                continue
            if min_quality_score and metrics.quality_score < min_quality_score:
                continue
            
            filtered.append(symbol)
        
        return sorted(filtered)

# Global instance
expanded_universe = AtlasExpandedUniverse()

# Convenience functions
async def initialize_expanded_universe():
    """Initialize the expanded universe"""
    return await expanded_universe.initialize_expanded_universe()

def get_expanded_symbols():
    """Get all expanded universe symbols"""
    return expanded_universe.get_universe_symbols()
