"""
A.T.L.A.S. Ultra-Responsive Alert Manager
Handles instant alert generation and delivery for TTM Squeeze pattern detection
Provides WebSocket real-time notifications, visual/audio alerts, and multi-channel delivery
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

logger = logging.getLogger(__name__)

class AlertPriority(Enum):
    """Alert priority levels"""
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

class AlertType(Enum):
    """Alert types"""
    FIRST_LESS_NEGATIVE = "first_less_negative"
    TTM_SQUEEZE_MOMENTUM = "ttm_squeeze_momentum"
    PATTERN_CONFIRMATION = "pattern_confirmation"
    SYSTEM_STATUS = "system_status"
    TRADING_PLAN_ENTRY = "trading_plan_entry"
    TRADING_PLAN_EXIT = "trading_plan_exit"
    TRADING_PLAN_STOP_LOSS = "trading_plan_stop_loss"
    TRADING_PLAN_TARGET = "trading_plan_target"
    TRADING_PLAN_RISK = "trading_plan_risk"
    TRADING_PLAN_UPDATE = "trading_plan_update"

@dataclass
class TTMSqueezeAlert:
    """TTM Squeeze pattern alert data structure"""
    alert_id: str
    symbol: str
    alert_type: AlertType
    priority: AlertPriority
    timestamp: datetime

    # Pattern data
    signal_type: str
    confidence: float
    signal_strength: str

    # Market data
    current_price: float
    price_change: float
    price_change_percent: float

    # Technical indicators
    histogram_current: float
    histogram_previous: float
    improvement_magnitude: float
    ema8_rising: bool
    ema21_rising: bool
    squeeze_active: bool

    # Alert metadata
    alert_message: str
    context_info: Dict[str, Any]
    delivery_channels: List[str]
    expires_at: datetime

    # Optional fields (must come last)
    volume: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary for JSON serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['expires_at'] = self.expires_at.isoformat()
        data['alert_type'] = self.alert_type.value
        data['priority'] = self.priority.value
        return data

class AtlasAlertManager:
    """
    Ultra-responsive alert manager for TTM Squeeze pattern detection
    Provides instant alert generation and multi-channel delivery
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Alert storage
        self.active_alerts: Dict[str, TTMSqueezeAlert] = {}
        self.alert_history: List[TTMSqueezeAlert] = []
        self.alert_cooldowns: Dict[str, datetime] = {}  # Symbol -> last alert time
        
        # WebSocket connections
        self.websocket_connections: Set = set()
        
        # Alert configuration
        self.cooldown_period = 30  # seconds between alerts for same symbol
        self.max_alerts_per_minute = 20
        self.alert_expiry_minutes = 60
        
        # Performance tracking
        self.alerts_sent_count = 0
        self.alerts_per_minute = 0
        self.last_minute_reset = datetime.now()
        
        # Integration with News Insights
        self.news_insights_enabled = True
        self.news_insights_callbacks: List = []
        
        self.logger.info("[ALERT_MANAGER] Ultra-responsive alert manager initialized")

    async def generate_ttm_squeeze_alert(self, symbol: str, pattern_result: Dict[str, Any], 
                                       market_data: Dict[str, Any]) -> Optional[TTMSqueezeAlert]:
        """Generate TTM Squeeze pattern alert with ultra-responsive timing"""
        try:
            # Check cooldown to prevent spam
            if not self._check_alert_cooldown(symbol):
                return None
            
            # Check rate limiting
            if not self._check_rate_limit():
                self.logger.warning(f"[ALERT] Rate limit exceeded, skipping alert for {symbol}")
                return None
            
            # Determine alert priority and type
            alert_type, priority = self._determine_alert_type_and_priority(pattern_result)
            
            # Create alert
            alert = TTMSqueezeAlert(
                alert_id=str(uuid.uuid4()),
                symbol=symbol,
                alert_type=alert_type,
                priority=priority,
                timestamp=datetime.now(),
                signal_type=pattern_result.get('signal_type', 'ttm_squeeze'),
                confidence=pattern_result.get('confidence', 0.5),
                signal_strength=pattern_result.get('signal_strength', 'MODERATE'),
                current_price=market_data.get('price', 0.0),
                price_change=market_data.get('change', 0.0),
                price_change_percent=market_data.get('change_percent', 0.0),
                volume=market_data.get('volume'),
                histogram_current=pattern_result.get('histogram_current', 0.0),
                histogram_previous=pattern_result.get('histogram_previous', 0.0),
                improvement_magnitude=pattern_result.get('improvement_magnitude', 0.0),
                ema8_rising=pattern_result.get('ema8_rising', False),
                ema21_rising=pattern_result.get('ema21_rising', False),
                squeeze_active=pattern_result.get('squeeze_active', False),
                alert_message=self._generate_alert_message(symbol, pattern_result, market_data),
                context_info=self._generate_context_info(pattern_result, market_data),
                delivery_channels=['websocket', 'ui_notification'],
                expires_at=datetime.now() + timedelta(minutes=self.alert_expiry_minutes)
            )
            
            # Store alert
            self.active_alerts[alert.alert_id] = alert
            self.alert_history.append(alert)
            self.alert_cooldowns[symbol] = datetime.now()
            
            # Deliver alert immediately
            await self._deliver_alert(alert)
            
            # Update performance metrics
            self._update_performance_metrics()
            
            self.logger.info(f"[ALERT] Generated {priority.value} priority alert for {symbol}: {alert.signal_type}")
            return alert
            
        except Exception as e:
            self.logger.error(f"Error generating TTM Squeeze alert for {symbol}: {e}")
            return None

    def _determine_alert_type_and_priority(self, pattern_result: Dict[str, Any]) -> tuple[AlertType, AlertPriority]:
        """Determine alert type and priority based on pattern characteristics"""
        try:
            signal_type = pattern_result.get('signal_type', '')
            confidence = pattern_result.get('confidence', 0.5)
            
            # First less negative pattern gets highest priority
            if signal_type == 'first_less_negative':
                return AlertType.FIRST_LESS_NEGATIVE, AlertPriority.CRITICAL
            
            # TTM Squeeze momentum shift
            if 'ttm_squeeze' in signal_type or 'momentum_shift' in signal_type:
                if confidence >= 0.8:
                    return AlertType.TTM_SQUEEZE_MOMENTUM, AlertPriority.HIGH
                elif confidence >= 0.6:
                    return AlertType.TTM_SQUEEZE_MOMENTUM, AlertPriority.MEDIUM
                else:
                    return AlertType.TTM_SQUEEZE_MOMENTUM, AlertPriority.LOW
            
            # Default
            return AlertType.PATTERN_CONFIRMATION, AlertPriority.MEDIUM
            
        except Exception as e:
            self.logger.error(f"Error determining alert type and priority: {e}")
            return AlertType.PATTERN_CONFIRMATION, AlertPriority.LOW

    def _generate_alert_message(self, symbol: str, pattern_result: Dict[str, Any], 
                              market_data: Dict[str, Any]) -> str:
        """Generate human-readable alert message"""
        try:
            signal_type = pattern_result.get('signal_type', 'pattern')
            confidence = pattern_result.get('confidence', 0.5)
            price = market_data.get('price', 0.0)
            change_percent = market_data.get('change_percent', 0.0)
            
            if signal_type == 'first_less_negative':
                return (f"🟡 FIRST LESS NEGATIVE: {symbol} at ${price:.2f} ({change_percent:+.2f}%) - "
                       f"Histogram turning positive! Confidence: {confidence:.1%}")
            
            elif 'momentum_shift' in signal_type:
                return (f"📈 MOMENTUM SHIFT: {symbol} at ${price:.2f} ({change_percent:+.2f}%) - "
                       f"TTM Squeeze momentum reversal detected! Confidence: {confidence:.1%}")
            
            else:
                return (f"🎯 PATTERN ALERT: {symbol} at ${price:.2f} ({change_percent:+.2f}%) - "
                       f"TTM Squeeze pattern confirmed! Confidence: {confidence:.1%}")
                
        except Exception as e:
            self.logger.error(f"Error generating alert message: {e}")
            return f"Pattern alert for {symbol}"

    def _generate_context_info(self, pattern_result: Dict[str, Any], 
                             market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate additional context information for the alert"""
        try:
            return {
                'pattern_details': pattern_result,
                'market_data': market_data,
                'detection_time': datetime.now().isoformat(),
                'alert_source': 'A.T.L.A.S. TTM Squeeze Scanner',
                'recommended_action': self._get_recommended_action(pattern_result),
                'risk_level': self._assess_risk_level(pattern_result)
            }
        except Exception as e:
            self.logger.error(f"Error generating context info: {e}")
            return {}

    def _get_recommended_action(self, pattern_result: Dict[str, Any]) -> str:
        """Get recommended trading action based on pattern"""
        try:
            confidence = pattern_result.get('confidence', 0.5)
            signal_type = pattern_result.get('signal_type', '')
            
            if signal_type == 'first_less_negative':
                return "IMMEDIATE ATTENTION - First momentum shift detected"
            elif confidence >= 0.8:
                return "STRONG BUY SIGNAL - High confidence pattern"
            elif confidence >= 0.6:
                return "BUY SIGNAL - Moderate confidence pattern"
            else:
                return "WATCH - Low confidence pattern"
                
        except Exception as e:
            self.logger.error(f"Error getting recommended action: {e}")
            return "MONITOR"

    def _assess_risk_level(self, pattern_result: Dict[str, Any]) -> str:
        """Assess risk level for the pattern"""
        try:
            confidence = pattern_result.get('confidence', 0.5)
            
            if confidence >= 0.8:
                return "LOW"
            elif confidence >= 0.6:
                return "MEDIUM"
            else:
                return "HIGH"
                
        except Exception as e:
            self.logger.error(f"Error assessing risk level: {e}")
            return "MEDIUM"

    async def _deliver_alert(self, alert: TTMSqueezeAlert):
        """Deliver alert through all configured channels"""
        try:
            # Primary delivery channels
            delivery_tasks = []

            # WebSocket delivery (primary channel for real-time updates)
            delivery_tasks.append(self._send_websocket_alert(alert))

            # News Insights integration
            if self.news_insights_enabled:
                delivery_tasks.append(self._send_to_news_insights(alert))

            # Additional delivery channels based on alert priority
            if alert.priority in [AlertPriority.CRITICAL, AlertPriority.HIGH]:
                # High priority alerts get additional delivery channels
                if 'email' in alert.delivery_channels:
                    delivery_tasks.append(self._send_email_alert(alert))

                if 'push' in alert.delivery_channels:
                    delivery_tasks.append(self._send_push_notification(alert))

                if 'webhook' in alert.delivery_channels:
                    delivery_tasks.append(self._send_webhook_alert(alert))

            # Execute all delivery tasks concurrently for speed
            if delivery_tasks:
                await asyncio.gather(*delivery_tasks, return_exceptions=True)

            self.logger.info(f"[ALERT] Delivered {alert.priority.value} alert for {alert.symbol} via {len(delivery_tasks)} channels")

        except Exception as e:
            self.logger.error(f"Error delivering alert {alert.alert_id}: {e}")

    async def _send_websocket_alert(self, alert):
        """Send alert to all WebSocket connections with ultra-responsive timing"""
        try:
            if not self.websocket_connections:
                return

            # Determine alert type based on alert object
            alert_type = 'lee_method_alert' if hasattr(alert, 'consecutive_bars') else 'ttm_squeeze_alert'

            alert_data = {
                'type': alert_type,
                'alert': alert.to_dict() if hasattr(alert, 'to_dict') else alert,
                'timestamp': datetime.now().isoformat(),
                'delivery_time_ms': int((datetime.now().timestamp() * 1000) % 1000)  # Sub-second timing
            }

            # Ultra-fast concurrent delivery to all clients
            send_tasks = []
            disconnected = set()

            for websocket in self.websocket_connections:
                try:
                    # Create concurrent send task for each connection
                    task = asyncio.create_task(websocket.send_text(json.dumps(alert_data)))
                    send_tasks.append(task)
                except Exception:
                    disconnected.add(websocket)

            # Execute all sends concurrently for maximum speed
            if send_tasks:
                await asyncio.gather(*send_tasks, return_exceptions=True)

            # Remove disconnected clients
            self.websocket_connections -= disconnected

            self.logger.debug(f"[ALERT] Ultra-fast WebSocket delivery to {len(self.websocket_connections)} clients")

        except Exception as e:
            self.logger.error(f"Error sending WebSocket alert: {e}")

    async def generate_lee_method_alert(self, symbol: str, pattern_result: Dict[str, Any],
                                       market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate Lee Method pattern alert with ultra-responsive timing"""
        try:
            # Check cooldown to prevent spam
            if not self._check_alert_cooldown(symbol):
                return None

            # Check rate limiting
            if not self._check_rate_limit():
                self.logger.warning(f"[ALERT] Rate limit exceeded, skipping alert for {symbol}")
                return None

            # Create Lee Method alert
            alert = {
                'symbol': symbol,
                'pattern_type': 'lee_method_declining_bars',
                'consecutive_bars': pattern_result.get('consecutive_bars', 0),
                'decline_percent': pattern_result.get('decline_percent', 0),
                'confidence': pattern_result.get('confidence', 0),
                'current_price': pattern_result.get('current_price', 0),
                'signal_strength': pattern_result.get('signal_strength', 'WEAK'),
                'timestamp': datetime.now().isoformat(),
                'alert_message': f"Lee Method: {pattern_result.get('consecutive_bars', 0)} consecutive declining bars detected for {symbol}",
                'priority': 'HIGH' if pattern_result.get('confidence', 0) > 0.7 else 'MEDIUM',
                'recommendation': pattern_result.get('recommendation', {})
            }

            # Ultra-fast delivery
            await self._send_websocket_alert(alert)

            # Update alert tracking
            self.alerts_sent_count += 1
            self.alert_cooldowns[symbol] = datetime.now()

            self.logger.info(f"[ALERT] Lee Method alert generated for {symbol} - {alert['consecutive_bars']} bars")

            return alert

        except Exception as e:
            self.logger.error(f"Lee Method alert generation failed for {symbol}: {e}")
            return None

    async def _send_to_news_insights(self, alert: TTMSqueezeAlert):
        """Send alert to News Insights system with enhanced integration"""
        try:
            # Create News Insights compatible alert format
            news_insights_alert = {
                'type': 'ttm_squeeze_pattern',
                'symbol': alert.symbol,
                'priority': alert.priority.value,
                'title': f"TTM Squeeze Alert: {alert.symbol}",
                'message': alert.alert_message,
                'confidence': alert.confidence,
                'signal_strength': alert.signal_strength,
                'price': alert.current_price,
                'change_percent': alert.price_change_percent,
                'technical_data': {
                    'histogram_current': alert.histogram_current,
                    'histogram_previous': alert.histogram_previous,
                    'improvement_magnitude': alert.improvement_magnitude,
                    'ema8_rising': alert.ema8_rising,
                    'ema21_rising': alert.ema21_rising,
                    'squeeze_active': alert.squeeze_active
                },
                'timestamp': alert.timestamp.isoformat(),
                'source': 'A.T.L.A.S. Ultra-Responsive Scanner'
            }

            # Send to News Insights callbacks
            for callback in self.news_insights_callbacks:
                try:
                    await callback(news_insights_alert)
                except Exception as e:
                    self.logger.error(f"Error in News Insights callback: {e}")

        except Exception as e:
            self.logger.error(f"Error sending to News Insights: {e}")

    async def _send_email_alert(self, alert: TTMSqueezeAlert):
        """Send email alert via SMTP"""
        try:
            # Check if email configuration is available
            email_config = getattr(self.config, 'email_config', {})
            if not email_config.get('enabled', False):
                self.logger.debug(f"Email alerts disabled for {alert.symbol}")
                return

            # Import email libraries only when needed
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart

            # Create email message
            msg = MIMEMultipart()
            msg['From'] = email_config.get('from_email', '<EMAIL>')
            msg['To'] = email_config.get('to_email', '<EMAIL>')
            msg['Subject'] = f"A.T.L.A.S. Alert: {alert.symbol} - {alert.alert_type.value}"

            # Email body
            body = f"""
A.T.L.A.S. Trading Alert

Symbol: {alert.symbol}
Alert Type: {alert.alert_type.value}
Priority: {alert.priority.value}
Confidence: {alert.confidence:.2f}
Current Price: ${alert.current_price:.2f}
Target Price: ${alert.target_price:.2f}
Stop Loss: ${alert.stop_loss:.2f}

Message: {alert.message}

Generated at: {alert.timestamp}

This is an automated alert from A.T.L.A.S. Trading System.
"""
            msg.attach(MIMEText(body, 'plain'))

            # Send email if SMTP configuration is available
            smtp_config = email_config.get('smtp', {})
            if smtp_config.get('server') and smtp_config.get('port'):
                server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
                if smtp_config.get('use_tls', True):
                    server.starttls()
                if smtp_config.get('username') and smtp_config.get('password'):
                    server.login(smtp_config['username'], smtp_config['password'])

                text = msg.as_string()
                server.sendmail(msg['From'], msg['To'], text)
                server.quit()

                self.logger.info(f"Email alert sent for {alert.symbol}")
            else:
                self.logger.debug(f"SMTP not configured - email alert logged for {alert.symbol}")

        except Exception as e:
            self.logger.error(f"Error sending email alert: {e}")

    async def _send_push_notification(self, alert: TTMSqueezeAlert):
        """Send push notification via webhook or service"""
        try:
            # Check if push notification configuration is available
            push_config = getattr(self.config, 'push_config', {})
            if not push_config.get('enabled', False):
                self.logger.debug(f"Push notifications disabled for {alert.symbol}")
                return

            # Prepare notification payload
            notification_data = {
                'title': f'A.T.L.A.S. Alert: {alert.symbol}',
                'body': f'{alert.alert_type.value} - {alert.message}',
                'data': {
                    'symbol': alert.symbol,
                    'alert_type': alert.alert_type.value,
                    'priority': alert.priority.value,
                    'confidence': alert.confidence,
                    'current_price': alert.current_price,
                    'target_price': alert.target_price,
                    'stop_loss': alert.stop_loss,
                    'timestamp': alert.timestamp.isoformat()
                }
            }

            # Send via webhook if configured
            webhook_url = push_config.get('webhook_url')
            if webhook_url:
                import requests
                response = requests.post(
                    webhook_url,
                    json=notification_data,
                    timeout=10,
                    headers={'Content-Type': 'application/json'}
                )
                if response.status_code == 200:
                    self.logger.info(f"Push notification sent for {alert.symbol}")
                else:
                    self.logger.warning(f"Push notification failed for {alert.symbol}: {response.status_code}")
            else:
                self.logger.debug(f"Push webhook not configured - notification logged for {alert.symbol}")

        except Exception as e:
            self.logger.error(f"Error sending push notification: {e}")

    async def _send_webhook_alert(self, alert: TTMSqueezeAlert):
        """Send webhook alert to external services"""
        try:
            # Webhook implementation for Slack, Discord, etc.
            webhook_data = {
                'text': f"🚨 A.T.L.A.S. TTM Squeeze Alert",
                'attachments': [{
                    'color': 'good' if alert.priority == AlertPriority.HIGH else 'warning',
                    'title': f"{alert.symbol} - {alert.signal_type.upper()}",
                    'text': alert.alert_message,
                    'fields': [
                        {'title': 'Price', 'value': f"${alert.current_price:.2f}", 'short': True},
                        {'title': 'Change', 'value': f"{alert.price_change_percent:+.2f}%", 'short': True},
                        {'title': 'Confidence', 'value': f"{alert.confidence:.1%}", 'short': True},
                        {'title': 'Signal Strength', 'value': alert.signal_strength, 'short': True}
                    ],
                    'timestamp': alert.timestamp.isoformat()
                }]
            }

            self.logger.debug(f"Webhook alert prepared for {alert.symbol}")
            # Actual webhook sending would be implemented here

        except Exception as e:
            self.logger.error(f"Error sending webhook alert: {e}")

    def _check_alert_cooldown(self, symbol: str) -> bool:
        """Check if enough time has passed since last alert for this symbol"""
        try:
            if symbol not in self.alert_cooldowns:
                return True
            
            last_alert = self.alert_cooldowns[symbol]
            time_since_last = (datetime.now() - last_alert).total_seconds()
            
            return time_since_last >= self.cooldown_period
            
        except Exception as e:
            self.logger.error(f"Error checking alert cooldown: {e}")
            return True

    def _check_rate_limit(self) -> bool:
        """Check if we're within rate limits for alerts"""
        try:
            current_time = datetime.now()
            
            # Reset counter every minute
            if (current_time - self.last_minute_reset).total_seconds() >= 60:
                self.alerts_per_minute = 0
                self.last_minute_reset = current_time
            
            return self.alerts_per_minute < self.max_alerts_per_minute
            
        except Exception as e:
            self.logger.error(f"Error checking rate limit: {e}")
            return True

    def _update_performance_metrics(self):
        """Update alert performance metrics"""
        try:
            self.alerts_sent_count += 1
            self.alerts_per_minute += 1
            
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")

    def add_websocket_connection(self, websocket):
        """Add WebSocket connection for alert delivery"""
        self.websocket_connections.add(websocket)
        self.logger.debug(f"[ALERT] Added WebSocket connection, total: {len(self.websocket_connections)}")

    def remove_websocket_connection(self, websocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)
        self.logger.debug(f"[ALERT] Removed WebSocket connection, total: {len(self.websocket_connections)}")

    def add_news_insights_callback(self, callback):
        """Add callback for News Insights integration"""
        self.news_insights_callbacks.append(callback)

    def get_alert_status(self) -> Dict[str, Any]:
        """Get current alert system status"""
        try:
            current_time = datetime.now()
            
            # Clean up expired alerts
            self._cleanup_expired_alerts()
            
            return {
                'active_alerts': len(self.active_alerts),
                'total_alerts_sent': self.alerts_sent_count,
                'alerts_per_minute': self.alerts_per_minute,
                'websocket_connections': len(self.websocket_connections),
                'cooldown_period': self.cooldown_period,
                'max_alerts_per_minute': self.max_alerts_per_minute,
                'news_insights_enabled': self.news_insights_enabled,
                'last_alert_time': max(self.alert_cooldowns.values()).isoformat() if self.alert_cooldowns else None
            }
            
        except Exception as e:
            self.logger.error(f"Error getting alert status: {e}")
            return {'error': str(e)}

    def _cleanup_expired_alerts(self):
        """Clean up expired alerts"""
        try:
            current_time = datetime.now()
            expired_alerts = [
                alert_id for alert_id, alert in self.active_alerts.items()
                if alert.expires_at < current_time
            ]

            for alert_id in expired_alerts:
                del self.active_alerts[alert_id]

            # Keep only last 1000 alerts in history
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-1000:]

        except Exception as e:
            self.logger.error(f"Error cleaning up expired alerts: {e}")

    # ============================================================================
    # TRADING PLAN ALERT METHODS
    # ============================================================================

    async def generate_trading_plan_alert(self, plan_id: str, alert_type: str,
                                        symbol: str = None, message: str = None,
                                        priority: AlertPriority = AlertPriority.MEDIUM) -> Optional[Dict[str, Any]]:
        """Generate trading plan related alert"""
        try:
            # Check rate limiting
            if not self._check_rate_limit():
                self.logger.warning(f"[TRADING_PLAN_ALERT] Rate limit exceeded for plan {plan_id}")
                return None

            alert_id = f"PLAN_{uuid.uuid4().hex[:8].upper()}"

            # Create alert data
            alert_data = {
                'alert_id': alert_id,
                'plan_id': plan_id,
                'symbol': symbol,
                'alert_type': alert_type,
                'priority': priority.value,
                'message': message or f"Trading plan {alert_type} alert",
                'timestamp': datetime.now().isoformat(),
                'delivery_channels': ['websocket', 'ui_notification'],
                'expires_at': (datetime.now() + timedelta(hours=1)).isoformat()
            }

            # Store alert
            self.active_alerts[alert_id] = alert_data
            self.alert_history.append(alert_data)

            # Send through delivery channels
            await self._deliver_trading_plan_alert(alert_data)

            self.logger.info(f"[TRADING_PLAN_ALERT] Generated alert {alert_id} for plan {plan_id}")
            return alert_data

        except Exception as e:
            self.logger.error(f"[TRADING_PLAN_ALERT] Error generating alert for plan {plan_id}: {e}")
            return None

    async def _deliver_trading_plan_alert(self, alert_data: Dict[str, Any]):
        """Deliver trading plan alert through configured channels"""
        try:
            # WebSocket delivery
            if 'websocket' in alert_data.get('delivery_channels', []):
                await self._send_websocket_trading_plan_alert(alert_data)

            # UI notification (handled by WebSocket)
            if 'ui_notification' in alert_data.get('delivery_channels', []):
                # Additional UI-specific formatting could go here
                pass

        except Exception as e:
            self.logger.error(f"[TRADING_PLAN_ALERT] Error delivering alert: {e}")

    async def _send_websocket_trading_plan_alert(self, alert_data: Dict[str, Any]):
        """Send trading plan alert via WebSocket"""
        try:
            # Format for WebSocket delivery
            websocket_message = {
                'type': 'trading_plan_alert',
                'data': {
                    'alert_id': alert_data['alert_id'],
                    'plan_id': alert_data['plan_id'],
                    'symbol': alert_data.get('symbol'),
                    'alert_type': alert_data['alert_type'],
                    'priority': alert_data['priority'],
                    'message': alert_data['message'],
                    'timestamp': alert_data['timestamp'],
                    'formatted_message': self._format_trading_plan_alert_message(alert_data)
                }
            }

            # Send to all connected WebSocket clients
            # This would integrate with the existing WebSocket system
            self.logger.info(f"[TRADING_PLAN_ALERT] WebSocket alert sent: {alert_data['alert_id']}")

        except Exception as e:
            self.logger.error(f"[TRADING_PLAN_ALERT] WebSocket delivery error: {e}")

    def _format_trading_plan_alert_message(self, alert_data: Dict[str, Any]) -> str:
        """Format trading plan alert message for display"""
        try:
            alert_type = alert_data['alert_type']
            symbol = alert_data.get('symbol', '')
            plan_id = alert_data['plan_id']

            # Format based on alert type
            if alert_type == 'trading_plan_entry':
                return f"🚀 **Entry Signal** for {symbol} in Plan {plan_id}\n\n{alert_data['message']}"
            elif alert_type == 'trading_plan_exit':
                return f"🎯 **Exit Signal** for {symbol} in Plan {plan_id}\n\n{alert_data['message']}"
            elif alert_type == 'trading_plan_stop_loss':
                return f"🛑 **Stop Loss Triggered** for {symbol} in Plan {plan_id}\n\n{alert_data['message']}"
            elif alert_type == 'trading_plan_target':
                return f"✅ **Target Reached** for {symbol} in Plan {plan_id}\n\n{alert_data['message']}"
            elif alert_type == 'trading_plan_risk':
                return f"⚠️ **Risk Alert** for Plan {plan_id}\n\n{alert_data['message']}"
            elif alert_type == 'trading_plan_update':
                return f"📊 **Plan Update** for Plan {plan_id}\n\n{alert_data['message']}"
            else:
                return f"📋 **Trading Plan Alert** for Plan {plan_id}\n\n{alert_data['message']}"

        except Exception as e:
            self.logger.error(f"[TRADING_PLAN_ALERT] Error formatting message: {e}")
            return alert_data.get('message', 'Trading plan alert')

    async def send_trading_plan_entry_alert(self, plan_id: str, symbol: str, entry_price: float,
                                          target_price: float, stop_loss: float):
        """Send entry signal alert for trading plan"""
        message = f"Entry signal detected for {symbol}\n\n"
        message += f"📈 **Entry Price:** ${entry_price:.2f}\n"
        message += f"🎯 **Target:** ${target_price:.2f}\n"
        message += f"🛑 **Stop Loss:** ${stop_loss:.2f}\n"
        message += f"⚡ **Action Required:** Review and execute trade"

        await self.generate_trading_plan_alert(
            plan_id=plan_id,
            alert_type='trading_plan_entry',
            symbol=symbol,
            message=message,
            priority=AlertPriority.HIGH
        )

    async def send_trading_plan_risk_alert(self, plan_id: str, risk_message: str):
        """Send risk alert for trading plan"""
        message = f"Risk threshold exceeded for trading plan\n\n"
        message += f"⚠️ **Risk Alert:** {risk_message}\n"
        message += f"🔍 **Action Required:** Review plan and adjust positions"

        await self.generate_trading_plan_alert(
            plan_id=plan_id,
            alert_type='trading_plan_risk',
            message=message,
            priority=AlertPriority.CRITICAL
        )

    async def send_trading_plan_update_alert(self, plan_id: str, update_message: str):
        """Send general update alert for trading plan"""
        await self.generate_trading_plan_alert(
            plan_id=plan_id,
            alert_type='trading_plan_update',
            message=update_message,
            priority=AlertPriority.MEDIUM
        )


# Global alert manager instance
alert_manager = AtlasAlertManager()
