# 🐳 A.T.L.A.S. Docker Compose - Development & Testing Environment
version: '3.8'

services:
  # ===== MAIN APPLICATION =====
  atlas-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: atlas-trading-system
    ports:
      - "8001:8001"  # Main API
      - "8000:8000"  # Metrics/Health
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=*****************************************/atlas_dev
      - PROMETHEUS_PORT=8001
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./:/app
      - atlas_logs:/app/logs
      - atlas_cache:/app/cache
    networks:
      - atlas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ===== DATABASE =====
  postgres:
    image: postgres:15-alpine
    container_name: atlas-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=atlas_dev
      - POSTGRES_USER=atlas
      - POSTGRES_PASSWORD=atlas123
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - atlas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U atlas -d atlas_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ===== REDIS CACHE =====
  redis:
    image: redis:7-alpine
    container_name: atlas-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    networks:
      - atlas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ===== TIME SERIES DATABASE =====
  influxdb:
    image: influxdb:2.7-alpine
    container_name: atlas-influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=atlas
      - DOCKER_INFLUXDB_INIT_PASSWORD=atlas123
      - DOCKER_INFLUXDB_INIT_ORG=atlas-trading
      - DOCKER_INFLUXDB_INIT_BUCKET=market-data
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=atlas-admin-token-12345
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - atlas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== MONITORING =====
  prometheus:
    image: prom/prometheus:latest
    container_name: atlas-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - atlas-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: atlas-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=atlas123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
      - influxdb
    networks:
      - atlas-network
    restart: unless-stopped

  # ===== MESSAGE QUEUE =====
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: atlas-kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    depends_on:
      - zookeeper
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - atlas-network
    restart: unless-stopped

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: atlas-zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
    networks:
      - atlas-network
    restart: unless-stopped

  # ===== ELASTICSEARCH & KIBANA =====
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: atlas-elasticsearch
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - atlas-network
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: atlas-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - atlas-network
    restart: unless-stopped

  # ===== NGINX REVERSE PROXY =====
  nginx:
    image: nginx:alpine
    container_name: atlas-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - atlas-app
    networks:
      - atlas-network
    restart: unless-stopped

  # ===== TESTING SERVICES =====
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: testing
    container_name: atlas-test-runner
    environment:
      - ENVIRONMENT=testing
      - DATABASE_URL=*****************************************/atlas_test
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./:/app
      - test_reports:/app/test-reports
    networks:
      - atlas-network
    profiles:
      - testing
    command: ["pytest", "tests/", "-v", "--cov=./", "--cov-report=html:/app/test-reports/coverage"]

# ===== VOLUMES =====
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  influxdb_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  atlas_logs:
    driver: local
  atlas_cache:
    driver: local
  test_reports:
    driver: local

# ===== NETWORKS =====
networks:
  atlas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
