"""
Test script for A.T.L.A.S. v5.0 Expanded Stock Universe
Validates the expanded universe implementation and performance
"""

import asyncio
import logging
import time
from datetime import datetime
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_expanded_universe():
    """Test the expanded stock universe functionality"""
    
    print("=" * 80)
    print("🚀 A.T.L.A.S. v5.0 EXPANDED STOCK UNIVERSE TEST")
    print("=" * 80)
    
    try:
        # Test 1: Basic symbol lists
        print("\n📊 TEST 1: Basic Symbol Lists")
        print("-" * 40)
        
        from sp500_symbols import (
            get_symbol_count, get_expanded_universe_symbols,
            get_symbols_by_market_cap, get_symbols_by_growth_sector
        )
        
        # Get counts
        counts = get_symbol_count()
        print(f"✅ S&P 500 symbols: {counts['total_sp500']}")
        print(f"✅ Core S&P 500: {counts['core_sp500']}")
        print(f"✅ High volume: {counts['high_volume']}")
        
        # Get expanded universe
        expanded_symbols = get_expanded_universe_symbols()
        print(f"✅ Expanded universe: {len(expanded_symbols)} symbols")
        
        # Test market cap categories
        large_cap = get_symbols_by_market_cap('large')
        mid_cap = get_symbols_by_market_cap('mid')
        small_cap = get_symbols_by_market_cap('small')
        micro_cap = get_symbols_by_market_cap('micro')
        
        print(f"✅ Large-cap: {len(large_cap)} symbols")
        print(f"✅ Mid-cap: {len(mid_cap)} symbols")
        print(f"✅ Small-cap: {len(small_cap)} symbols")
        print(f"✅ Micro-cap: {len(micro_cap)} symbols")
        
        # Test growth sectors
        biotech = get_symbols_by_growth_sector('biotech')
        fintech = get_symbols_by_growth_sector('fintech')
        clean_energy = get_symbols_by_growth_sector('clean_energy')
        
        print(f"✅ Biotech: {len(biotech)} symbols")
        print(f"✅ Fintech: {len(fintech)} symbols")
        print(f"✅ Clean Energy: {len(clean_energy)} symbols")
        
    except Exception as e:
        print(f"❌ TEST 1 FAILED: {e}")
        return False
    
    try:
        # Test 2: Expanded Universe Module
        print("\n🎯 TEST 2: Expanded Universe Module")
        print("-" * 40)
        
        from atlas_expanded_universe import AtlasExpandedUniverse, initialize_expanded_universe
        
        # Initialize expanded universe
        start_time = time.time()
        summary = await initialize_expanded_universe()
        init_time = time.time() - start_time
        
        print(f"✅ Initialization time: {init_time:.2f} seconds")
        print(f"✅ Status: {summary.get('status', 'unknown')}")
        print(f"✅ Total symbols: {summary.get('total_symbols', 0)}")
        print(f"✅ S&P 500 symbols: {summary.get('sp500_symbols', 0)}")
        print(f"✅ Average quality score: {summary.get('avg_quality_score', 0):.1f}")
        
        # Test market cap distribution
        market_cap_dist = summary.get('market_cap_distribution', {})
        print(f"✅ Market cap distribution:")
        for cap_type, count in market_cap_dist.items():
            print(f"   - {cap_type}: {count} symbols")
        
        # Test sector distribution
        sector_dist = summary.get('sector_distribution', {})
        print(f"✅ Sector distribution:")
        for sector, count in sorted(sector_dist.items()):
            print(f"   - {sector}: {count} symbols")
        
        # Test liquidity distribution
        liquidity_dist = summary.get('liquidity_distribution', {})
        print(f"✅ Liquidity distribution:")
        for tier, count in liquidity_dist.items():
            print(f"   - {tier}: {count} symbols")
        
    except Exception as e:
        print(f"❌ TEST 2 FAILED: {e}")
        return False
    
    try:
        # Test 3: Symbol Manager Integration
        print("\n🔧 TEST 3: Symbol Manager Integration")
        print("-" * 40)
        
        from atlas_enhanced_symbol_manager import EnhancedSymbolManager

        # Initialize symbol manager
        symbol_manager = EnhancedSymbolManager()
        await symbol_manager.initialize()
        
        # Get symbol counts
        all_symbols = symbol_manager.get_all_active_symbols()
        from atlas_enhanced_symbol_manager import SymbolTier
        ultra_priority = symbol_manager.get_symbols_by_tier(SymbolTier.ULTRA_PRIORITY)

        print(f"✅ Symbol manager initialized")
        print(f"✅ Total active symbols: {len(all_symbols)}")
        print(f"✅ Ultra priority symbols: {len(ultra_priority)}")

        # Test symbol metrics
        if all_symbols:
            sample_symbols = list(all_symbols)[:10]
            print(f"✅ Sample symbols: {sample_symbols}")

            for symbol in sample_symbols[:5]:
                tier = symbol_manager.get_symbol_tier(symbol)
                if tier:
                    print(f"   - {symbol}: Tier={tier.name}")
        else:
            print("⚠️ No active symbols found in symbol manager")
        
    except Exception as e:
        print(f"❌ TEST 3 FAILED: {e}")
        return False
    
    try:
        # Test 4: Scanner Integration
        print("\n📡 TEST 4: Scanner Integration")
        print("-" * 40)
        
        from atlas_enhanced_realtime_scanner import EnhancedRealtimeScanner

        # Initialize scanner
        scanner = EnhancedRealtimeScanner()
        await scanner.initialize()
        
        # Check symbol lists
        if hasattr(scanner, 'priority_symbols'):
            print(f"✅ Priority symbols: {len(scanner.priority_symbols)}")
        if hasattr(scanner, 'watchlist_symbols'):
            print(f"✅ Watchlist symbols: {len(scanner.watchlist_symbols)}")
        if hasattr(scanner, 'extended_symbols'):
            print(f"✅ Extended symbols: {len(scanner.extended_symbols)}")
        if hasattr(scanner, 'batch_symbols'):
            print(f"✅ Scanning batches: {len(scanner.batch_symbols)}")
            if scanner.batch_symbols:
                avg_batch_size = sum(len(batch) for batch in scanner.batch_symbols) / len(scanner.batch_symbols)
                print(f"✅ Average batch size: {avg_batch_size:.1f} symbols")
        
        # Test scanner status
        status = await scanner.get_scanner_status()
        print(f"✅ Scanner status: {status.get('status', 'unknown')}")
        
    except Exception as e:
        print(f"❌ TEST 4 FAILED: {e}")
        return False
    
    try:
        # Test 5: Performance Analysis
        print("\n⚡ TEST 5: Performance Analysis")
        print("-" * 40)
        
        from atlas_expanded_universe import expanded_universe
        
        # Test symbol filtering performance
        start_time = time.time()
        high_quality = expanded_universe.get_symbols_by_criteria(min_quality_score=80)
        filter_time = time.time() - start_time
        
        print(f"✅ High quality symbols (>80): {len(high_quality)}")
        print(f"✅ Filtering time: {filter_time:.3f} seconds")
        
        # Test market cap filtering
        start_time = time.time()
        large_cap_filtered = expanded_universe.get_symbols_by_criteria(market_cap='large')
        mid_cap_filtered = expanded_universe.get_symbols_by_criteria(market_cap='mid')
        filter_time = time.time() - start_time
        
        print(f"✅ Large-cap filtered: {len(large_cap_filtered)}")
        print(f"✅ Mid-cap filtered: {len(mid_cap_filtered)}")
        print(f"✅ Market cap filtering time: {filter_time:.3f} seconds")
        
        # Test sector filtering
        start_time = time.time()
        tech_symbols = expanded_universe.get_symbols_by_criteria(sector='technology')
        healthcare_symbols = expanded_universe.get_symbols_by_criteria(sector='healthcare')
        filter_time = time.time() - start_time
        
        print(f"✅ Technology symbols: {len(tech_symbols)}")
        print(f"✅ Healthcare symbols: {len(healthcare_symbols)}")
        print(f"✅ Sector filtering time: {filter_time:.3f} seconds")
        
    except Exception as e:
        print(f"❌ TEST 5 FAILED: {e}")
        return False
    
    # Final summary
    print("\n" + "=" * 80)
    print("🎉 ALL TESTS PASSED - EXPANDED UNIVERSE READY!")
    print("=" * 80)
    
    print(f"\n📈 EXPANSION SUMMARY:")
    print(f"   • Original S&P 500 coverage: ~350 symbols")
    print(f"   • Expanded universe coverage: {summary.get('total_symbols', 0)} symbols")
    print(f"   • Expansion factor: {summary.get('total_symbols', 0) / 350:.1f}x")
    print(f"   • Quality-filtered symbols with liquidity requirements")
    print(f"   • Multi-cap coverage: Large, Mid, Small, Micro")
    print(f"   • Sector diversification across 11 GICS sectors")
    print(f"   • Growth sector focus: Biotech, Fintech, Clean Energy")
    
    print(f"\n🚀 READY FOR PRODUCTION:")
    print(f"   • API capacity supports {summary.get('total_symbols', 0)} symbols")
    print(f"   • Intelligent batching for efficient scanning")
    print(f"   • Quality scoring and liquidity filtering")
    print(f"   • Backward compatibility with existing systems")
    
    return True

async def main():
    """Main test function"""
    try:
        success = await test_expanded_universe()
        if success:
            print(f"\n✅ Test completed successfully!")
            return 0
        else:
            print(f"\n❌ Test failed!")
            return 1
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
