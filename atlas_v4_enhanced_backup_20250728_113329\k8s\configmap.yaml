apiVersion: v1
kind: ConfigMap
metadata:
  name: atlas-config
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: config
data:
  # Application configuration
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  PYTHONPATH: "/app"
  PYTHONUNBUFFERED: "1"
  PYTHONDONTWRITEBYTECODE: "1"
  
  # Server configuration
  HOST: "0.0.0.0"
  PORT: "8001"
  WORKERS: "4"
  
  # Multi-agent system configuration
  ORCHESTRATION_MODE: "hybrid"
  MAX_CONCURRENT_REQUESTS: "100"
  AGENT_TIMEOUT_SECONDS: "300"
  
  # Monitoring configuration
  PROMETHEUS_PORT: "8000"
  HEALTH_CHECK_INTERVAL: "30"
  METRICS_COLLECTION_INTERVAL: "60"
  
  # Security configuration
  SESSION_TIMEOUT: "3600"
  MAX_FAILED_ATTEMPTS: "5"
  RATE_LIMIT_WINDOW: "60"
  MAX_REQUESTS_PER_WINDOW: "100"
  
  # Database configuration (if using)
  DB_POOL_SIZE: "20"
  DB_MAX_OVERFLOW: "30"
  DB_POOL_TIMEOUT: "30"
  
  # Cache configuration
  CACHE_TTL: "300"
  CACHE_MAX_SIZE: "10000"
  
  # Trading configuration
  MAX_POSITION_SIZE: "0.1"
  MAX_DAILY_VAR: "0.02"
  COMPLIANCE_CHECK_ENABLED: "true"
  
  # Grok integration
  GROK_TIMEOUT: "30"
  GROK_MAX_RETRIES: "3"
  
  # Logging configuration
  log_config.yaml: |
    version: 1
    disable_existing_loggers: false
    formatters:
      default:
        format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
      detailed:
        format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s'
      json:
        format: '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d}'
    handlers:
      console:
        class: logging.StreamHandler
        level: INFO
        formatter: json
        stream: ext://sys.stdout
      file:
        class: logging.handlers.RotatingFileHandler
        level: DEBUG
        formatter: detailed
        filename: /app/logs/atlas.log
        maxBytes: 10485760  # 10MB
        backupCount: 5
    loggers:
      atlas:
        level: DEBUG
        handlers: [console, file]
        propagate: false
      uvicorn:
        level: INFO
        handlers: [console]
        propagate: false
      fastapi:
        level: INFO
        handlers: [console]
        propagate: false
    root:
      level: INFO
      handlers: [console]
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: atlas-nginx-config
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: nginx
data:
  nginx.conf: |
    upstream atlas_backend {
        least_conn;
        server atlas-service:8001 max_fails=3 fail_timeout=30s;
    }
    
    upstream prometheus_backend {
        server atlas-service:8000 max_fails=3 fail_timeout=30s;
    }
    
    server {
        listen 80;
        server_name _;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=metrics:10m rate=1r/s;
        
        # Main API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://atlas_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Health check endpoint
        location /health {
            proxy_pass http://atlas_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            access_log off;
        }
        
        # Metrics endpoint (restricted access)
        location /metrics {
            limit_req zone=metrics burst=5 nodelay;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            proxy_pass http://prometheus_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # Static files (if any)
        location /static/ {
            alias /app/static/;
            expires 1d;
            add_header Cache-Control "public, immutable";
        }
        
        # Default location
        location / {
            return 404;
        }
    }
