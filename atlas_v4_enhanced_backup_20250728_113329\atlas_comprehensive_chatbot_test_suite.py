#!/usr/bin/env python3
"""
Atlas V5 Enhanced Chatbot Comprehensive Test Suite
Tests all functionality described in README.md through web interface
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AtlasChatbotTestSuite:
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.test_results = []
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def send_chat_message(self, message: str, timeout: int = 30) -> Dict[str, Any]:
        """Send a chat message and get response"""
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/chat",
                json={"message": message},
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"HTTP {response.status}", "status": "failed"}
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    async def test_basic_connectivity(self) -> Dict[str, Any]:
        """Test basic server connectivity"""
        logger.info("Testing basic connectivity...")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    return {"status": "passed", "message": "Server connectivity OK"}
                else:
                    return {"status": "failed", "message": f"HTTP {response.status}"}
        except Exception as e:
            return {"status": "failed", "message": f"Connection error: {str(e)}"}
    
    async def test_beginner_friendly_queries(self) -> List[Dict[str, Any]]:
        """Test beginner-friendly queries from README examples"""
        logger.info("Testing beginner-friendly queries...")
        
        test_cases = [
            {
                "name": "Profit Target Query",
                "message": "Make me $100 today",
                "expected_features": ["trading_plan", "profit_target", "risk_management"],
                "timeout": 45
            },
            {
                "name": "Learning Request",
                "message": "What is RSI and how does it work?",
                "expected_features": ["educational", "technical_indicators", "beginner_friendly"],
                "timeout": 30
            },
            {
                "name": "General Trading Advice",
                "message": "I'm new to trading, where should I start?",
                "expected_features": ["educational", "beginner_guidance", "risk_warnings"],
                "timeout": 30
            }
        ]
        
        results = []
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['name']}")
            start_time = time.time()
            
            response = await self.send_chat_message(
                test_case["message"], 
                timeout=test_case["timeout"]
            )
            
            response_time = time.time() - start_time
            
            # Analyze response quality
            result = {
                "test_name": test_case["name"],
                "message": test_case["message"],
                "response_time": response_time,
                "status": "passed" if "error" not in response else "failed",
                "response": response,
                "expected_features": test_case["expected_features"],
                "analysis": self.analyze_response_quality(response, test_case["expected_features"])
            }
            
            results.append(result)
            
            # Wait between requests
            await asyncio.sleep(2)
        
        return results
    
    async def test_trading_plan_generation(self) -> List[Dict[str, Any]]:
        """Test trading plan generation with specific amounts and timeframes"""
        logger.info("Testing trading plan generation...")
        
        test_cases = [
            {
                "name": "Specific Dollar Target",
                "message": "I want to make $5,000 in 30 days with moderate risk",
                "expected_features": ["trading_plan", "specific_targets", "risk_assessment", "timeframe"],
                "timeout": 60
            },
            {
                "name": "Conservative Plan",
                "message": "Create a conservative trading plan for $1,000 profit in 2 weeks",
                "expected_features": ["trading_plan", "conservative_approach", "position_sizing"],
                "timeout": 60
            },
            {
                "name": "Aggressive Plan",
                "message": "I need to make $10,000 in 1 week, what's the plan?",
                "expected_features": ["trading_plan", "risk_warnings", "aggressive_strategy"],
                "timeout": 60
            }
        ]
        
        results = []
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['name']}")
            start_time = time.time()
            
            response = await self.send_chat_message(
                test_case["message"], 
                timeout=test_case["timeout"]
            )
            
            response_time = time.time() - start_time
            
            result = {
                "test_name": test_case["name"],
                "message": test_case["message"],
                "response_time": response_time,
                "status": "passed" if "error" not in response else "failed",
                "response": response,
                "expected_features": test_case["expected_features"],
                "analysis": self.analyze_response_quality(response, test_case["expected_features"])
            }
            
            results.append(result)
            await asyncio.sleep(3)
        
        return results
    
    async def test_lee_method_scanning(self) -> List[Dict[str, Any]]:
        """Test Lee Method signal scanning and analysis"""
        logger.info("Testing Lee Method scanning...")
        
        test_cases = [
            {
                "name": "General Lee Method Scan",
                "message": "Scan for Lee Method signals",
                "expected_features": ["lee_method", "pattern_detection", "signal_strength"],
                "timeout": 45
            },
            {
                "name": "S&P 500 Scan",
                "message": "Scan the S&P 500 for stocks ready to pop",
                "expected_features": ["market_scanning", "pattern_detection", "opportunity_identification"],
                "timeout": 60
            },
            {
                "name": "TTM Squeeze Scan",
                "message": "Find TTM Squeeze patterns in tech stocks",
                "expected_features": ["ttm_squeeze", "sector_specific", "technical_patterns"],
                "timeout": 45
            }
        ]
        
        results = []
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['name']}")
            start_time = time.time()
            
            response = await self.send_chat_message(
                test_case["message"], 
                timeout=test_case["timeout"]
            )
            
            response_time = time.time() - start_time
            
            result = {
                "test_name": test_case["name"],
                "message": test_case["message"],
                "response_time": response_time,
                "status": "passed" if "error" not in response else "failed",
                "response": response,
                "expected_features": test_case["expected_features"],
                "analysis": self.analyze_response_quality(response, test_case["expected_features"])
            }
            
            results.append(result)
            await asyncio.sleep(3)
        
        return results
    
    async def test_stock_analysis(self) -> List[Dict[str, Any]]:
        """Test stock analysis requests (single and multi-symbol)"""
        logger.info("Testing stock analysis...")
        
        test_cases = [
            {
                "name": "Single Stock Analysis",
                "message": "Analyze AAPL for a potential trade",
                "expected_features": ["6_point_format", "stock_analysis", "trading_recommendation"],
                "timeout": 45
            },
            {
                "name": "Multi-Symbol Analysis",
                "message": "Compare AAPL, MSFT, and GOOGL for best trading opportunity",
                "expected_features": ["multi_symbol", "comparative_analysis", "ranking"],
                "timeout": 60
            },
            {
                "name": "Options Analysis",
                "message": "Should I buy TSLA calls or puts?",
                "expected_features": ["options_analysis", "strategy_recommendation", "risk_assessment"],
                "timeout": 45
            }
        ]
        
        results = []
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['name']}")
            start_time = time.time()
            
            response = await self.send_chat_message(
                test_case["message"], 
                timeout=test_case["timeout"]
            )
            
            response_time = time.time() - start_time
            
            result = {
                "test_name": test_case["name"],
                "message": test_case["message"],
                "response_time": response_time,
                "status": "passed" if "error" not in response else "failed",
                "response": response,
                "expected_features": test_case["expected_features"],
                "analysis": self.analyze_response_quality(response, test_case["expected_features"])
            }
            
            results.append(result)
            await asyncio.sleep(3)
        
        return results
    
    def analyze_response_quality(self, response: Dict[str, Any], expected_features: List[str]) -> Dict[str, Any]:
        """Analyze response quality and feature compliance"""
        if "error" in response:
            return {
                "quality_score": 0,
                "feature_compliance": 0,
                "issues": ["Response contains error"],
                "strengths": []
            }
        
        response_text = str(response).lower()
        issues = []
        strengths = []
        feature_score = 0
        
        # Check for expected features
        for feature in expected_features:
            if any(keyword in response_text for keyword in self.get_feature_keywords(feature)):
                feature_score += 1
                strengths.append(f"Contains {feature} content")
            else:
                issues.append(f"Missing {feature} content")
        
        feature_compliance = (feature_score / len(expected_features)) * 100 if expected_features else 100
        
        # Check for 6-point format compliance
        six_point_indicators = ["1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣"]
        six_point_score = sum(1 for indicator in six_point_indicators if indicator in str(response))
        
        if six_point_score >= 4:
            strengths.append("Good 6-point format compliance")
        elif six_point_score > 0:
            strengths.append("Partial 6-point format compliance")
        
        # Calculate overall quality score
        quality_score = min(100, (feature_compliance + (six_point_score * 10)) / 2)
        
        return {
            "quality_score": quality_score,
            "feature_compliance": feature_compliance,
            "six_point_score": six_point_score,
            "issues": issues,
            "strengths": strengths
        }
    
    def get_feature_keywords(self, feature: str) -> List[str]:
        """Get keywords to look for based on feature type"""
        keyword_map = {
            "trading_plan": ["plan", "strategy", "target", "profit", "risk"],
            "profit_target": ["$", "profit", "target", "make", "earn"],
            "risk_management": ["risk", "stop", "loss", "management", "protection"],
            "educational": ["learn", "explain", "what is", "how", "education"],
            "technical_indicators": ["rsi", "macd", "indicator", "technical", "analysis"],
            "beginner_friendly": ["beginner", "start", "basic", "simple", "guide"],
            "6_point_format": ["1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣"],
            "lee_method": ["lee method", "pattern", "criteria", "signal"],
            "pattern_detection": ["pattern", "detection", "signal", "scan"],
            "signal_strength": ["strength", "confidence", "probability", "score"],
            "market_scanning": ["scan", "screening", "opportunities", "search"],
            "stock_analysis": ["analysis", "analyze", "recommendation", "trade"],
            "options_analysis": ["options", "calls", "puts", "strategy", "greeks"],
            "multi_symbol": ["compare", "vs", "versus", "multiple", "symbols"]
        }
        return keyword_map.get(feature, [feature])

    async def test_enhanced_features(self) -> List[Dict[str, Any]]:
        """Test enhanced features like intent detection, Grok integration, etc."""
        logger.info("Testing enhanced features...")

        test_cases = [
            {
                "name": "Intent Detection Test",
                "message": "Should I buy NVDA now?",
                "expected_features": ["intent_detection", "stock_analysis", "confidence_scoring"],
                "timeout": 30
            },
            {
                "name": "Grok Integration Test",
                "message": "What's the latest news on Tesla affecting stock price?",
                "expected_features": ["grok_integration", "news_analysis", "real_time_data"],
                "timeout": 45
            },
            {
                "name": "Beginner Grok System Test",
                "message": "Explain options trading like I'm 5 years old",
                "expected_features": ["beginner_grok", "educational", "simple_explanation"],
                "timeout": 30
            }
        ]

        results = []
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['name']}")
            start_time = time.time()

            response = await self.send_chat_message(
                test_case["message"],
                timeout=test_case["timeout"]
            )

            response_time = time.time() - start_time

            result = {
                "test_name": test_case["name"],
                "message": test_case["message"],
                "response_time": response_time,
                "status": "passed" if "error" not in response else "failed",
                "response": response,
                "expected_features": test_case["expected_features"],
                "analysis": self.analyze_response_quality(response, test_case["expected_features"])
            }

            results.append(result)
            await asyncio.sleep(2)

        return results

    async def test_system_integrations(self) -> List[Dict[str, Any]]:
        """Test system integrations and API connectivity"""
        logger.info("Testing system integrations...")

        test_cases = [
            {
                "name": "API Connectivity Test",
                "message": "Get current price for AAPL",
                "expected_features": ["real_time_data", "api_integration", "current_price"],
                "timeout": 30
            },
            {
                "name": "Error Handling Test",
                "message": "Analyze INVALIDTICKER123",
                "expected_features": ["error_handling", "graceful_degradation", "user_feedback"],
                "timeout": 30
            },
            {
                "name": "Performance Test",
                "message": "Quick analysis of SPY",
                "expected_features": ["fast_response", "performance", "efficiency"],
                "timeout": 15
            }
        ]

        results = []
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['name']}")
            start_time = time.time()

            response = await self.send_chat_message(
                test_case["message"],
                timeout=test_case["timeout"]
            )

            response_time = time.time() - start_time

            result = {
                "test_name": test_case["name"],
                "message": test_case["message"],
                "response_time": response_time,
                "status": "passed" if "error" not in response else "failed",
                "response": response,
                "expected_features": test_case["expected_features"],
                "analysis": self.analyze_response_quality(response, test_case["expected_features"])
            }

            results.append(result)
            await asyncio.sleep(2)

        return results

    async def run_comprehensive_test_suite(self) -> Dict[str, Any]:
        """Run the complete test suite"""
        logger.info("Starting comprehensive chatbot test suite...")

        start_time = datetime.now()
        all_results = {}

        # Test basic connectivity first
        connectivity_result = await self.test_basic_connectivity()
        all_results["connectivity"] = connectivity_result

        if connectivity_result["status"] != "passed":
            logger.error("Basic connectivity failed, aborting test suite")
            return {
                "status": "failed",
                "message": "Basic connectivity failed",
                "results": all_results,
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat()
            }

        # Run all test categories
        test_categories = [
            ("beginner_queries", self.test_beginner_friendly_queries),
            ("trading_plans", self.test_trading_plan_generation),
            ("lee_method", self.test_lee_method_scanning),
            ("stock_analysis", self.test_stock_analysis),
            ("enhanced_features", self.test_enhanced_features),
            ("system_integrations", self.test_system_integrations)
        ]

        for category_name, test_method in test_categories:
            try:
                logger.info(f"Running {category_name} tests...")
                results = await test_method()
                all_results[category_name] = results

                # Brief pause between test categories
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"Error in {category_name} tests: {str(e)}")
                all_results[category_name] = {
                    "status": "failed",
                    "error": str(e)
                }

        end_time = datetime.now()

        # Generate summary
        summary = self.generate_test_summary(all_results, start_time, end_time)

        return {
            "status": "completed",
            "summary": summary,
            "detailed_results": all_results,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "total_duration": (end_time - start_time).total_seconds()
        }

    def generate_test_summary(self, results: Dict[str, Any], start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """Generate a comprehensive test summary"""
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        total_response_time = 0
        quality_scores = []

        for category, category_results in results.items():
            if category == "connectivity":
                total_tests += 1
                if category_results.get("status") == "passed":
                    passed_tests += 1
                else:
                    failed_tests += 1
                continue

            if isinstance(category_results, list):
                for test_result in category_results:
                    total_tests += 1
                    if test_result.get("status") == "passed":
                        passed_tests += 1
                    else:
                        failed_tests += 1

                    if "response_time" in test_result:
                        total_response_time += test_result["response_time"]

                    if "analysis" in test_result and "quality_score" in test_result["analysis"]:
                        quality_scores.append(test_result["analysis"]["quality_score"])

        avg_response_time = total_response_time / max(1, total_tests - 1)  # Exclude connectivity test
        avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "pass_rate": round(pass_rate, 2),
            "average_response_time": round(avg_response_time, 2),
            "average_quality_score": round(avg_quality_score, 2),
            "test_duration": (end_time - start_time).total_seconds(),
            "status": "PASSED" if pass_rate >= 80 else "FAILED"
        }

async def main():
    """Main execution function"""
    async with AtlasChatbotTestSuite() as test_suite:
        results = await test_suite.run_comprehensive_test_suite()

        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"atlas_chatbot_test_results_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        # Print summary
        print("\n" + "="*80)
        print("ATLAS V5 ENHANCED CHATBOT TEST RESULTS")
        print("="*80)

        summary = results.get("summary", {})
        print(f"Overall Status: {summary.get('status', 'UNKNOWN')}")
        print(f"Total Tests: {summary.get('total_tests', 0)}")
        print(f"Passed: {summary.get('passed_tests', 0)}")
        print(f"Failed: {summary.get('failed_tests', 0)}")
        print(f"Pass Rate: {summary.get('pass_rate', 0)}%")
        print(f"Average Response Time: {summary.get('average_response_time', 0):.2f}s")
        print(f"Average Quality Score: {summary.get('average_quality_score', 0):.2f}/100")
        print(f"Test Duration: {summary.get('test_duration', 0):.2f}s")
        print(f"\nDetailed results saved to: {filename}")
        print("="*80)

        return results

if __name__ == "__main__":
    asyncio.run(main())
