"""
A.T.L.A.S. v5.0 Expanded Universe Scanner Configuration
Optimized scanning strategies for 1000+ stock universe with API efficiency
"""

import logging
import asyncio
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import math

logger = logging.getLogger(__name__)

class ScanningStrategy(Enum):
    """Different scanning strategies for expanded universe"""
    PRIORITY_FIRST = "priority_first"      # Scan high-priority symbols first
    ROUND_ROBIN = "round_robin"            # Rotate through all symbols equally
    ADAPTIVE = "adaptive"                  # Adapt based on market conditions
    SECTOR_ROTATION = "sector_rotation"    # Rotate by sector
    VOLUME_WEIGHTED = "volume_weighted"    # Weight by trading volume

class ScanningTier(Enum):
    """Scanning frequency tiers"""
    ULTRA_FAST = "ultra_fast"    # Every 10 seconds
    FAST = "fast"                # Every 30 seconds  
    NORMAL = "normal"            # Every 60 seconds
    SLOW = "slow"                # Every 300 seconds (5 minutes)
    BACKGROUND = "background"    # Every 900 seconds (15 minutes)

@dataclass
class ScannerBatchConfig:
    """Configuration for scanner batching"""
    batch_size: int = 50                    # Symbols per batch
    batch_interval: float = 2.0             # Seconds between batches
    max_concurrent_batches: int = 3         # Max parallel batches
    api_calls_per_batch: int = 10           # API calls per batch
    retry_failed_symbols: bool = True       # Retry failed symbols
    priority_batch_size: int = 20           # Smaller batches for priority symbols

@dataclass
class ExpandedScannerConfig:
    """Comprehensive scanner configuration for expanded universe"""
    
    # Universe settings
    total_universe_size: int = 1000
    active_scanning_size: int = 500         # Actively scanned symbols
    priority_symbols_count: int = 50        # Ultra-high priority
    
    # Scanning intervals by tier
    tier_intervals: Dict[ScanningTier, int] = None
    
    # API management
    max_api_calls_per_minute: int = 2500    # Conservative FMP limit
    api_call_buffer: float = 0.8            # Use 80% of limit for safety
    
    # Batching configuration
    batch_config: ScannerBatchConfig = None
    
    # Performance settings
    enable_caching: bool = True
    cache_duration: int = 30                # Seconds
    enable_adaptive_scanning: bool = True
    market_hours_boost: bool = True         # Faster scanning during market hours
    
    # Quality filters
    min_volume_threshold: int = 50000       # Minimum daily volume
    min_market_cap: int = 50_000_000       # Minimum market cap
    exclude_penny_stocks: bool = True       # Exclude stocks under $1
    
    def __post_init__(self):
        if self.tier_intervals is None:
            self.tier_intervals = {
                ScanningTier.ULTRA_FAST: 10,
                ScanningTier.FAST: 30,
                ScanningTier.NORMAL: 60,
                ScanningTier.SLOW: 300,
                ScanningTier.BACKGROUND: 900
            }
        
        if self.batch_config is None:
            self.batch_config = ScannerBatchConfig()

class ExpandedUniverseScanner:
    """Optimized scanner for expanded stock universe"""
    
    def __init__(self, config: ExpandedScannerConfig = None):
        self.config = config or ExpandedScannerConfig()
        self.symbol_tiers: Dict[str, ScanningTier] = {}
        self.scanning_batches: Dict[ScanningTier, List[List[str]]] = {}
        self.last_scan_times: Dict[str, datetime] = {}
        self.scan_results_cache: Dict[str, Dict] = {}
        self.api_call_tracker = APICallTracker(self.config.max_api_calls_per_minute)
        self.is_running = False
        
    async def initialize(self):
        """Initialize the expanded universe scanner"""
        try:
            logger.info("🚀 Initializing A.T.L.A.S. Expanded Universe Scanner...")
            
            # Load expanded universe
            symbols = await self._load_expanded_universe()
            logger.info(f"📊 Loaded {len(symbols)} symbols for scanning")
            
            # Assign symbols to scanning tiers
            await self._assign_scanning_tiers(symbols)
            
            # Create optimized batches
            await self._create_scanning_batches()
            
            # Initialize API call tracking
            self.api_call_tracker.reset()
            
            logger.info("✅ Expanded Universe Scanner initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize scanner: {e}")
            raise
    
    async def _load_expanded_universe(self) -> List[str]:
        """Load the expanded stock universe"""
        try:
            from atlas_expanded_universe import get_expanded_symbols
            symbols = get_expanded_symbols()
            
            if not symbols or len(symbols) < 100:
                logger.warning("⚠️ Expanded universe too small, falling back to S&P 500")
                from sp500_symbols import get_sp500_symbols
                symbols = get_sp500_symbols()
            
            return symbols[:self.config.total_universe_size]  # Limit to configured size
            
        except Exception as e:
            logger.error(f"❌ Failed to load expanded universe: {e}")
            # Fallback to S&P 500
            from sp500_symbols import get_sp500_symbols
            return get_sp500_symbols()
    
    async def _assign_scanning_tiers(self, symbols: List[str]):
        """Assign symbols to appropriate scanning tiers based on priority"""
        try:
            # Get symbol priorities (simulated for now)
            symbol_priorities = await self._calculate_symbol_priorities(symbols)
            
            # Sort by priority
            sorted_symbols = sorted(symbols, key=lambda s: symbol_priorities.get(s, 0), reverse=True)
            
            # Assign to tiers
            tier_sizes = {
                ScanningTier.ULTRA_FAST: min(50, len(sorted_symbols)),
                ScanningTier.FAST: min(100, len(sorted_symbols) - 50),
                ScanningTier.NORMAL: min(200, len(sorted_symbols) - 150),
                ScanningTier.SLOW: min(300, len(sorted_symbols) - 350),
                ScanningTier.BACKGROUND: len(sorted_symbols) - 650
            }
            
            current_index = 0
            for tier, size in tier_sizes.items():
                if size > 0:
                    tier_symbols = sorted_symbols[current_index:current_index + size]
                    for symbol in tier_symbols:
                        self.symbol_tiers[symbol] = tier
                    current_index += size
                    logger.info(f"📈 {tier.value}: {len(tier_symbols)} symbols")
            
        except Exception as e:
            logger.error(f"❌ Failed to assign scanning tiers: {e}")
            # Fallback: assign all to normal tier
            for symbol in symbols:
                self.symbol_tiers[symbol] = ScanningTier.NORMAL
    
    async def _calculate_symbol_priorities(self, symbols: List[str]) -> Dict[str, float]:
        """Calculate priority scores for symbols"""
        priorities = {}
        
        try:
            from sp500_symbols import SP500_SYMBOLS, HIGH_VOLUME_SYMBOLS
            from atlas_expanded_universe import expanded_universe
            
            for symbol in symbols:
                score = 50.0  # Base score
                
                # S&P 500 bonus
                if symbol in SP500_SYMBOLS:
                    score += 30
                
                # High volume bonus
                if symbol in HIGH_VOLUME_SYMBOLS:
                    score += 20
                
                # Get metrics from expanded universe
                try:
                    metrics = expanded_universe.stock_metrics.get(symbol)
                    if metrics:
                        score += metrics.quality_score * 0.3
                        
                        # Liquidity bonus
                        if metrics.liquidity_tier.value == 'ultra_high':
                            score += 25
                        elif metrics.liquidity_tier.value == 'high':
                            score += 15
                        elif metrics.liquidity_tier.value == 'medium':
                            score += 5
                        
                        # Market cap bonus
                        if metrics.market_cap_category.value == 'large':
                            score += 15
                        elif metrics.market_cap_category.value == 'mid':
                            score += 10
                
                except:
                    pass  # Use base score if metrics unavailable
                
                priorities[symbol] = score
                
        except Exception as e:
            logger.error(f"❌ Error calculating priorities: {e}")
            # Fallback: equal priorities
            for symbol in symbols:
                priorities[symbol] = 50.0
        
        return priorities
    
    async def _create_scanning_batches(self):
        """Create optimized batches for each scanning tier"""
        try:
            for tier in ScanningTier:
                tier_symbols = [s for s, t in self.symbol_tiers.items() if t == tier]
                
                if not tier_symbols:
                    continue
                
                # Determine batch size based on tier
                if tier == ScanningTier.ULTRA_FAST:
                    batch_size = self.config.batch_config.priority_batch_size
                else:
                    batch_size = self.config.batch_config.batch_size
                
                # Create batches
                batches = []
                for i in range(0, len(tier_symbols), batch_size):
                    batch = tier_symbols[i:i + batch_size]
                    batches.append(batch)
                
                self.scanning_batches[tier] = batches
                logger.info(f"📦 {tier.value}: {len(batches)} batches ({batch_size} symbols each)")
                
        except Exception as e:
            logger.error(f"❌ Failed to create scanning batches: {e}")
    
    async def start_scanning(self):
        """Start the expanded universe scanning process"""
        if self.is_running:
            logger.warning("⚠️ Scanner already running")
            return
        
        self.is_running = True
        logger.info("🚀 Starting expanded universe scanning...")
        
        # Start scanning tasks for each tier
        tasks = []
        for tier in ScanningTier:
            if tier in self.scanning_batches and self.scanning_batches[tier]:
                task = asyncio.create_task(self._scan_tier(tier))
                tasks.append(task)
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"❌ Scanning error: {e}")
        finally:
            self.is_running = False
    
    async def _scan_tier(self, tier: ScanningTier):
        """Scan all batches for a specific tier"""
        interval = self.config.tier_intervals[tier]
        batches = self.scanning_batches[tier]
        
        logger.info(f"📡 Starting {tier.value} scanning (interval: {interval}s, batches: {len(batches)})")
        
        batch_index = 0
        while self.is_running:
            try:
                # Check API rate limits
                if not await self.api_call_tracker.can_make_calls(len(batches[batch_index])):
                    await asyncio.sleep(1)
                    continue
                
                # Scan current batch
                batch = batches[batch_index]
                await self._scan_batch(batch, tier)
                
                # Move to next batch
                batch_index = (batch_index + 1) % len(batches)
                
                # Wait for next scan
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"❌ Error scanning {tier.value} tier: {e}")
                await asyncio.sleep(interval)
    
    async def _scan_batch(self, symbols: List[str], tier: ScanningTier):
        """Scan a batch of symbols"""
        try:
            # Record API calls
            await self.api_call_tracker.record_calls(len(symbols))
            
            # Simulate scanning (in production, would call real scanner)
            logger.debug(f"🔍 Scanning {tier.value} batch: {len(symbols)} symbols")
            
            # Update last scan times
            now = datetime.now()
            for symbol in symbols:
                self.last_scan_times[symbol] = now
            
        except Exception as e:
            logger.error(f"❌ Error scanning batch: {e}")
    
    def get_scanning_status(self) -> Dict[str, Any]:
        """Get current scanning status"""
        total_symbols = sum(len(batches) * len(batches[0]) if batches else 0 
                           for batches in self.scanning_batches.values())
        
        tier_status = {}
        for tier, batches in self.scanning_batches.items():
            tier_symbols = sum(len(batch) for batch in batches)
            tier_status[tier.value] = {
                'symbols': tier_symbols,
                'batches': len(batches),
                'interval': self.config.tier_intervals[tier]
            }
        
        return {
            'is_running': self.is_running,
            'total_symbols': total_symbols,
            'tier_status': tier_status,
            'api_calls_remaining': self.api_call_tracker.get_remaining_calls(),
            'last_update': datetime.now().isoformat()
        }

class APICallTracker:
    """Track API call usage and rate limits"""
    
    def __init__(self, max_calls_per_minute: int):
        self.max_calls_per_minute = max_calls_per_minute
        self.calls_this_minute = 0
        self.minute_start = datetime.now()
    
    async def can_make_calls(self, num_calls: int) -> bool:
        """Check if we can make the specified number of calls"""
        self._reset_if_new_minute()
        return (self.calls_this_minute + num_calls) <= self.max_calls_per_minute
    
    async def record_calls(self, num_calls: int):
        """Record that we made the specified number of calls"""
        self._reset_if_new_minute()
        self.calls_this_minute += num_calls
    
    def get_remaining_calls(self) -> int:
        """Get remaining API calls for this minute"""
        self._reset_if_new_minute()
        return max(0, self.max_calls_per_minute - self.calls_this_minute)
    
    def _reset_if_new_minute(self):
        """Reset counter if we're in a new minute"""
        now = datetime.now()
        if (now - self.minute_start).total_seconds() >= 60:
            self.calls_this_minute = 0
            self.minute_start = now
    
    def reset(self):
        """Reset the tracker"""
        self.calls_this_minute = 0
        self.minute_start = datetime.now()

# Global scanner instance
expanded_scanner = ExpandedUniverseScanner()

# Convenience functions
async def initialize_expanded_scanner():
    """Initialize the expanded universe scanner"""
    await expanded_scanner.initialize()
    return expanded_scanner

async def start_expanded_scanning():
    """Start expanded universe scanning"""
    await expanded_scanner.start_scanning()

def get_expanded_scanner_status():
    """Get expanded scanner status"""
    return expanded_scanner.get_scanning_status()
