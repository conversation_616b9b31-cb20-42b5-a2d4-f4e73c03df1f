#!/usr/bin/env python3
"""
Test Trading Data Display for A.T.L.A.S. v5.0 Web Interface
Validates that <PERSON> scanner results and Alpaca trading positions display correctly with real data
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingDataValidator:
    """Validate trading data display in web interface"""
    
    def __init__(self, base_url="http://localhost:8002"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def validate_alpaca_positions(self):
        """Validate Alpaca trading positions data"""
        logger.info("💼 Validating Alpaca trading positions...")
        
        try:
            url = f"{self.base_url}/api/v1/trading/positions"
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.error(f"❌ Positions API returned {response.status}")
                    return False
                
                data = await response.json()
                
                # Validate required fields
                required_fields = ['positions', 'total_value', 'cash_balance', 'buying_power', 'timestamp', 'status']
                for field in required_fields:
                    if field not in data:
                        logger.error(f"❌ Missing required field: {field}")
                        return False
                
                # Validate data types and values
                if not isinstance(data['positions'], list):
                    logger.error("❌ Positions should be a list")
                    return False
                
                if not isinstance(data['total_value'], (int, float)):
                    logger.error("❌ Total value should be numeric")
                    return False
                
                if data['status'] != 'live_data':
                    logger.error(f"❌ Expected 'live_data' status, got '{data['status']}'")
                    return False
                
                # Log account information
                logger.info(f"✅ Account Value: ${data['total_value']:,.2f}")
                logger.info(f"✅ Cash Balance: ${data['cash_balance']:,.2f}")
                logger.info(f"✅ Buying Power: ${data['buying_power']:,.2f}")
                logger.info(f"✅ Positions Count: {len(data['positions'])}")
                logger.info(f"✅ Data Status: {data['status']}")
                
                # Validate individual positions if any exist
                for i, position in enumerate(data['positions']):
                    pos_fields = ['symbol', 'qty', 'market_value', 'cost_basis', 'unrealized_pl', 'side']
                    for field in pos_fields:
                        if field not in position:
                            logger.error(f"❌ Position {i} missing field: {field}")
                            return False
                    
                    logger.info(f"✅ Position: {position['symbol']} - {position['qty']} shares")
                
                logger.info("✅ Alpaca positions data validation PASSED")
                return True
                
        except Exception as e:
            logger.error(f"❌ Alpaca positions validation failed: {e}")
            return False
    
    async def validate_lee_method_signals(self):
        """Validate Lee Method scanner signals data"""
        logger.info("📊 Validating Lee Method scanner signals...")
        
        try:
            url = f"{self.base_url}/api/v1/lee_method/signals"
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.error(f"❌ Lee Method API returned {response.status}")
                    return False
                
                data = await response.json()
                
                # Validate required fields
                required_fields = ['signals', 'timestamp', 'status', 'total_signals', 'message']
                for field in required_fields:
                    if field not in data:
                        logger.error(f"❌ Missing required field: {field}")
                        return False
                
                # Validate data types
                if not isinstance(data['signals'], list):
                    logger.error("❌ Signals should be a list")
                    return False
                
                if not isinstance(data['total_signals'], int):
                    logger.error("❌ Total signals should be an integer")
                    return False
                
                if data['status'] != 'active':
                    logger.error(f"❌ Expected 'active' status, got '{data['status']}'")
                    return False
                
                # Log scanner information
                logger.info(f"✅ Scanner Status: {data['status']}")
                logger.info(f"✅ Total Signals: {data['total_signals']}")
                logger.info(f"✅ Message: {data['message']}")
                
                # Validate individual signals if any exist
                for i, signal in enumerate(data['signals']):
                    signal_fields = ['symbol', 'direction', 'strength', 'confidence', 'timestamp']
                    for field in signal_fields:
                        if field not in signal:
                            logger.error(f"❌ Signal {i} missing field: {field}")
                            return False
                    
                    logger.info(f"✅ Signal: {signal['symbol']} - {signal['direction']} ({signal['confidence']:.1%})")
                
                logger.info("✅ Lee Method signals data validation PASSED")
                return True
                
        except Exception as e:
            logger.error(f"❌ Lee Method signals validation failed: {e}")
            return False
    
    async def validate_market_data_integrity(self):
        """Validate that no mock data is being returned"""
        logger.info("🔍 Validating market data integrity (no mock data)...")
        
        try:
            # Test multiple endpoints for mock data indicators
            endpoints_to_check = [
                "/api/v1/trading/positions",
                "/api/v1/lee_method/signals",
                "/api/v1/health"
            ]
            
            mock_indicators = [
                "mock", "simulated", "fake", "test_data", "placeholder",
                "demo", "sample", "generated", "artificial"
            ]
            
            for endpoint in endpoints_to_check:
                url = f"{self.base_url}{endpoint}"
                async with self.session.get(url) as response:
                    if response.status == 200:
                        text = await response.text()
                        text_lower = text.lower()
                        
                        for indicator in mock_indicators:
                            if indicator in text_lower:
                                logger.error(f"❌ Mock data indicator '{indicator}' found in {endpoint}")
                                return False
                        
                        logger.info(f"✅ {endpoint} - No mock data indicators found")
            
            logger.info("✅ Market data integrity validation PASSED")
            return True
            
        except Exception as e:
            logger.error(f"❌ Market data integrity validation failed: {e}")
            return False
    
    async def validate_real_time_data_flow(self):
        """Validate that data updates in real-time"""
        logger.info("⚡ Validating real-time data flow...")
        
        try:
            # Get initial data
            url = f"{self.base_url}/api/v1/health"
            async with self.session.get(url) as response:
                initial_data = await response.json()
                initial_timestamp = initial_data.get('timestamp')
            
            # Wait a moment
            await asyncio.sleep(2)
            
            # Get updated data
            async with self.session.get(url) as response:
                updated_data = await response.json()
                updated_timestamp = updated_data.get('timestamp')
            
            # Validate timestamps are different (indicating real-time updates)
            if initial_timestamp == updated_timestamp:
                logger.warning("⚠️ Timestamps identical - may indicate caching")
            else:
                logger.info("✅ Timestamps different - real-time updates confirmed")
            
            # Validate uptime is increasing
            initial_uptime = initial_data.get('uptime_seconds', 0)
            updated_uptime = updated_data.get('uptime_seconds', 0)
            
            if updated_uptime > initial_uptime:
                logger.info(f"✅ Uptime increased: {initial_uptime:.1f}s → {updated_uptime:.1f}s")
                logger.info("✅ Real-time data flow validation PASSED")
                return True
            else:
                logger.error("❌ Uptime did not increase - real-time updates may not be working")
                return False
                
        except Exception as e:
            logger.error(f"❌ Real-time data flow validation failed: {e}")
            return False
    
    async def run_comprehensive_validation(self):
        """Run comprehensive trading data display validation"""
        logger.info("🚀 Starting Trading Data Display Validation")
        
        validations = [
            ("Alpaca Trading Positions", self.validate_alpaca_positions),
            ("Lee Method Scanner Signals", self.validate_lee_method_signals),
            ("Market Data Integrity", self.validate_market_data_integrity),
            ("Real-time Data Flow", self.validate_real_time_data_flow)
        ]
        
        results = {}
        
        for validation_name, validation_func in validations:
            logger.info(f"\n📋 Running: {validation_name}")
            try:
                result = await validation_func()
                results[validation_name] = result
                logger.info(f"{'✅ PASS' if result else '❌ FAIL'}: {validation_name}")
            except Exception as e:
                logger.error(f"❌ ERROR in {validation_name}: {e}")
                results[validation_name] = False
        
        # Summary
        logger.info("\n📊 Trading Data Display Validation Results:")
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for validation_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {validation_name}: {status}")
        
        logger.info(f"\n🎯 Overall Result: {passed}/{total} validations passed")
        
        if passed == total:
            logger.info("🎉 All trading data display validations PASSED!")
            return True
        else:
            logger.error("❌ Some trading data display validations FAILED!")
            return False

async def main():
    """Main validation function"""
    async with TradingDataValidator() as validator:
        success = await validator.run_comprehensive_validation()
        return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
