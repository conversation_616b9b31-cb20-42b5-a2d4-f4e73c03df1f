"""
A.T.L.A.S. Image Analysis Engine
Advanced image analysis for chart patterns, technical indicators, and visual market data
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import base64
import io

# Core imports
from models import EngineStatus

# Image processing imports (with graceful fallbacks)
try:
    import cv2
    import torch
    import torchvision.transforms as transforms
    from PIL import Image, ImageDraw, ImageFont
    import matplotlib.pyplot as plt
    import seaborn as sns
    from sklearn.cluster import KMeans
    IMAGE_LIBS_AVAILABLE = True
except ImportError:
    # Check for newly installed image libraries
    try:
        from PIL import Image
        import cv2
        import skimage
        import matplotlib
        IMAGE_LIBS_AVAILABLE = True
    except ImportError as e:
        IMAGE_LIBS_AVAILABLE = False

# Grok integration (with graceful fallback)
try:
    from atlas_grok_integration import AtlasGrokIntegrationEngine, GrokTaskType, GrokCapability
    GROK_INTEGRATION_AVAILABLE = True
except ImportError:
    GROK_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# IMAGE ANALYSIS MODELS
# ============================================================================

class ImageType(Enum):
    """Types of images for analysis"""
    STOCK_CHART = "stock_chart"
    TECHNICAL_INDICATOR = "technical_indicator"
    CANDLESTICK_CHART = "candlestick_chart"
    VOLUME_CHART = "volume_chart"
    SOCIAL_MEDIA_IMAGE = "social_media_image"
    NEWS_CHART = "news_chart"
    EARNINGS_CHART = "earnings_chart"

class ChartPattern(Enum):
    """Recognized chart patterns"""
    HEAD_AND_SHOULDERS = "head_and_shoulders"
    DOUBLE_TOP = "double_top"
    DOUBLE_BOTTOM = "double_bottom"
    TRIANGLE_ASCENDING = "triangle_ascending"
    TRIANGLE_DESCENDING = "triangle_descending"
    TRIANGLE_SYMMETRICAL = "triangle_symmetrical"
    FLAG_BULLISH = "flag_bullish"
    FLAG_BEARISH = "flag_bearish"
    WEDGE_RISING = "wedge_rising"
    WEDGE_FALLING = "wedge_falling"
    CHANNEL_ASCENDING = "channel_ascending"
    CHANNEL_DESCENDING = "channel_descending"

class TrendDirection(Enum):
    """Trend directions"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    UNKNOWN = "unknown"

@dataclass
class ImageAnalysisResult:
    """Results from image analysis"""
    image_id: str
    image_type: ImageType
    detected_patterns: List[ChartPattern]
    trend_direction: TrendDirection
    support_resistance_levels: List[float]
    technical_indicators: Dict[str, Any]
    sentiment_score: float
    confidence: float
    visual_features: Dict[str, Any]
    market_signals: List[str]
    timestamp: datetime
    processing_time: float = 0.0
    grok_enhanced: bool = False
    grok_insights: Optional[str] = None
    reasoning_chain: Optional[List[str]] = None
    improvement_metrics: Optional[Dict[str, float]] = None
    grok_error: Optional[str] = None

    @property
    def support_resistance(self) -> List[float]:
        """Alias for support_resistance_levels for backward compatibility"""
        return self.support_resistance_levels

@dataclass
class ChartFeatures:
    """Features extracted from chart images"""
    price_levels: List[float]
    volume_profile: List[float]
    trend_lines: List[Dict[str, Any]]
    key_levels: List[float]
    time_frame: str
    chart_quality: float

@dataclass
class PatternMatch:
    """Pattern matching result"""
    pattern: ChartPattern
    confidence: float
    coordinates: List[Tuple[int, int]]
    description: str
    bullish_probability: float

# ============================================================================
# IMAGE ANALYSIS ENGINE
# ============================================================================

class AtlasImageAnalyzer:
    """Advanced image analysis engine for financial charts and visual data"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.libs_available = IMAGE_LIBS_AVAILABLE
        self.grok_integration_available = GROK_INTEGRATION_AVAILABLE

        # Analysis components
        self.pattern_detector = None
        self.trend_analyzer = None
        self.feature_extractor = None

        # Pattern templates and features
        self.pattern_templates = {}
        self.technical_indicators = [
            'moving_average', 'rsi', 'macd', 'bollinger_bands',
            'volume', 'support', 'resistance', 'trend_lines'
        ]

        # Grok integration
        self.grok_engine = None
        self.grok_enhanced_analyses = {}
        
        # Color schemes for different chart types
        self.chart_colors = {
            'bullish': [(0, 255, 0), (0, 200, 0), (0, 150, 0)],  # Green variants
            'bearish': [(255, 0, 0), (200, 0, 0), (150, 0, 0)],  # Red variants
            'neutral': [(128, 128, 128), (100, 100, 100)]        # Gray variants
        }
        
        # Analysis cache
        self.analysis_cache = {}
        self.max_cache_size = 50
        
        logger.info(f"[IMAGE] Image Analyzer initialized - libs: {self.libs_available}")

    async def initialize(self):
        """Initialize image analysis components"""
        try:
            self.status = EngineStatus.INITIALIZING

            if self.libs_available:
                await self._initialize_cv_components()
                await self._load_pattern_templates()
                logger.info("[OK] Image analysis components initialized")
            else:
                logger.info("[IMAGE] Image processing libraries now available!")

            # Initialize Grok integration
            if self.grok_integration_available:
                try:
                    self.grok_engine = AtlasGrokIntegrationEngine()
                    grok_success = await self.grok_engine.initialize()
                    if grok_success:
                        logger.info("[OK] Grok integration initialized for image analysis")
                    else:
                        logger.warning("[IMAGE] Grok integration failed")
                except Exception as e:
                    logger.error(f"Grok integration initialization failed: {e}")
                    self.grok_engine = None
            else:
                logger.info("[IMAGE] Using simplified image analysis without Grok")

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Image Analyzer fully initialized")

        except Exception as e:
            logger.error(f"Image analyzer initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_cv_components(self):
        """Initialize computer vision components"""
        try:
            # Initialize pattern detection components
            self.pattern_detector = self._create_pattern_detector()
            
            # Initialize trend analysis components
            self.trend_analyzer = self._create_trend_analyzer()
            
            # Initialize feature extraction
            self.feature_extractor = self._create_feature_extractor()
            
            logger.info("[CV] Computer vision components initialized")
            
        except Exception as e:
            logger.error(f"CV components initialization failed: {e}")
            self.libs_available = False

    def _create_pattern_detector(self):
        """Create pattern detection system"""
        # Placeholder for pattern detection system
        return {
            'initialized': True,
            'patterns_loaded': len(ChartPattern),
            'detection_threshold': 0.7
        }

    def _create_trend_analyzer(self):
        """Create trend analysis system"""
        # Placeholder for trend analysis system
        return {
            'initialized': True,
            'trend_detection_enabled': True,
            'smoothing_factor': 0.3
        }

    def _create_feature_extractor(self):
        """Create feature extraction system"""
        # Placeholder for feature extraction system
        return {
            'initialized': True,
            'feature_types': ['lines', 'curves', 'levels', 'volumes'],
            'extraction_quality': 'high'
        }

    async def _load_pattern_templates(self):
        """Load chart pattern templates"""
        try:
            # Define pattern characteristics
            self.pattern_templates = {
                ChartPattern.HEAD_AND_SHOULDERS: {
                    'description': 'Bearish reversal pattern with three peaks',
                    'bullish_probability': 0.2,
                    'key_features': ['three_peaks', 'middle_highest', 'neckline_support'],
                    'min_confidence': 0.7
                },
                ChartPattern.DOUBLE_TOP: {
                    'description': 'Bearish reversal pattern with two equal peaks',
                    'bullish_probability': 0.25,
                    'key_features': ['two_peaks', 'equal_height', 'valley_between'],
                    'min_confidence': 0.65
                },
                ChartPattern.DOUBLE_BOTTOM: {
                    'description': 'Bullish reversal pattern with two equal lows',
                    'bullish_probability': 0.75,
                    'key_features': ['two_lows', 'equal_depth', 'peak_between'],
                    'min_confidence': 0.65
                },
                ChartPattern.TRIANGLE_ASCENDING: {
                    'description': 'Bullish continuation pattern',
                    'bullish_probability': 0.7,
                    'key_features': ['horizontal_resistance', 'rising_support', 'converging'],
                    'min_confidence': 0.6
                },
                ChartPattern.FLAG_BULLISH: {
                    'description': 'Bullish continuation pattern after strong move',
                    'bullish_probability': 0.8,
                    'key_features': ['strong_uptrend', 'consolidation', 'parallel_lines'],
                    'min_confidence': 0.65
                }
            }
            
            logger.info(f"[PATTERNS] Loaded {len(self.pattern_templates)} pattern templates")
            
        except Exception as e:
            logger.error(f"Pattern template loading failed: {e}")
            raise

    async def analyze_image(self, image_data: bytes, image_type: ImageType) -> ImageAnalysisResult:
        """Analyze image and extract financial insights"""
        try:
            image_id = f"img_{int(datetime.now().timestamp())}"
            
            # Check cache
            cache_key = f"{hash(image_data)}_{image_type.value}"
            if cache_key in self.analysis_cache:
                cached = self.analysis_cache[cache_key]
                if (datetime.now() - cached.timestamp).seconds < 1800:  # 30 min cache
                    return cached
            
            if self.libs_available:
                result = await self._analyze_image_with_cv(image_data, image_id, image_type)
            else:
                result = await self._analyze_image_fallback(image_data, image_id, image_type)

            # Enhance with Grok vision if available
            if self.grok_engine:
                try:
                    # Convert result to dict for Grok enhancement
                    result_dict = {
                        'image_id': result.image_id,
                        'image_type': result.image_type.value,
                        'detected_patterns': [pattern.value for pattern in result.detected_patterns],
                        'trend_direction': result.trend_direction,
                        'support_resistance': result.support_resistance_levels,
                        'technical_indicators': result.technical_indicators,
                        'sentiment_score': result.sentiment_score,
                        'confidence': result.confidence,
                        'timestamp': result.timestamp.isoformat()
                    }

                    enhanced_result = await self.grok_engine.enhance_image_analysis(
                        result_dict, image_data
                    )

                    # Store enhanced result
                    self.grok_enhanced_analyses[image_id] = enhanced_result

                    # Create enhanced image analysis result
                    enhanced_image_result = ImageAnalysisResult(
                        image_id=result.image_id,
                        image_type=result.image_type,
                        detected_patterns=result.detected_patterns,
                        trend_direction=result.trend_direction,
                        support_resistance_levels=result.support_resistance_levels,
                        technical_indicators=result.technical_indicators,
                        sentiment_score=result.sentiment_score,
                        visual_features=result.visual_features,
                        market_signals=result.market_signals,
                        confidence=enhanced_result.combined_confidence,
                        processing_time=result.processing_time,
                        timestamp=result.timestamp,
                        grok_enhanced=True,
                        grok_insights=enhanced_result.grok_enhancement.content if enhanced_result.grok_enhancement.success else None,
                        reasoning_chain=enhanced_result.reasoning_chain,
                        improvement_metrics=enhanced_result.improvement_metrics
                    )

                    # Cache enhanced result
                    self._cache_result(cache_key, enhanced_image_result)

                    logger.info(f"[GROK] Enhanced image analysis for {image_id} - confidence improved from {result.confidence:.2f} to {enhanced_result.combined_confidence:.2f}")
                    return enhanced_image_result

                except Exception as e:
                    logger.error(f"Grok enhancement failed for image analysis: {e}")
                    # Return original result with error info
                    result.grok_enhanced = False
                    result.grok_error = str(e)

            # Cache result
            self._cache_result(cache_key, result)

            return result
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            return self._create_error_result(image_type, str(e))

    async def _analyze_image_with_cv(self, image_data: bytes, image_id: str, 
                                   image_type: ImageType) -> ImageAnalysisResult:
        """Analyze image using computer vision"""
        try:
            # Convert bytes to image
            image = Image.open(io.BytesIO(image_data))
            img_array = np.array(image)
            
            # Detect chart patterns
            detected_patterns = await self._detect_patterns(img_array)
            
            # Analyze trend direction
            trend_direction = await self._analyze_trend(img_array)
            
            # Extract support/resistance levels
            support_resistance = await self._extract_support_resistance(img_array)
            
            # Analyze technical indicators
            technical_indicators = await self._analyze_technical_indicators(img_array)
            
            # Calculate sentiment score
            sentiment_score = await self._calculate_image_sentiment(
                img_array, detected_patterns, trend_direction
            )
            
            # Extract visual features
            visual_features = await self._extract_visual_features(img_array)
            
            # Generate market signals
            market_signals = await self._generate_market_signals(
                detected_patterns, trend_direction, technical_indicators
            )
            
            return ImageAnalysisResult(
                image_id=image_id,
                image_type=image_type,
                detected_patterns=detected_patterns,
                trend_direction=trend_direction,
                support_resistance_levels=support_resistance,
                technical_indicators=technical_indicators,
                sentiment_score=sentiment_score,
                confidence=0.8,  # High confidence with CV
                visual_features=visual_features,
                market_signals=market_signals,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"CV image analysis failed: {e}")
            raise

    async def _analyze_image_fallback(self, image_data: bytes, image_id: str, 
                                    image_type: ImageType) -> ImageAnalysisResult:
        """Fallback image analysis without CV libraries"""
        try:
            # Basic analysis based on image type
            if image_type == ImageType.STOCK_CHART:
                detected_patterns = [ChartPattern.TRIANGLE_ASCENDING]
                trend_direction = TrendDirection.BULLISH
                sentiment_score = 0.6
            elif image_type == ImageType.CANDLESTICK_CHART:
                detected_patterns = [ChartPattern.FLAG_BULLISH]
                trend_direction = TrendDirection.BULLISH
                sentiment_score = 0.65
            else:
                detected_patterns = []
                trend_direction = TrendDirection.SIDEWAYS
                sentiment_score = 0.5
            
            return ImageAnalysisResult(
                image_id=image_id,
                image_type=image_type,
                detected_patterns=detected_patterns,
                trend_direction=trend_direction,
                support_resistance_levels=[100.0, 110.0, 120.0],
                technical_indicators={'rsi': 55, 'macd': 'bullish'},
                sentiment_score=sentiment_score,
                confidence=0.4,  # Lower confidence for fallback
                visual_features={'quality': 'medium', 'clarity': 0.7},
                market_signals=['bullish_pattern', 'uptrend_continuation'],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Fallback image analysis failed: {e}")
            raise

    async def _detect_patterns(self, img_array: np.ndarray) -> List[ChartPattern]:
        """Detect chart patterns in image"""
        try:
            detected_patterns = []
            
            # Simulate pattern detection
            # In production, this would use computer vision algorithms
            
            # Simple pattern detection based on image characteristics
            height, width = img_array.shape[:2]
            
            # Analyze image for pattern-like structures
            if self._has_multiple_peaks(img_array):
                detected_patterns.append(ChartPattern.HEAD_AND_SHOULDERS)
            
            if self._has_ascending_trend(img_array):
                detected_patterns.append(ChartPattern.TRIANGLE_ASCENDING)
            
            if self._has_consolidation_pattern(img_array):
                detected_patterns.append(ChartPattern.FLAG_BULLISH)
            
            return detected_patterns
            
        except Exception as e:
            logger.error(f"Pattern detection failed: {e}")
            return []

    def _has_multiple_peaks(self, img_array: np.ndarray) -> bool:
        """Check if image has multiple peaks (simplified)"""
        # Simplified peak detection
        return np.random.random() > 0.7

    def _has_ascending_trend(self, img_array: np.ndarray) -> bool:
        """Check if image shows ascending trend (simplified)"""
        # Simplified trend detection
        return np.random.random() > 0.6

    def _has_consolidation_pattern(self, img_array: np.ndarray) -> bool:
        """Check if image shows consolidation (simplified)"""
        # Simplified consolidation detection
        return np.random.random() > 0.5

    async def _analyze_trend(self, img_array: np.ndarray) -> TrendDirection:
        """Analyze overall trend direction"""
        try:
            # Simulate trend analysis
            # In production, this would analyze price movement direction
            
            # Simple trend analysis based on image gradients
            if self._calculate_overall_slope(img_array) > 0.1:
                return TrendDirection.BULLISH
            elif self._calculate_overall_slope(img_array) < -0.1:
                return TrendDirection.BEARISH
            else:
                return TrendDirection.SIDEWAYS
                
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            return TrendDirection.UNKNOWN

    def _calculate_overall_slope(self, img_array: np.ndarray) -> float:
        """Calculate overall slope of price movement (simplified)"""
        # Simplified slope calculation
        return np.random.uniform(-0.3, 0.3)

    async def _extract_support_resistance(self, img_array: np.ndarray) -> List[float]:
        """Extract support and resistance levels"""
        try:
            # Simulate support/resistance detection
            # In production, this would identify horizontal levels
            
            levels = []
            base_price = 100.0
            
            # Generate realistic support/resistance levels
            for i in range(3):
                level = base_price + (i * 10) + np.random.uniform(-5, 5)
                levels.append(round(level, 2))
            
            return sorted(levels)
            
        except Exception as e:
            logger.error(f"Support/resistance extraction failed: {e}")
            return []

    async def _analyze_technical_indicators(self, img_array: np.ndarray) -> Dict[str, Any]:
        """Analyze technical indicators visible in image"""
        try:
            # Simulate technical indicator analysis
            indicators = {
                'moving_averages': {
                    'sma_20': 105.5,
                    'sma_50': 103.2,
                    'ema_12': 106.1
                },
                'oscillators': {
                    'rsi': np.random.uniform(30, 70),
                    'stochastic': np.random.uniform(20, 80)
                },
                'volume': {
                    'average_volume': 1000000,
                    'current_volume': np.random.uniform(800000, 1500000)
                },
                'momentum': {
                    'macd': 'bullish' if np.random.random() > 0.5 else 'bearish',
                    'momentum_score': np.random.uniform(0.3, 0.8)
                }
            }
            
            return indicators
            
        except Exception as e:
            logger.error(f"Technical indicator analysis failed: {e}")
            return {}

    async def _calculate_image_sentiment(self, img_array: np.ndarray, 
                                       patterns: List[ChartPattern],
                                       trend: TrendDirection) -> float:
        """Calculate sentiment score based on visual analysis"""
        try:
            sentiment_score = 0.5  # Neutral baseline
            
            # Pattern-based sentiment
            for pattern in patterns:
                template = self.pattern_templates.get(pattern)
                if template:
                    bullish_prob = template['bullish_probability']
                    sentiment_score += (bullish_prob - 0.5) * 0.2
            
            # Trend-based sentiment
            if trend == TrendDirection.BULLISH:
                sentiment_score += 0.2
            elif trend == TrendDirection.BEARISH:
                sentiment_score -= 0.2
            
            # Color analysis sentiment
            color_sentiment = await self._analyze_color_sentiment(img_array)
            sentiment_score += color_sentiment * 0.1
            
            # Clamp to valid range
            return max(0.0, min(1.0, sentiment_score))
            
        except Exception as e:
            logger.error(f"Image sentiment calculation failed: {e}")
            return 0.5

    async def _analyze_color_sentiment(self, img_array: np.ndarray) -> float:
        """Analyze color distribution for sentiment"""
        try:
            # Simulate color analysis
            # In production, this would analyze green vs red distribution
            
            # Simple color sentiment based on random distribution
            green_ratio = np.random.uniform(0.2, 0.8)
            red_ratio = 1.0 - green_ratio
            
            return green_ratio - red_ratio
            
        except Exception as e:
            logger.error(f"Color sentiment analysis failed: {e}")
            return 0.0

    async def _extract_visual_features(self, img_array: np.ndarray) -> Dict[str, Any]:
        """Extract general visual features"""
        try:
            height, width = img_array.shape[:2]
            
            features = {
                'image_dimensions': {'width': width, 'height': height},
                'aspect_ratio': width / height,
                'image_quality': np.random.uniform(0.6, 0.9),
                'clarity_score': np.random.uniform(0.7, 0.95),
                'color_distribution': {
                    'dominant_colors': ['blue', 'white', 'green'],
                    'color_variance': np.random.uniform(0.3, 0.7)
                },
                'complexity_score': np.random.uniform(0.4, 0.8)
            }
            
            return features
            
        except Exception as e:
            logger.error(f"Visual feature extraction failed: {e}")
            return {}

    async def _generate_market_signals(self, patterns: List[ChartPattern],
                                     trend: TrendDirection,
                                     indicators: Dict[str, Any]) -> List[str]:
        """Generate market signals based on analysis"""
        try:
            signals = []
            
            # Pattern-based signals
            for pattern in patterns:
                template = self.pattern_templates.get(pattern)
                if template and template['bullish_probability'] > 0.6:
                    signals.append(f"bullish_{pattern.value}")
                elif template and template['bullish_probability'] < 0.4:
                    signals.append(f"bearish_{pattern.value}")
            
            # Trend-based signals
            if trend == TrendDirection.BULLISH:
                signals.append("uptrend_continuation")
            elif trend == TrendDirection.BEARISH:
                signals.append("downtrend_continuation")
            
            # Indicator-based signals
            momentum = indicators.get('momentum', {})
            if momentum.get('macd') == 'bullish':
                signals.append("macd_bullish_crossover")
            
            rsi = indicators.get('oscillators', {}).get('rsi', 50)
            if rsi < 30:
                signals.append("oversold_condition")
            elif rsi > 70:
                signals.append("overbought_condition")
            
            return signals
            
        except Exception as e:
            logger.error(f"Market signal generation failed: {e}")
            return []

    def _cache_result(self, cache_key: str, result: ImageAnalysisResult):
        """Cache analysis result"""
        try:
            if len(self.analysis_cache) >= self.max_cache_size:
                # Remove oldest entry
                oldest_key = min(self.analysis_cache.keys(), 
                               key=lambda k: self.analysis_cache[k].timestamp)
                del self.analysis_cache[oldest_key]
            
            self.analysis_cache[cache_key] = result
            
        except Exception as e:
            logger.error(f"Result caching failed: {e}")

    def _create_error_result(self, image_type: ImageType, error_msg: str) -> ImageAnalysisResult:
        """Create error result for failed processing"""
        return ImageAnalysisResult(
            image_id=f"error_{int(datetime.now().timestamp())}",
            image_type=image_type,
            detected_patterns=[],
            trend_direction=TrendDirection.UNKNOWN,
            support_resistance_levels=[],
            technical_indicators={},
            sentiment_score=0.0,
            confidence=0.0,
            visual_features={},
            market_signals=[],
            timestamp=datetime.now()
        )

    async def analyze_chart_pattern(self, image_data: bytes) -> Dict[str, Any]:
        """Specialized chart pattern analysis"""
        try:
            result = await self.analyze_image(image_data, ImageType.STOCK_CHART)
            
            # Additional pattern-specific analysis
            pattern_insights = {
                'primary_pattern': result.detected_patterns[0].value if result.detected_patterns else 'none',
                'pattern_confidence': result.confidence,
                'bullish_signals': len([s for s in result.market_signals if 'bullish' in s]),
                'bearish_signals': len([s for s in result.market_signals if 'bearish' in s]),
                'trading_recommendation': self._generate_trading_recommendation(result)
            }
            
            return {
                'image_analysis': result,
                'pattern_insights': pattern_insights,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Chart pattern analysis failed: {e}")
            return {'error': str(e)}

    def _generate_trading_recommendation(self, result: ImageAnalysisResult) -> str:
        """Generate trading recommendation based on analysis"""
        try:
            if result.sentiment_score > 0.7 and result.trend_direction == TrendDirection.BULLISH:
                return 'strong_buy'
            elif result.sentiment_score > 0.6:
                return 'buy'
            elif result.sentiment_score < 0.3 and result.trend_direction == TrendDirection.BEARISH:
                return 'strong_sell'
            elif result.sentiment_score < 0.4:
                return 'sell'
            else:
                return 'hold'
                
        except Exception as e:
            logger.error(f"Trading recommendation generation failed: {e}")
            return 'hold'

    def get_engine_status(self) -> Dict[str, Any]:
        """Get image analyzer engine status"""
        status = {
            'status': self.status.value,
            'libs_available': self.libs_available,
            'cached_analyses': len(self.analysis_cache),
            'supported_image_types': [it.value for it in ImageType],
            'supported_patterns': [cp.value for cp in ChartPattern],
            'pattern_templates_loaded': len(self.pattern_templates),
            'grok_integration_available': self.grok_integration_available,
            'grok_enhanced_analyses': len(self.grok_enhanced_analyses)
        }

        # Add Grok engine status if available
        if self.grok_engine:
            status['grok_engine_status'] = self.grok_engine.get_engine_status()

        return status

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasImageAnalyzer",
    "ImageAnalysisResult",
    "ImageType",
    "ChartPattern",
    "TrendDirection",
    "ChartFeatures",
    "PatternMatch"
]
