#!/usr/bin/env python3
"""
Test Critical Vulnerability Safeguards
Direct API testing of the implemented safety mechanisms
"""

import requests
import json
from datetime import datetime

def test_division_by_zero_protection():
    """Test division by zero protection in position sizing"""
    print("🧪 Testing Division by Zero Protection...")
    
    # Test case 1: Entry price equals stop price
    test_data = {
        "entry_price": 100.00,
        "stop_price": 100.00,  # Same as entry - should trigger protection
        "risk_amount": 500.00,
        "portfolio_value": 50000
    }
    
    try:
        response = requests.post(
            "http://localhost:8002/api/v1/trading/position_size",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                print(f"✅ PASS: Division by zero protection working")
                print(f"   Error message: {result['error']}")
                return True
            else:
                print(f"❌ FAIL: Should have returned error for zero risk")
                return False
        else:
            print(f"❌ FAIL: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_position_sizing_edge_cases():
    """Test various edge cases in position sizing"""
    print("\n🧪 Testing Position Sizing Edge Cases...")
    
    test_cases = [
        {
            "name": "Very small risk per share",
            "data": {
                "entry_price": 100.00,
                "stop_price": 100.005,  # 0.5 cent risk - should fail
                "risk_amount": 500.00,
                "portfolio_value": 50000
            },
            "should_fail": True
        },
        {
            "name": "Negative risk amount",
            "data": {
                "entry_price": 100.00,
                "stop_price": 95.00,
                "risk_amount": -500.00,  # Negative - should fail
                "portfolio_value": 50000
            },
            "should_fail": True
        },
        {
            "name": "Zero portfolio value",
            "data": {
                "entry_price": 100.00,
                "stop_price": 95.00,
                "risk_amount": 500.00,
                "portfolio_value": 0  # Zero portfolio - should fail
            },
            "should_fail": True
        },
        {
            "name": "Valid calculation",
            "data": {
                "entry_price": 100.00,
                "stop_price": 95.00,
                "risk_amount": 500.00,
                "portfolio_value": 50000
            },
            "should_fail": False
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        try:
            response = requests.post(
                "http://localhost:8002/api/v1/trading/position_size",
                json=test_case["data"],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                has_error = "error" in result
                
                if test_case["should_fail"]:
                    if has_error:
                        print(f"    ✅ PASS: Correctly rejected with error: {result.get('error', 'Unknown error')}")
                        passed += 1
                    else:
                        print(f"    ❌ FAIL: Should have failed but returned: {result}")
                else:
                    if not has_error:
                        print(f"    ✅ PASS: Valid calculation returned: {result.get('recommended_shares', 'N/A')} shares")
                        passed += 1
                    else:
                        print(f"    ❌ FAIL: Valid input rejected with error: {result.get('error', 'Unknown error')}")
            else:
                print(f"    ❌ FAIL: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ ERROR: {e}")
    
    print(f"\n  Edge Cases Summary: {passed}/{total} passed")
    return passed == total

def test_context_awareness():
    """Test context awareness across conversation turns"""
    print("\n🧪 Testing Context Awareness...")
    
    session_id = f"test_session_{datetime.now().strftime('%H%M%S')}"
    
    # First message about AAPL
    try:
        response1 = requests.post(
            "http://localhost:8002/api/v1/chat/message",
            json={
                "message": "Tell me about AAPL trend",
                "session_id": session_id,
                "context": "trading_analysis"
            },
            timeout=10
        )
        
        if response1.status_code != 200:
            print(f"❌ FAIL: First message failed with HTTP {response1.status_code}")
            return False
        
        # Second message that should remember AAPL
        response2 = requests.post(
            "http://localhost:8002/api/v1/chat/message",
            json={
                "message": "What if I wanted a tighter stop?",
                "session_id": session_id,
                "context": "trading_analysis"
            },
            timeout=10
        )
        
        if response2.status_code != 200:
            print(f"❌ FAIL: Second message failed with HTTP {response2.status_code}")
            return False
        
        result2 = response2.json()
        response_text = result2.get("response", "").lower()
        
        # Check if it remembers AAPL context
        if "aapl" in response_text or "apple" in response_text or "previous discussion" in response_text:
            print("✅ PASS: Context awareness working - remembered AAPL from previous message")
            return True
        else:
            print("❌ FAIL: Context not maintained across conversation turns")
            print(f"   Response: {response_text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_six_point_analysis():
    """Test 6-point analysis format"""
    print("\n🧪 Testing 6-Point Analysis Format...")
    
    try:
        response = requests.post(
            "http://localhost:8002/api/v1/chat/message",
            json={
                "message": "Give me a 6-Point analysis for TSLA today",
                "context": "analysis"
            },
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ FAIL: HTTP {response.status_code}")
            return False
        
        result = response.json()
        response_text = result.get("response", "")
        
        # Check for numbered points (1. 2. 3. 4. 5. 6.)
        numbered_points = 0
        for i in range(1, 7):
            if f"{i}." in response_text:
                numbered_points += 1
        
        if numbered_points >= 6:
            print(f"✅ PASS: 6-Point analysis format working - found {numbered_points} numbered points")
            return True
        else:
            print(f"❌ FAIL: Only found {numbered_points} numbered points, expected 6")
            print(f"   Response preview: {response_text[:300]}...")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_var_calculation():
    """Test VaR calculation functionality"""
    print("\n🧪 Testing VaR Calculation...")
    
    try:
        response = requests.post(
            "http://localhost:8002/api/v1/chat/message",
            json={
                "message": "Calculate my portfolio's 95% VaR",
                "context": "risk_analysis"
            },
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ FAIL: HTTP {response.status_code}")
            return False
        
        result = response.json()
        response_text = result.get("response", "").lower()
        
        # Check for VaR-specific terms
        var_terms = ["var", "value at risk", "95%", "confidence", "portfolio"]
        found_terms = sum(1 for term in var_terms if term in response_text)
        
        if found_terms >= 4:
            print(f"✅ PASS: VaR calculation working - found {found_terms} relevant terms")
            return True
        else:
            print(f"❌ FAIL: VaR calculation not working properly - only found {found_terms} relevant terms")
            print(f"   Response preview: {response_text[:300]}...")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all critical safeguard tests"""
    print("🚀 Atlas V4 Enhanced - Critical Safeguards Testing")
    print("=" * 60)
    
    tests = [
        ("Division by Zero Protection", test_division_by_zero_protection),
        ("Position Sizing Edge Cases", test_position_sizing_edge_cases),
        ("Context Awareness", test_context_awareness),
        ("6-Point Analysis Format", test_six_point_analysis),
        ("VaR Calculation", test_var_calculation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ ERROR in {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print("📊 CRITICAL SAFEGUARDS TEST SUMMARY")
    print("=" * 60)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Pass Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL CRITICAL SAFEGUARDS WORKING!")
    else:
        print("⚠️  Some critical safeguards need attention")
    
    return passed == total

if __name__ == "__main__":
    main()
