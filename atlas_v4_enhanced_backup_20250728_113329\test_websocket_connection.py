#!/usr/bin/env python3
"""
Test WebSocket Connection to A.T.L.A.S. v5.0
Verifies real-time communication between terminal and web interface
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_connection():
    """Test WebSocket connection to A.T.L.A.S. server"""
    uri = "ws://localhost:8002/ws/scanner"
    
    try:
        logger.info(f"🔗 Connecting to WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket connection established")
            
            # Send a test message
            test_message = {
                "type": "test_connection",
                "timestamp": datetime.now().isoformat(),
                "client": "terminal_test"
            }
            
            await websocket.send(json.dumps(test_message))
            logger.info("📤 Sent test message")
            
            # Listen for messages for 30 seconds
            timeout = 30
            start_time = asyncio.get_event_loop().time()
            
            while (asyncio.get_event_loop().time() - start_time) < timeout:
                try:
                    # Wait for message with timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    
                    logger.info(f"📨 Received message: {data.get('type', 'unknown')}")
                    logger.info(f"   Content: {data}")
                    
                    # Send pong response if ping received
                    if data.get('type') == 'ping':
                        pong_response = {"type": "pong", "timestamp": datetime.now().isoformat()}
                        await websocket.send(json.dumps(pong_response))
                        logger.info("📤 Sent pong response")
                        
                except asyncio.TimeoutError:
                    logger.info("⏱️ No message received in last 5 seconds")
                    continue
                except Exception as e:
                    logger.error(f"❌ Error receiving message: {e}")
                    break
            
            logger.info("✅ WebSocket test completed successfully")
            
    except Exception as e:
        logger.error(f"❌ WebSocket connection failed: {e}")
        return False
    
    return True

async def test_multiple_connections():
    """Test multiple WebSocket connections"""
    logger.info("🔗 Testing multiple WebSocket connections...")
    
    tasks = []
    for i in range(3):
        task = asyncio.create_task(test_single_connection(f"client_{i}"))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful = sum(1 for result in results if result is True)
    logger.info(f"✅ {successful}/3 connections successful")
    
    return successful == 3

async def test_single_connection(client_id):
    """Test a single WebSocket connection"""
    uri = "ws://localhost:8002/ws/scanner"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info(f"✅ {client_id} connected")
            
            # Send identification message
            message = {
                "type": "client_identification",
                "client_id": client_id,
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(message))
            
            # Listen for a few seconds
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                logger.info(f"📨 {client_id} received: {json.loads(message).get('type', 'unknown')}")
            except asyncio.TimeoutError:
                logger.info(f"⏱️ {client_id} no message received")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ {client_id} connection failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🚀 Starting A.T.L.A.S. WebSocket Connection Tests")
    
    # Test 1: Basic WebSocket connection
    logger.info("\n📋 Test 1: Basic WebSocket Connection")
    basic_test = await test_websocket_connection()
    
    # Test 2: Multiple connections
    logger.info("\n📋 Test 2: Multiple WebSocket Connections")
    multi_test = await test_multiple_connections()
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info(f"   Basic Connection: {'✅ PASS' if basic_test else '❌ FAIL'}")
    logger.info(f"   Multiple Connections: {'✅ PASS' if multi_test else '❌ FAIL'}")
    
    if basic_test and multi_test:
        logger.info("🎉 All WebSocket tests PASSED!")
        return True
    else:
        logger.error("❌ Some WebSocket tests FAILED!")
        return False

if __name__ == "__main__":
    asyncio.run(main())
