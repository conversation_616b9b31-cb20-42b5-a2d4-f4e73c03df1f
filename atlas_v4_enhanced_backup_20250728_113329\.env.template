# A.T.L.A.S AI Trading System - Environment Configuration Template
# Copy this file to .env and fill in your API keys

# =============================================================================
# REQUIRED API KEYS
# =============================================================================

# Alpaca Trading API (Required for live/paper trading)
# Get your keys from: https://alpaca.markets/
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets  # Use paper trading by default

# Grok AI (X.AI) API (Primary AI Provider - Required for AI features)
# Get your key from: https://console.x.ai/
GROK_API_KEY=************************************************************************************

# OpenAI API (Fallback AI Provider - Optional)
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Financial Modeling Prep API (Required for market data)
# Get your key from: https://financialmodelingprep.com/
FMP_API_KEY=your_fmp_api_key_here

# =============================================================================
# OPTIONAL API KEYS (System will work without these but with reduced functionality)
# =============================================================================

# Predicto API (Optional - for AI forecasting)
# Get your key from: https://predicto.ai/
PREDICTO_API_KEY=your_predicto_api_key_here

# Google Custom Search API (Optional - for web search)
# Get your key from: https://developers.google.com/custom-search/v1/introduction
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_custom_search_engine_id_here

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development

# Logging Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Server Configuration
HOST=0.0.0.0
PORT=8001

# Database Configuration (SQLite by default)
DATABASE_URL=sqlite:///atlas.db

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Paper Trading (true for simulation, false for live trading)
PAPER_TRADING=true

# Risk Management
MAX_DAILY_LOSS=0.02  # 2% maximum daily loss
MAX_POSITION_SIZE=0.10  # 10% maximum position size
REQUIRE_STOP_LOSS=true
REQUIRE_PROFIT_TARGET=true

# TTM Squeeze Scanner
MAX_SCAN_RESULTS=50
MIN_SIGNAL_STRENGTH=moderate

# =============================================================================
# AI CONFIGURATION
# =============================================================================

# Grok AI Model Configuration
GROK_MODEL=grok-3-latest
GROK_TEMPERATURE=0.2

# OpenAI Model Configuration (Fallback)
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# Multi-Agent System Weights
TECHNICAL_AGENT_WEIGHT=0.35
RISK_AGENT_WEIGHT=0.30
SENTIMENT_AGENT_WEIGHT=0.20
EXECUTION_AGENT_WEIGHT=0.15

# =============================================================================
# VALIDATION MODE (for testing without API keys)
# =============================================================================

# Set to true to run validation without requiring all API keys
VALIDATION_MODE=false

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Rate Limiting
API_RATE_LIMIT=100  # requests per minute
MARKET_DATA_CACHE_TTL=60  # seconds

# Security
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
API_KEY_HEADER=X-API-Key

# Performance
WORKER_PROCESSES=1
MAX_CONCURRENT_REQUESTS=10

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable specific features
ENABLE_WEB_SEARCH=true
ENABLE_ML_SENTIMENT=true
ENABLE_PORTFOLIO_OPTIMIZATION=true
ENABLE_AUTO_REINVESTMENT=false
ENABLE_REAL_TRADING=false  # Keep false for safety

# =============================================================================
# MONITORING AND ALERTS
# =============================================================================

# Health Check Configuration
HEALTH_CHECK_INTERVAL=300  # seconds
ALERT_EMAIL=<EMAIL>

# Performance Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
