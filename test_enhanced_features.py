#!/usr/bin/env python3
"""
Test Enhanced Atlas V5 Features
Tests the new Grok-enhanced trading strategies and real-time scanner
"""

import asyncio
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_enhanced_features():
    """Test the enhanced Atlas V5 features"""
    print('🔍 Testing Enhanced Atlas V5 Features...')
    
    try:
        # Test 1: Enhanced Grok Trading Strategies
        print('\n📊 Testing Enhanced Grok Trading Strategies...')
        
        from atlas_enhanced_grok_trading_strategies import enhanced_grok_strategy
        print('✅ Enhanced Grok Trading Strategies imported successfully')
        
        # Initialize the strategy
        init_success = await enhanced_grok_strategy.initialize()
        if init_success:
            print('✅ Enhanced Grok Strategy initialized with Grok AI')
        else:
            print('⚠️ Enhanced Grok Strategy initialized without Grok AI (fallback mode)')
        
        # Test opportunity generation
        print('\n🎯 Generating specific trading opportunities...')
        opportunities = await enhanced_grok_strategy.generate_specific_trading_opportunities(
            target_profit=50.0,
            timeframe_days=1,
            starting_capital=1000.0
        )
        
        print(f'✅ Generated {len(opportunities)} trading opportunities')
        
        if opportunities:
            print('\n📈 Sample Trading Opportunities:')
            for i, opp in enumerate(opportunities[:3], 1):  # Show top 3
                print(f'\n   🔸 Opportunity #{i}:')
                print(f'      Symbol: {opp.get("symbol", "N/A")}')
                print(f'      Company: {opp.get("company_name", "N/A")}')
                print(f'      Current Price: ${opp.get("current_price", 0):.2f}')
                print(f'      Entry Price: ${opp.get("entry_price", 0):.2f}')
                print(f'      Target Price: ${opp.get("target_price", 0):.2f}')
                print(f'      Stop Loss: ${opp.get("stop_loss", 0):.2f}')
                print(f'      Position Size: {opp.get("position_size", 0)} shares')
                print(f'      Expected Profit: ${opp.get("expected_profit", 0):.2f}')
                print(f'      Max Risk: ${opp.get("max_risk", 0):.2f}')
                print(f'      Confidence: {opp.get("confidence", 0):.1%}')
                print(f'      Signal Strength: {opp.get("signal_strength", 0)}/5 ⭐')
                print(f'      Setup Type: {opp.get("opportunity_type", "N/A")}')
                print(f'      Catalyst: {opp.get("catalyst", "N/A")}')
                print(f'      Risk/Reward: {opp.get("risk_reward_ratio", 0):.2f}')
                print(f'      Grok Enhanced: {opp.get("grok_enhanced", False)}')
        
        # Test 2: Enhanced Real-Time Scanner
        print('\n\n🔍 Testing Enhanced Real-Time Scanner...')
        
        from atlas_enhanced_realtime_scanner import enhanced_scanner
        print('✅ Enhanced Real-Time Scanner imported successfully')
        
        # Initialize the scanner
        scanner_init = await enhanced_scanner.initialize()
        if scanner_init:
            print('✅ Enhanced Real-Time Scanner initialized successfully')
            
            # Test opportunity scanning
            print('\n🎯 Scanning for trading opportunities...')
            scan_opportunities = await enhanced_scanner.scan_for_trading_opportunities(
                target_profit=50.0,
                timeframe_days=1,
                starting_capital=1000.0
            )
            
            print(f'✅ Scanner found {len(scan_opportunities)} opportunities')
            
            if scan_opportunities:
                print('\n📊 Scanner Results:')
                for i, opp in enumerate(scan_opportunities[:2], 1):  # Show top 2
                    print(f'\n   🔸 Scanner Opportunity #{i}:')
                    print(f'      Symbol: {opp.get("symbol", "N/A")}')
                    print(f'      Opportunity Score: {opp.get("opportunity_score", 0):.2f}')
                    print(f'      Recommended Action: {opp.get("recommended_action", "N/A")}')
        else:
            print('❌ Enhanced Real-Time Scanner initialization failed')
        
        # Test 3: Integration with Trading Plan Engine
        print('\n\n🚀 Testing Integration with Trading Plan Engine...')
        
        from atlas_trading_plan_engine import AtlasTradingPlanEngine
        
        plan_engine = AtlasTradingPlanEngine()
        await plan_engine.initialize()
        print('✅ Trading Plan Engine initialized')
        
        # Generate comprehensive trading plan
        print('\n📋 Generating comprehensive trading plan...')
        trading_plan = await plan_engine.generate_comprehensive_trading_plan(
            target_profit=50.0,
            timeframe_days=1,
            starting_capital=1000.0,
            risk_tolerance="moderate"
        )
        
        print(f'✅ Generated trading plan: {trading_plan.plan_name}')
        print(f'   Plan ID: {trading_plan.plan_id}')
        print(f'   Opportunities: {len(trading_plan.opportunities)}')
        print(f'   Total Capital Allocation: ${trading_plan.target.starting_capital:.2f}')
        print(f'   Target Profit: ${trading_plan.target.profit_amount:.2f}')
        print(f'   Timeframe: {trading_plan.target.timeframe_days} days')
        
        if trading_plan.opportunities:
            print('\n📈 Trading Plan Opportunities:')
            for i, opp in enumerate(trading_plan.opportunities[:2], 1):
                print(f'\n   🔸 Plan Opportunity #{i}:')
                print(f'      Symbol: {opp.symbol}')
                print(f'      Entry: ${opp.entry_price:.2f}')
                print(f'      Target: ${opp.target_price:.2f}')
                print(f'      Stop: ${opp.stop_loss:.2f}')
                print(f'      Expected Profit: ${opp.expected_profit:.2f}')
                print(f'      Confidence: {opp.confidence:.1%}')
                print(f'      Grok Enhanced: {getattr(opp, "grok_enhanced", False)}')
        
        print('\n🎉 Enhanced Atlas V5 Features Test Completed Successfully!')
        
    except Exception as e:
        print(f'\n❌ Enhanced features test failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_features())
