"""
A.T.L.A.S. Real-Time Monitor
Real-time monitoring and performance tracking for the Atlas trading system
"""

import asyncio
import logging
import json
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque

# Core imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'core'))

try:
    from models import EngineStatus
    from config import get_api_config
except ImportError:
    # Fallback definitions
    from enum import Enum
    class EngineStatus(Enum):
        INITIALIZING = "initializing"
        ACTIVE = "active"
        STOPPED = "stopped"
        FAILED = "failed"

    def get_api_config():
        return {}

from atlas_realtime import realtime_core, RealtimeEvent

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    network_io_mb: float
    active_threads: int
    
class PerformanceTracker:
    """Tracks system performance metrics"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history = deque(maxlen=max_history)
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # Network baseline
        self.last_network_io = psutil.net_io_counters()
        self.last_network_time = time.time()
    
    def collect_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Network I/O
            current_network = psutil.net_io_counters()
            current_time = time.time()
            
            time_delta = current_time - self.last_network_time
            bytes_sent_delta = current_network.bytes_sent - self.last_network_io.bytes_sent
            bytes_recv_delta = current_network.bytes_recv - self.last_network_io.bytes_recv
            
            network_io_mb = (bytes_sent_delta + bytes_recv_delta) / (1024 * 1024) / time_delta if time_delta > 0 else 0
            
            # Update network baseline
            self.last_network_io = current_network
            self.last_network_time = current_time
            
            # Thread count
            active_threads = threading.active_count()
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                disk_usage_percent=disk.percent,
                network_io_mb=network_io_mb,
                active_threads=active_threads
            )
            
            with self.lock:
                self.metrics_history.append(metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Metrics collection error: {e}")
            return None
    
    def get_recent_metrics(self, minutes: int = 5) -> List[SystemMetrics]:
        """Get metrics from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        with self.lock:
            return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_average_metrics(self, minutes: int = 5) -> Dict[str, float]:
        """Get average metrics over the last N minutes"""
        recent_metrics = self.get_recent_metrics(minutes)
        
        if not recent_metrics:
            return {}
        
        return {
            "avg_cpu_percent": sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics),
            "avg_memory_percent": sum(m.memory_percent for m in recent_metrics) / len(recent_metrics),
            "avg_memory_used_mb": sum(m.memory_used_mb for m in recent_metrics) / len(recent_metrics),
            "avg_network_io_mb": sum(m.network_io_mb for m in recent_metrics) / len(recent_metrics),
            "max_cpu_percent": max(m.cpu_percent for m in recent_metrics),
            "max_memory_percent": max(m.memory_percent for m in recent_metrics),
            "sample_count": len(recent_metrics)
        }

class AlertManager:
    """Manages real-time alerts and notifications"""
    
    def __init__(self):
        self.active_alerts = {}
        self.alert_history = deque(maxlen=1000)
        self.alert_thresholds = {
            "cpu_high": 80.0,
            "memory_high": 85.0,
            "disk_high": 90.0,
            "network_high": 100.0  # MB/s
        }
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    def check_alerts(self, metrics: SystemMetrics):
        """Check metrics against alert thresholds"""
        alerts_triggered = []
        
        # CPU alert
        if metrics.cpu_percent > self.alert_thresholds["cpu_high"]:
            alert_id = "cpu_high"
            if alert_id not in self.active_alerts:
                alert = {
                    "id": alert_id,
                    "type": "performance",
                    "severity": "warning",
                    "message": f"High CPU usage: {metrics.cpu_percent:.1f}%",
                    "timestamp": metrics.timestamp,
                    "value": metrics.cpu_percent,
                    "threshold": self.alert_thresholds["cpu_high"]
                }
                alerts_triggered.append(alert)
        
        # Memory alert
        if metrics.memory_percent > self.alert_thresholds["memory_high"]:
            alert_id = "memory_high"
            if alert_id not in self.active_alerts:
                alert = {
                    "id": alert_id,
                    "type": "performance",
                    "severity": "warning",
                    "message": f"High memory usage: {metrics.memory_percent:.1f}%",
                    "timestamp": metrics.timestamp,
                    "value": metrics.memory_percent,
                    "threshold": self.alert_thresholds["memory_high"]
                }
                alerts_triggered.append(alert)
        
        # Disk alert
        if metrics.disk_usage_percent > self.alert_thresholds["disk_high"]:
            alert_id = "disk_high"
            if alert_id not in self.active_alerts:
                alert = {
                    "id": alert_id,
                    "type": "performance",
                    "severity": "critical",
                    "message": f"High disk usage: {metrics.disk_usage_percent:.1f}%",
                    "timestamp": metrics.timestamp,
                    "value": metrics.disk_usage_percent,
                    "threshold": self.alert_thresholds["disk_high"]
                }
                alerts_triggered.append(alert)
        
        # Process new alerts
        with self.lock:
            for alert in alerts_triggered:
                self.active_alerts[alert["id"]] = alert
                self.alert_history.append(alert)
                self.logger.warning(f"[MONITOR] Alert triggered: {alert['message']}")
                
                # Publish to real-time event bus
                if realtime_core.status == EngineStatus.ACTIVE:
                    event = RealtimeEvent(
                        event_type="system_alert",
                        timestamp=alert["timestamp"],
                        data=alert,
                        priority=2 if alert["severity"] == "critical" else 1
                    )
                    realtime_core.event_bus.publish(event)
    
    def clear_resolved_alerts(self, metrics: SystemMetrics):
        """Clear alerts that are no longer triggered"""
        resolved_alerts = []
        
        with self.lock:
            for alert_id, alert in list(self.active_alerts.items()):
                resolved = False
                
                if alert_id == "cpu_high" and metrics.cpu_percent <= self.alert_thresholds["cpu_high"] - 5:
                    resolved = True
                elif alert_id == "memory_high" and metrics.memory_percent <= self.alert_thresholds["memory_high"] - 5:
                    resolved = True
                elif alert_id == "disk_high" and metrics.disk_usage_percent <= self.alert_thresholds["disk_high"] - 5:
                    resolved = True
                
                if resolved:
                    resolved_alerts.append(alert_id)
                    del self.active_alerts[alert_id]
                    self.logger.info(f"[MONITOR] Alert resolved: {alert_id}")
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active alerts"""
        with self.lock:
            return list(self.active_alerts.values())

class AtlasRealtimeMonitor:
    """Main real-time monitoring system"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.performance_tracker = PerformanceTracker()
        self.alert_manager = AlertManager()
        self.monitoring_task = None
        self.is_running = False
        self.monitor_interval = 5  # seconds
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize the monitoring system"""
        try:
            self.logger.info("[MONITOR] Initializing real-time monitor...")
            
            # Collect initial metrics
            initial_metrics = self.performance_tracker.collect_metrics()
            if initial_metrics:
                self.logger.info(f"[MONITOR] Initial metrics collected - CPU: {initial_metrics.cpu_percent:.1f}%, Memory: {initial_metrics.memory_percent:.1f}%")
            
            self.status = EngineStatus.ACTIVE
            self.logger.info("[MONITOR] Real-time monitor initialized successfully")
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise
    
    async def start_monitoring(self):
        """Start the monitoring loop"""
        if self.is_running:
            self.logger.warning("[MONITOR] Monitoring is already running")
            return
        
        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("[MONITOR] Real-time monitoring started")
    
    async def stop_monitoring(self):
        """Stop the monitoring loop"""
        self.is_running = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        self.logger.info("[MONITOR] Real-time monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_running:
            try:
                # Collect metrics
                metrics = self.performance_tracker.collect_metrics()
                
                if metrics:
                    # Check for alerts
                    self.alert_manager.check_alerts(metrics)
                    self.alert_manager.clear_resolved_alerts(metrics)
                
                # Wait for next interval
                await asyncio.sleep(self.monitor_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"[MONITOR] Monitoring loop error: {e}")
                await asyncio.sleep(self.monitor_interval)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        current_metrics = self.performance_tracker.collect_metrics()
        avg_metrics = self.performance_tracker.get_average_metrics(5)
        active_alerts = self.alert_manager.get_active_alerts()
        
        return {
            "status": self.status.value,
            "is_monitoring": self.is_running,
            "current_metrics": asdict(current_metrics) if current_metrics else None,
            "average_metrics_5min": avg_metrics,
            "active_alerts": active_alerts,
            "alert_count": len(active_alerts),
            "monitor_interval": self.monitor_interval
        }

# Global monitor instance
realtime_monitor = AtlasRealtimeMonitor()

# Export main components
__all__ = [
    'SystemMetrics',
    'PerformanceTracker',
    'AlertManager', 
    'AtlasRealtimeMonitor',
    'realtime_monitor'
]
