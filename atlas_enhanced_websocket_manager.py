#!/usr/bin/env python3
"""
Atlas Enhanced WebSocket Manager
Provides optimized real-time data streaming with connection recovery and performance monitoring
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Any, Optional, Set, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import weakref
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """WebSocket connection states"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"

@dataclass
class StreamSubscription:
    """Subscription to a data stream"""
    stream_type: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    last_update: Optional[datetime] = None
    update_count: int = 0
    
@dataclass
class ConnectionMetrics:
    """Metrics for WebSocket connections"""
    connection_time: datetime = field(default_factory=datetime.now)
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    last_activity: datetime = field(default_factory=datetime.now)
    reconnection_count: int = 0
    average_latency: float = 0.0

class EnhancedWebSocketConnection:
    """Enhanced WebSocket connection with monitoring and recovery"""
    
    def __init__(self, websocket: WebSocket, connection_id: str):
        self.websocket = websocket
        self.connection_id = connection_id
        self.state = ConnectionState.CONNECTING
        self.subscriptions: Dict[str, StreamSubscription] = {}
        self.metrics = ConnectionMetrics()
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.last_heartbeat: Optional[datetime] = None
        self.heartbeat_interval = 30  # seconds
        self.max_message_size = 1024 * 1024  # 1MB
        self.message_queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        self.send_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the WebSocket connection"""
        try:
            await self.websocket.accept()
            self.state = ConnectionState.CONNECTED
            self.metrics.connection_time = datetime.now()
            
            # Start background tasks
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            self.send_task = asyncio.create_task(self._send_loop())
            
            logger.info(f"✅ WebSocket connection {self.connection_id} initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize WebSocket {self.connection_id}: {e}")
            self.state = ConnectionState.FAILED
            return False
    
    async def send_message(self, message: Dict[str, Any], priority: bool = False):
        """Send message to WebSocket with queuing"""
        try:
            message_str = json.dumps(message)
            message_size = len(message_str.encode('utf-8'))
            
            if message_size > self.max_message_size:
                logger.warning(f"Message too large ({message_size} bytes) for {self.connection_id}")
                return False
            
            # Add to queue (priority messages go to front)
            if priority:
                # For priority messages, we'll send immediately if possible
                if self.message_queue.empty():
                    await self._send_direct(message_str)
                    return True
            
            try:
                self.message_queue.put_nowait((message_str, message_size))
                return True
            except asyncio.QueueFull:
                logger.warning(f"Message queue full for {self.connection_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error queuing message for {self.connection_id}: {e}")
            return False
    
    async def _send_direct(self, message_str: str):
        """Send message directly to WebSocket"""
        try:
            await self.websocket.send_text(message_str)
            self.metrics.messages_sent += 1
            self.metrics.bytes_sent += len(message_str.encode('utf-8'))
            self.metrics.last_activity = datetime.now()
            
        except Exception as e:
            logger.error(f"Direct send failed for {self.connection_id}: {e}")
            self.state = ConnectionState.DISCONNECTED
            raise
    
    async def _send_loop(self):
        """Background task to send queued messages"""
        while self.state == ConnectionState.CONNECTED:
            try:
                # Wait for message with timeout
                try:
                    message_str, message_size = await asyncio.wait_for(
                        self.message_queue.get(), timeout=1.0
                    )
                    
                    await self._send_direct(message_str)
                    
                except asyncio.TimeoutError:
                    continue  # No messages to send
                    
            except Exception as e:
                logger.error(f"Send loop error for {self.connection_id}: {e}")
                break
    
    async def _heartbeat_loop(self):
        """Background heartbeat to keep connection alive"""
        while self.state == ConnectionState.CONNECTED:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                # Send heartbeat
                heartbeat_msg = {
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat(),
                    "connection_id": self.connection_id
                }
                
                await self.send_message(heartbeat_msg, priority=True)
                self.last_heartbeat = datetime.now()
                
            except Exception as e:
                logger.error(f"Heartbeat error for {self.connection_id}: {e}")
                break
    
    async def subscribe_to_stream(self, stream_type: str, parameters: Dict[str, Any] = None):
        """Subscribe to a data stream"""
        subscription_id = f"{stream_type}_{hash(str(parameters or {}))}"
        
        self.subscriptions[subscription_id] = StreamSubscription(
            stream_type=stream_type,
            parameters=parameters or {}
        )
        
        logger.info(f"📡 Connection {self.connection_id} subscribed to {stream_type}")
        return subscription_id
    
    async def unsubscribe_from_stream(self, subscription_id: str):
        """Unsubscribe from a data stream"""
        if subscription_id in self.subscriptions:
            del self.subscriptions[subscription_id]
            logger.info(f"📡 Connection {self.connection_id} unsubscribed from {subscription_id}")
    
    async def close(self):
        """Close the WebSocket connection"""
        self.state = ConnectionState.DISCONNECTED
        
        # Cancel background tasks
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        if self.send_task:
            self.send_task.cancel()
        
        try:
            await self.websocket.close()
        except:
            pass  # Connection might already be closed
        
        logger.info(f"🔌 WebSocket connection {self.connection_id} closed")

class EnhancedWebSocketManager:
    """Enhanced WebSocket manager with streaming optimization"""
    
    def __init__(self):
        self.connections: Dict[str, EnhancedWebSocketConnection] = {}
        self.stream_handlers: Dict[str, Callable] = {}
        self.broadcast_tasks: Dict[str, asyncio.Task] = {}
        self.performance_monitor_task: Optional[asyncio.Task] = None
        self.connection_counter = 0
        
    async def initialize(self):
        """Initialize the WebSocket manager"""
        try:
            # Register default stream handlers
            await self._register_default_streams()
            
            # Start performance monitoring
            self.performance_monitor_task = asyncio.create_task(self._performance_monitor_loop())
            
            logger.info("✅ Enhanced WebSocket Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced WebSocket Manager: {e}")
            return False
    
    async def _register_default_streams(self):
        """Register default data streams"""
        # Lee Method signals stream
        self.stream_handlers["lee_method_signals"] = self._handle_lee_method_stream
        
        # Market data stream
        self.stream_handlers["market_data"] = self._handle_market_data_stream
        
        # Trading alerts stream
        self.stream_handlers["trading_alerts"] = self._handle_alerts_stream
        
        # System status stream
        self.stream_handlers["system_status"] = self._handle_system_status_stream
        
        logger.info(f"📡 Registered {len(self.stream_handlers)} default streams")
    
    async def connect_websocket(self, websocket: WebSocket) -> str:
        """Connect a new WebSocket"""
        self.connection_counter += 1
        connection_id = f"ws_{self.connection_counter}_{int(time.time())}"
        
        connection = EnhancedWebSocketConnection(websocket, connection_id)
        
        if await connection.initialize():
            self.connections[connection_id] = connection
            logger.info(f"🔌 New WebSocket connected: {connection_id}")
            
            # Send welcome message
            welcome_msg = {
                "type": "connection_established",
                "connection_id": connection_id,
                "available_streams": list(self.stream_handlers.keys()),
                "timestamp": datetime.now().isoformat()
            }
            await connection.send_message(welcome_msg)
            
            return connection_id
        else:
            raise Exception("Failed to initialize WebSocket connection")
    
    async def disconnect_websocket(self, connection_id: str):
        """Disconnect a WebSocket"""
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            await connection.close()
            del self.connections[connection_id]
            logger.info(f"🔌 WebSocket disconnected: {connection_id}")
    
    async def handle_message(self, connection_id: str, message: Dict[str, Any]):
        """Handle incoming WebSocket message"""
        if connection_id not in self.connections:
            return
        
        connection = self.connections[connection_id]
        connection.metrics.messages_received += 1
        
        message_type = message.get("type")
        
        if message_type == "subscribe":
            stream_type = message.get("stream_type")
            parameters = message.get("parameters", {})
            
            if stream_type in self.stream_handlers:
                subscription_id = await connection.subscribe_to_stream(stream_type, parameters)
                
                # Start broadcasting for this stream if not already running
                if stream_type not in self.broadcast_tasks:
                    self.broadcast_tasks[stream_type] = asyncio.create_task(
                        self._broadcast_stream(stream_type)
                    )
                
                # Send confirmation
                await connection.send_message({
                    "type": "subscription_confirmed",
                    "stream_type": stream_type,
                    "subscription_id": subscription_id
                })
            else:
                await connection.send_message({
                    "type": "error",
                    "message": f"Unknown stream type: {stream_type}"
                })
        
        elif message_type == "unsubscribe":
            subscription_id = message.get("subscription_id")
            if subscription_id:
                await connection.unsubscribe_from_stream(subscription_id)
                await connection.send_message({
                    "type": "unsubscription_confirmed",
                    "subscription_id": subscription_id
                })
        
        elif message_type == "heartbeat_response":
            # Update connection activity
            connection.metrics.last_activity = datetime.now()
    
    async def _broadcast_stream(self, stream_type: str):
        """Broadcast data for a specific stream type"""
        handler = self.stream_handlers.get(stream_type)
        if not handler:
            return
        
        while stream_type in self.broadcast_tasks:
            try:
                # Get data from stream handler
                data = await handler()
                
                if data:
                    # Find connections subscribed to this stream
                    subscribed_connections = []
                    for conn_id, connection in self.connections.items():
                        for sub_id, subscription in connection.subscriptions.items():
                            if subscription.stream_type == stream_type:
                                subscribed_connections.append(connection)
                                subscription.last_update = datetime.now()
                                subscription.update_count += 1
                                break
                    
                    # Broadcast to subscribed connections
                    if subscribed_connections:
                        broadcast_message = {
                            "type": "stream_data",
                            "stream_type": stream_type,
                            "data": data,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        # Send to all subscribed connections concurrently
                        send_tasks = [
                            conn.send_message(broadcast_message)
                            for conn in subscribed_connections
                        ]
                        
                        await asyncio.gather(*send_tasks, return_exceptions=True)
                
                # Wait before next broadcast
                await asyncio.sleep(1.0)  # 1 second interval
                
            except Exception as e:
                logger.error(f"Broadcast error for {stream_type}: {e}")
                await asyncio.sleep(5.0)  # Wait longer on error
    
    async def _handle_lee_method_stream(self) -> Optional[Dict[str, Any]]:
        """Handle Lee Method signals stream"""
        try:
            # This would integrate with the actual Lee Method scanner
            # For now, return mock data
            return {
                "signals_count": 0,
                "last_scan": datetime.now().isoformat(),
                "status": "scanning"
            }
        except Exception as e:
            logger.error(f"Lee Method stream error: {e}")
            return None
    
    async def _handle_market_data_stream(self) -> Optional[Dict[str, Any]]:
        """Handle market data stream"""
        try:
            # This would integrate with real market data
            return {
                "market_status": "open",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Market data stream error: {e}")
            return None
    
    async def _handle_alerts_stream(self) -> Optional[Dict[str, Any]]:
        """Handle trading alerts stream"""
        try:
            return {
                "active_alerts": 0,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Alerts stream error: {e}")
            return None
    
    async def _handle_system_status_stream(self) -> Optional[Dict[str, Any]]:
        """Handle system status stream"""
        try:
            return {
                "status": "operational",
                "active_connections": len(self.connections),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"System status stream error: {e}")
            return None
    
    async def _performance_monitor_loop(self):
        """Monitor WebSocket performance"""
        while True:
            try:
                await asyncio.sleep(60)  # Monitor every minute
                
                # Log performance metrics
                total_connections = len(self.connections)
                total_messages_sent = sum(conn.metrics.messages_sent for conn in self.connections.values())
                total_messages_received = sum(conn.metrics.messages_received for conn in self.connections.values())
                
                logger.info(f"📊 WebSocket Performance: {total_connections} connections, "
                           f"{total_messages_sent} sent, {total_messages_received} received")
                
            except Exception as e:
                logger.error(f"Performance monitor error: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get WebSocket performance metrics"""
        total_connections = len(self.connections)
        active_streams = len(self.broadcast_tasks)
        
        connection_metrics = []
        for conn_id, connection in self.connections.items():
            connection_metrics.append({
                "connection_id": conn_id,
                "state": connection.state.value,
                "messages_sent": connection.metrics.messages_sent,
                "messages_received": connection.metrics.messages_received,
                "subscriptions": len(connection.subscriptions),
                "uptime_seconds": (datetime.now() - connection.metrics.connection_time).total_seconds()
            })
        
        return {
            "total_connections": total_connections,
            "active_streams": active_streams,
            "stream_types": list(self.stream_handlers.keys()),
            "connections": connection_metrics
        }
    
    async def close(self):
        """Close all WebSocket connections"""
        # Cancel broadcast tasks
        for task in self.broadcast_tasks.values():
            task.cancel()
        
        # Close all connections
        close_tasks = [conn.close() for conn in self.connections.values()]
        await asyncio.gather(*close_tasks, return_exceptions=True)
        
        # Cancel performance monitor
        if self.performance_monitor_task:
            self.performance_monitor_task.cancel()
        
        logger.info("Enhanced WebSocket Manager closed")

# Global instance
enhanced_websocket_manager = EnhancedWebSocketManager()
