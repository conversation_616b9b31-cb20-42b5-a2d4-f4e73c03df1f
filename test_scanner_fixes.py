#!/usr/bin/env python3
"""
A.T.L.A.S. Scanner Module - Comprehensive Test Suite
Tests all the critical fixes implemented for the scanner module
"""

import asyncio
import logging
import pytz
from datetime import datetime, time as dt_time
from typing import Dict, List, Any
import sys
import os

# Add current directory to path for imports
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class ScannerTestSuite:
    """Comprehensive test suite for scanner fixes"""
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
    
    async def run_all_tests(self):
        """Run all scanner tests"""
        logger.info("🧪 Starting A.T.L.A.S. Scanner Test Suite")
        logger.info("=" * 60)
        
        # Test 1: Import Tests
        await self.test_scanner_imports()
        
        # Test 2: Market Hours Detection
        await self.test_market_hours_detection()
        
        # Test 3: Pattern Detection Sensitivity
        await self.test_pattern_detection_sensitivity()
        
        # Test 4: Market Status Validation
        await self.test_market_status_validation()
        
        # Test 5: Scanner Integration
        await self.test_scanner_integration()
        
        # Test 6: Configuration Validation
        await self.test_configuration_validation()
        
        # Print results
        self.print_test_summary()
    
    async def test_scanner_imports(self):
        """Test 1: Verify all scanner imports work correctly"""
        test_name = "Scanner Import Tests"
        logger.info(f"🔍 Running {test_name}...")
        
        try:
            # Test Lee Method Scanner imports
            from atlas_lee_method import LeeMethodScanner, AtlasLeeMethodRealtimeScanner, LeeMethodSignal
            logger.info("✅ Lee Method Scanner imports successful")
            
            # Test Realtime Scanner imports
            from atlas_realtime_scanner import AtlasRealtimeScanner, ScannerConfig, ScannerStatus
            logger.info("✅ Realtime Scanner imports successful")
            
            # Test Enhanced Scanner imports
            from atlas_enhanced_realtime_scanner import EnhancedRealtimeScanner
            logger.info("✅ Enhanced Scanner imports successful")
            
            # Test instantiation
            lee_scanner = LeeMethodScanner()
            realtime_scanner = AtlasRealtimeScanner()
            enhanced_scanner = EnhancedRealtimeScanner()
            
            logger.info("✅ All scanner instantiations successful")
            
            self.record_test_result(test_name, True, "All scanner imports and instantiations successful")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"Import error: {e}")
    
    async def test_market_hours_detection(self):
        """Test 2: Verify market hours detection uses Eastern Time correctly"""
        test_name = "Market Hours Detection"
        logger.info(f"🔍 Running {test_name}...")
        
        try:
            from atlas_lee_method import LeeMethodScanner
            from atlas_realtime_scanner import AtlasRealtimeScanner
            
            # Test Lee Method Scanner market hours
            lee_scanner = LeeMethodScanner()
            lee_market_status = lee_scanner.get_market_status()
            
            # Test Realtime Scanner market hours
            realtime_scanner = AtlasRealtimeScanner()
            realtime_market_hours = realtime_scanner._is_market_hours()
            
            # Verify both scanners report consistent market status
            lee_market_open = lee_market_status['market_open']
            
            if lee_market_open == realtime_market_hours:
                logger.info(f"✅ Market hours detection consistent: {lee_market_open}")
                
                # Verify Eastern Time is being used
                et_tz = pytz.timezone('US/Eastern')
                current_time_et = datetime.now(et_tz).time()
                market_open = dt_time(9, 30)
                market_close = dt_time(16, 0)
                is_weekday = datetime.now(et_tz).weekday() < 5
                
                expected_market_hours = (market_open <= current_time_et <= market_close) and is_weekday
                
                if lee_market_open == expected_market_hours:
                    logger.info("✅ Eastern Time market hours calculation verified")
                    self.record_test_result(test_name, True, f"Market hours detection working correctly (Market: {'OPEN' if lee_market_open else 'CLOSED'})")
                else:
                    self.record_test_result(test_name, False, f"Market hours calculation mismatch: expected {expected_market_hours}, got {lee_market_open}")
            else:
                self.record_test_result(test_name, False, f"Inconsistent market hours: Lee={lee_market_open}, Realtime={realtime_market_hours}")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"Market hours test error: {e}")
    
    async def test_pattern_detection_sensitivity(self):
        """Test 3: Verify pattern detection has strict sensitivity settings"""
        test_name = "Pattern Detection Sensitivity"
        logger.info(f"🔍 Running {test_name}...")
        
        try:
            from atlas_lee_method import LeeMethodScanner
            
            scanner = LeeMethodScanner()
            
            # Check configuration values
            expected_config = {
                'confidence_threshold': 0.65,
                'min_confidence_threshold': 0.65,
                'pattern_sensitivity': 0.5,
                'allow_weak_signals': False,
                'use_flexible_patterns': False
            }
            
            config_checks = []
            for key, expected_value in expected_config.items():
                actual_value = getattr(scanner, key)
                if actual_value == expected_value:
                    config_checks.append(f"✅ {key}: {actual_value}")
                else:
                    config_checks.append(f"❌ {key}: expected {expected_value}, got {actual_value}")
            
            all_correct = all("✅" in check for check in config_checks)
            
            for check in config_checks:
                logger.info(check)
            
            if all_correct:
                self.record_test_result(test_name, True, "All pattern detection sensitivity settings correct")
            else:
                self.record_test_result(test_name, False, "Some pattern detection settings incorrect")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"Pattern sensitivity test error: {e}")
    
    async def test_market_status_validation(self):
        """Test 4: Verify market status validation and signal clearing"""
        test_name = "Market Status Validation"
        logger.info(f"🔍 Running {test_name}...")
        
        try:
            from atlas_lee_method import LeeMethodScanner, AtlasLeeMethodRealtimeScanner
            
            # Test Lee Method Scanner market status
            lee_scanner = LeeMethodScanner()
            market_status = lee_scanner.get_market_status()
            
            required_fields = ['market_open', 'market_status', 'current_time_et', 'is_weekday', 
                             'market_hours_only', 'scanner_config', 'active_signals_count']
            
            missing_fields = [field for field in required_fields if field not in market_status]
            
            if not missing_fields:
                logger.info("✅ Market status contains all required fields")
                
                # Test signal validation
                lee_scanner.validate_and_clean_signals()
                logger.info("✅ Signal validation method executed successfully")
                
                # Test realtime scanner status
                realtime_scanner = AtlasLeeMethodRealtimeScanner()
                scanner_status = realtime_scanner.get_scanner_status()
                
                if 'market_status' in scanner_status:
                    logger.info("✅ Realtime scanner includes market status")
                    self.record_test_result(test_name, True, "Market status validation working correctly")
                else:
                    self.record_test_result(test_name, False, "Realtime scanner missing market status")
            else:
                self.record_test_result(test_name, False, f"Missing market status fields: {missing_fields}")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"Market status validation error: {e}")
    
    async def test_scanner_integration(self):
        """Test 5: Verify scanner integration works correctly"""
        test_name = "Scanner Integration"
        logger.info(f"🔍 Running {test_name}...")
        
        try:
            from atlas_realtime_scanner import AtlasRealtimeScanner
            
            # Test scanner initialization
            scanner = AtlasRealtimeScanner()
            await scanner.initialize()
            
            logger.info("✅ Scanner initialization successful")
            
            # Test scanner status
            status = await scanner.get_scanner_status()
            
            required_status_fields = ['status', 'is_running', 'scan_count', 'active_signals', 
                                    'market_hours', 'market_status', 'config']
            
            missing_status_fields = [field for field in required_status_fields if field not in status]
            
            if not missing_status_fields:
                logger.info("✅ Scanner status contains all required fields")
                logger.info(f"   Market Status: {status['market_status']}")
                logger.info(f"   Scanner Status: {status['status']}")
                self.record_test_result(test_name, True, "Scanner integration working correctly")
            else:
                self.record_test_result(test_name, False, f"Missing status fields: {missing_status_fields}")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"Scanner integration error: {e}")
    
    async def test_configuration_validation(self):
        """Test 6: Verify all scanner configurations are production-ready"""
        test_name = "Configuration Validation"
        logger.info(f"🔍 Running {test_name}...")
        
        try:
            from atlas_realtime_scanner import AtlasRealtimeScanner, ScannerConfig
            
            # Test default configuration
            config = ScannerConfig()
            
            production_config = {
                'enabled': True,
                'market_hours_only': True,
                'min_confidence_threshold': 0.65,
                'pattern_sensitivity': 0.5,
                'allow_weak_signals': False
            }
            
            config_validation = []
            for key, expected_value in production_config.items():
                actual_value = getattr(config, key)
                if actual_value == expected_value:
                    config_validation.append(f"✅ {key}: {actual_value}")
                else:
                    config_validation.append(f"❌ {key}: expected {expected_value}, got {actual_value}")
            
            all_valid = all("✅" in check for check in config_validation)
            
            for check in config_validation:
                logger.info(check)
            
            if all_valid:
                self.record_test_result(test_name, True, "All configurations are production-ready")
            else:
                self.record_test_result(test_name, False, "Some configurations need adjustment")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"Configuration validation error: {e}")
    
    def record_test_result(self, test_name: str, passed: bool, message: str):
        """Record test result"""
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        
        if passed:
            self.passed_tests += 1
            logger.info(f"✅ {test_name}: PASSED - {message}")
        else:
            self.failed_tests += 1
            logger.error(f"❌ {test_name}: FAILED - {message}")
    
    def print_test_summary(self):
        """Print comprehensive test summary"""
        logger.info("=" * 60)
        logger.info("🎯 A.T.L.A.S. Scanner Test Suite Results")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {self.passed_tests}")
        logger.info(f"Failed: {self.failed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        logger.info("")
        
        if self.failed_tests > 0:
            logger.info("❌ Failed Tests:")
            for result in self.test_results:
                if not result['passed']:
                    logger.info(f"   - {result['test']}: {result['message']}")
            logger.info("")
        
        if success_rate >= 90:
            logger.info("🎉 Scanner module is ready for production!")
        elif success_rate >= 70:
            logger.info("⚠️  Scanner module needs minor fixes before production")
        else:
            logger.info("🚨 Scanner module requires significant fixes before production")

async def main():
    """Run the comprehensive scanner test suite"""
    test_suite = ScannerTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
