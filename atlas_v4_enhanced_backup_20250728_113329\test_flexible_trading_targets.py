#!/usr/bin/env python3
"""
Test Flexible Trading Targets for A.T.L.A.S. v5.0
Verifies that the system accepts dynamic user inputs for target amounts and timeframes
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FlexibleTradingTargetTester:
    """Test flexible trading target functionality"""
    
    def __init__(self, base_url="http://localhost:8002"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_chat_with_target(self, message, expected_amount=None, expected_timeframe=None):
        """Test chat with specific trading target"""
        logger.info(f"🧪 Testing: '{message}'")
        
        try:
            chat_data = {
                "message": message,
                "session_id": "flexible_target_test"
            }
            
            url = f"{self.base_url}/api/v1/chat"
            async with self.session.post(url, json=chat_data, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    response_text = result.get('response', '').lower()
                    
                    # Check if the response acknowledges the specific target
                    if expected_amount:
                        amount_str = str(expected_amount)
                        if amount_str in response_text or f"${amount_str}" in response_text:
                            logger.info(f"✅ Target amount ${expected_amount} acknowledged")
                        else:
                            logger.warning(f"⚠️ Target amount ${expected_amount} not clearly acknowledged")
                    
                    if expected_timeframe:
                        if expected_timeframe.lower() in response_text:
                            logger.info(f"✅ Timeframe '{expected_timeframe}' acknowledged")
                        else:
                            logger.warning(f"⚠️ Timeframe '{expected_timeframe}' not clearly acknowledged")
                    
                    # Check that it's not defaulting to $50 when a different amount was expected
                    if "$50" in response_text:
                        if expected_amount is not None and expected_amount != 50:
                            logger.error(f"❌ System defaulted to $50 instead of ${expected_amount}")
                            return False
                        elif expected_amount is None:
                            logger.error(f"❌ System defaulted to $50 instead of ${expected_amount}")
                            return False
                    
                    logger.info(f"📝 Response preview: {response_text[:200]}...")
                    return True
                else:
                    logger.error(f"❌ Chat API returned {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return False
    
    async def run_comprehensive_tests(self):
        """Run comprehensive flexible trading target tests"""
        logger.info("🚀 Starting Flexible Trading Target Tests")
        logger.info("=" * 60)
        
        test_cases = [
            # Different dollar amounts
            {
                "message": "I want to make $100 today",
                "expected_amount": 100,
                "expected_timeframe": "today",
                "description": "Small target, 1 day"
            },
            {
                "message": "Help me earn $500 in 1 week",
                "expected_amount": 500,
                "expected_timeframe": "1 week",
                "description": "Medium target, 1 week"
            },
            {
                "message": "I need to generate $1000 in 2 weeks",
                "expected_amount": 1000,
                "expected_timeframe": "2 weeks",
                "description": "Large target, 2 weeks"
            },
            {
                "message": "Can you help me make $2500 in 1 month?",
                "expected_amount": 2500,
                "expected_timeframe": "1 month",
                "description": "Very large target, 1 month"
            },
            {
                "message": "I want to profit $200 in 3 days",
                "expected_amount": 200,
                "expected_timeframe": "3 days",
                "description": "Custom timeframe"
            },
            {
                "message": "Generate $750 over 10 days",
                "expected_amount": 750,
                "expected_timeframe": "10 days",
                "description": "Specific day count"
            },
            {
                "message": "Make me $300 by next week",
                "expected_amount": 300,
                "expected_timeframe": "next week",
                "description": "Relative timeframe"
            },
            {
                "message": "I need $150 profit in 5 days",
                "expected_amount": 150,
                "expected_timeframe": "5 days",
                "description": "Short-term target"
            },
            # Test without specific amounts (should not default to $50)
            {
                "message": "I want to make money trading",
                "expected_amount": None,
                "expected_timeframe": None,
                "description": "General request (no $50 default)"
            },
            {
                "message": "Help me with a profitable trading strategy",
                "expected_amount": None,
                "expected_timeframe": None,
                "description": "Strategy request (no $50 default)"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n📋 Test {i}/{len(test_cases)}: {test_case['description']}")
            
            success = await self.test_chat_with_target(
                test_case["message"],
                test_case["expected_amount"],
                test_case["expected_timeframe"]
            )
            
            results.append({
                "test": test_case["description"],
                "message": test_case["message"],
                "success": success
            })
            
            # Small delay between tests
            await asyncio.sleep(2)
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 FLEXIBLE TRADING TARGET TEST RESULTS")
        logger.info("=" * 60)
        
        passed = sum(1 for result in results if result["success"])
        total = len(results)
        
        for result in results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            logger.info(f"   {result['test']}: {status}")
        
        logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All flexible trading target tests PASSED!")
            logger.info("✅ System successfully accepts dynamic target amounts and timeframes")
            logger.info("✅ No hardcoded $50/day defaults found")
            return True
        else:
            logger.error("❌ Some flexible trading target tests FAILED!")
            logger.error("⚠️ System may still have hardcoded defaults")
            return False

async def main():
    """Main test function"""
    async with FlexibleTradingTargetTester() as tester:
        success = await tester.run_comprehensive_tests()
        return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
