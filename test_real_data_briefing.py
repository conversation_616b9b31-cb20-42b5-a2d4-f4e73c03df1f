#!/usr/bin/env python3
"""
Test script for A.T.L.A.S. Morning Briefing with REAL market data
Verifies that NO mock data is used - only real API data for live trading
"""

import asyncio
import logging
import sys

# Add current directory to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

async def test_real_data_briefing():
    """Test morning briefing with real data verification"""
    logger.info("🧪 Testing A.T.L.A.S. Morning Briefing - REAL DATA ONLY")
    logger.info("=" * 70)
    logger.info("⚠️  CRITICAL: This is for LIVE PAPER TRADING - NO MOCK DATA ALLOWED")
    logger.info("=" * 70)
    
    try:
        from atlas_morning_briefing import morning_briefing
        
        # Test API initialization
        logger.info("\n🔍 Test 1: API Configuration Check...")
        if morning_briefing.fmp_config:
            if morning_briefing.fmp_config.get('api_key'):
                logger.info("✅ FMP API key configured")
            else:
                logger.warning("⚠️ FMP API key missing - real data unavailable")
        else:
            logger.warning("⚠️ FMP API not configured")
        
        # Test real quote fetching
        logger.info("\n🔍 Test 2: Real Quote Data Fetching...")
        test_symbols = ['SPY', 'QQQ', 'VIX']
        
        for symbol in test_symbols:
            quote_data = await morning_briefing._fetch_real_quote(symbol)
            if quote_data:
                logger.info(f"✅ Real data for {symbol}: ${quote_data.get('price', 0):.2f} ({quote_data.get('change_percent', 0):+.2f}%)")
                logger.info(f"   Source: {quote_data.get('source', 'Unknown')}")
            else:
                logger.warning(f"⚠️ No real data available for {symbol}")
        
        # Test sector ETF data
        logger.info("\n🔍 Test 3: Real Sector ETF Data...")
        sector_etfs = ['XLE', 'XLY', 'XLK', 'XLV', 'XLF']
        
        for etf in sector_etfs:
            etf_data = await morning_briefing._fetch_sector_etf_data(etf)
            if etf_data:
                logger.info(f"✅ Real sector data for {etf}: {etf_data.get('change_percent', 0):+.2f}%")
            else:
                logger.warning(f"⚠️ No real data available for {etf}")
        
        # Test VIX data
        logger.info("\n🔍 Test 4: Real VIX Data...")
        vix_level = await morning_briefing._fetch_vix_data()
        if vix_level:
            logger.info(f"✅ Real VIX level: {vix_level:.2f}")
        else:
            logger.warning("⚠️ No real VIX data available")
        
        # Generate full briefing and verify data sources
        logger.info("\n🔍 Test 5: Full Briefing Generation...")
        briefing = await morning_briefing.generate_morning_briefing()
        
        # Verify no mock data in briefing
        logger.info("\n🔍 Test 6: Mock Data Verification...")
        mock_data_found = False
        
        # Check major indexes for real data
        for index_name, index_data in briefing.major_indexes.items():
            source = index_data.get('source', 'Unknown')
            if source == 'REAL_DATA':
                logger.info(f"✅ {index_name}: Real data confirmed")
            elif source == 'ERROR':
                logger.warning(f"⚠️ {index_name}: Data fetch failed - API issue")
            else:
                logger.error(f"❌ {index_name}: Unexpected data source: {source}")
                mock_data_found = True
        
        # Check VIX level
        if briefing.vix_level > 0:
            logger.info(f"✅ VIX: Real data confirmed ({briefing.vix_level:.2f})")
        else:
            logger.warning("⚠️ VIX: No real data available")
        
        # Check trade setups (should be from real Lee Method scanner)
        if briefing.top_setups:
            logger.info(f"✅ Trade setups: {len(briefing.top_setups)} real signals from Lee Method scanner")
        else:
            logger.info("ℹ️ Trade setups: No signals found (normal if no patterns detected)")
        
        # Final verification
        if not mock_data_found:
            logger.info("\n🎉 VERIFICATION PASSED: No mock data detected")
            logger.info("✅ System ready for live paper trading")
        else:
            logger.error("\n❌ VERIFICATION FAILED: Mock data detected")
            logger.error("🚨 System NOT ready for live trading")
        
        # Display briefing
        logger.info("\n" + "="*80)
        logger.info("📊 REAL DATA MORNING BRIEFING:")
        logger.info("="*80)
        
        briefing_text = morning_briefing.format_briefing_for_chat(briefing)
        print(briefing_text)
        
        logger.info("="*80)
        
        return not mock_data_found
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

async def test_api_connectivity():
    """Test API connectivity and configuration"""
    logger.info("\n🔍 API Connectivity Test...")
    
    try:
        from config import get_api_config
        
        # Test FMP API
        fmp_config = get_api_config('fmp')
        if fmp_config and fmp_config.get('api_key'):
            logger.info("✅ FMP API configuration found")
            
            # Test actual API call
            import aiohttp
            url = "https://financialmodelingprep.com/api/v3/quote/AAPL"
            params = {'apikey': fmp_config['api_key']}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and len(data) > 0:
                            logger.info("✅ FMP API connectivity confirmed")
                            logger.info(f"   Sample data: AAPL at ${data[0].get('price', 0):.2f}")
                            return True
                        else:
                            logger.error("❌ FMP API returned empty data")
                    else:
                        logger.error(f"❌ FMP API error: {response.status}")
        else:
            logger.error("❌ FMP API not configured - check .env file")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ API connectivity test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🌅 A.T.L.A.S. Real Data Briefing Test")
    print("=" * 50)
    print("⚠️  LIVE PAPER TRADING VERIFICATION")
    print("=" * 50)
    
    # Test API connectivity first
    api_ok = await test_api_connectivity()
    
    if not api_ok:
        print("\n❌ API connectivity failed - check your .env configuration")
        print("Required: FMP_API_KEY in .env file")
        return
    
    # Run real data tests
    success = await test_real_data_briefing()
    
    print("\n" + "="*50)
    if success:
        print("🎯 LIVE TRADING VERIFICATION: PASSED")
        print("✅ System ready for paper trading with real data")
    else:
        print("🚨 LIVE TRADING VERIFICATION: FAILED") 
        print("❌ System NOT ready - mock data detected")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
