"""
A.T.L.A.S. Validation Integration
Integrates comprehensive validation systems with existing A.T.L.A.S. architecture
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from functools import wraps

# Import validation framework
from atlas_comprehensive_validation_framework import validation_framework, ValidationResult
from atlas_data_quality_monitor import DataQualityLevel
from atlas_user_safety_system import AlertSeverity

logger = logging.getLogger(__name__)

def validate_market_data(func):
    """Decorator to validate market data before processing"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            # Extract symbol and data from function arguments
            symbol = None
            data = None
            source = "unknown"
            
            # Try to extract from common argument patterns
            if len(args) >= 2:
                symbol = args[0] if isinstance(args[0], str) else None
                data = args[1] if isinstance(args[1], dict) else None
            
            if 'symbol' in kwargs:
                symbol = kwargs['symbol']
            if 'data' in kwargs:
                data = kwargs['data']
            if 'source' in kwargs:
                source = kwargs['source']
            
            # Perform validation if we have the required data
            if symbol and data:
                validation_result = await validation_framework.validate_market_data_comprehensive(
                    symbol, data, source
                )
                
                # Add validation result to kwargs for the function to use
                kwargs['_validation_result'] = validation_result
                
                # Log validation results
                if not validation_result.is_valid:
                    logger.warning(f"[VALIDATION_FAILED] {symbol}: {validation_result.safety_status}")
                    for warning in validation_result.warnings:
                        logger.warning(f"  - {warning}")
                
                # Proceed with function if validation passes or is acceptable
                if validation_result.is_valid or validation_result.quality_level != DataQualityLevel.CRITICAL:
                    return await func(*args, **kwargs)
                else:
                    # Return error response for critical validation failures
                    return {
                        'error': 'Data validation failed',
                        'validation_result': validation_result.__dict__,
                        'message': 'Data quality is too poor for safe processing'
                    }
            else:
                # No validation data available, proceed normally
                return await func(*args, **kwargs)
                
        except Exception as e:
            logger.error(f"Validation decorator error: {e}")
            # Proceed with original function if validation fails
            return await func(*args, **kwargs)
    
    return wrapper

def validate_trading_signal(func):
    """Decorator to validate trading signals before execution"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            # Extract trading signal from arguments
            signal = None
            
            if 'signal' in kwargs:
                signal = kwargs['signal']
            elif len(args) > 0 and hasattr(args[0], 'signal_type'):
                signal = args[0]
            
            if signal:
                validation_result = await validation_framework.validate_trading_signal_comprehensive(signal)
                kwargs['_signal_validation'] = validation_result
                
                # Log validation results
                if not validation_result.is_valid:
                    logger.warning(f"[SIGNAL_VALIDATION_FAILED] {signal.symbol}: {validation_result.safety_status}")
                
                # Add safety warnings to response
                if validation_result.warnings:
                    logger.info(f"[SIGNAL_WARNINGS] {signal.symbol}: {len(validation_result.warnings)} warnings")
            
            return await func(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Signal validation decorator error: {e}")
            return await func(*args, **kwargs)
    
    return wrapper

class AtlasValidationIntegration:
    """Integration layer for A.T.L.A.S. validation systems"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_framework = validation_framework
        self.integration_active = False
        
        # API endpoints for validation status
        self.validation_endpoints = {
            '/api/v1/validation/status': self.get_validation_status,
            '/api/v1/validation/data-quality': self.get_data_quality_status,
            '/api/v1/validation/trading-accuracy': self.get_trading_accuracy_status,
            '/api/v1/validation/user-safety': self.get_user_safety_status,
            '/api/v1/validation/alerts': self.get_active_alerts
        }
        
        self.logger.info("[VALIDATION_INTEGRATION] Integration layer initialized")
    
    async def initialize(self):
        """Initialize validation integration"""
        try:
            self.integration_active = True
            
            # Start validation framework monitoring
            asyncio.create_task(self.validation_framework.start_continuous_monitoring())
            
            self.logger.info("[VALIDATION_INTEGRATION] Integration active")
            
        except Exception as e:
            self.logger.error(f"Validation integration initialization failed: {e}")
            raise
    
    async def get_validation_status(self, request=None) -> Dict[str, Any]:
        """Get comprehensive validation status for API"""
        try:
            status = self.validation_framework.get_validation_framework_status()
            
            # Add integration-specific information
            status['integration_active'] = self.integration_active
            status['validation_endpoints'] = list(self.validation_endpoints.keys())
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting validation status: {e}")
            return {'error': str(e)}
    
    async def get_data_quality_status(self, request=None) -> Dict[str, Any]:
        """Get data quality status for API"""
        try:
            return self.validation_framework.data_quality_monitor.get_data_quality_report()
        except Exception as e:
            return {'error': str(e)}
    
    async def get_trading_accuracy_status(self, request=None) -> Dict[str, Any]:
        """Get trading accuracy status for API"""
        try:
            return self.validation_framework.trading_accuracy_monitor.get_accuracy_report()
        except Exception as e:
            return {'error': str(e)}
    
    async def get_user_safety_status(self, request=None) -> Dict[str, Any]:
        """Get user safety status for API"""
        try:
            return self.validation_framework.user_safety_system.get_user_safety_status()
        except Exception as e:
            return {'error': str(e)}
    
    async def get_active_alerts(self, request=None) -> Dict[str, Any]:
        """Get active alerts for API"""
        try:
            safety_status = self.validation_framework.user_safety_system.get_user_safety_status()
            
            # Combine all alerts
            all_alerts = []
            for severity, alerts in safety_status.get('active_alerts', {}).items():
                for alert in alerts:
                    alert['severity'] = severity
                    all_alerts.append(alert)
            
            # Add data quality warnings
            for warning in safety_status.get('data_quality_warnings', []):
                all_alerts.append({
                    'alert_id': f"dq_{warning['symbol']}_{warning['issue_type']}",
                    'title': f"Data Quality: {warning['issue_type']}",
                    'message': warning['message'],
                    'severity': warning['severity'],
                    'timestamp': warning['timestamp'],
                    'symbol': warning['symbol'],
                    'data_source': warning['data_source']
                })
            
            return {
                'timestamp': datetime.now().isoformat(),
                'total_alerts': len(all_alerts),
                'alerts': sorted(all_alerts, key=lambda x: x['timestamp'], reverse=True),
                'alert_summary': {
                    'INFO': len([a for a in all_alerts if a['severity'] == 'INFO']),
                    'WARNING': len([a for a in all_alerts if a['severity'] == 'WARNING']),
                    'CRITICAL': len([a for a in all_alerts if a['severity'] == 'CRITICAL']),
                    'EMERGENCY': len([a for a in all_alerts if a['severity'] == 'EMERGENCY'])
                }
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_validation_middleware(self):
        """Get middleware for web framework integration"""
        async def validation_middleware(request, handler):
            """Middleware to add validation context to requests"""
            try:
                # Add validation status to request context
                request['validation_status'] = await self.get_validation_status()
                
                # Process request
                response = await handler(request)
                
                # Add validation headers to response
                if hasattr(response, 'headers'):
                    response.headers['X-Atlas-Validation-Active'] = 'true'
                    response.headers['X-Atlas-Data-Quality'] = request['validation_status'].get('overall_health', 'UNKNOWN')
                
                return response
                
            except Exception as e:
                self.logger.error(f"Validation middleware error: {e}")
                return await handler(request)
        
        return validation_middleware
    
    def get_websocket_validation_updates(self):
        """Get WebSocket handler for real-time validation updates"""
        async def websocket_handler(websocket, path):
            """WebSocket handler for real-time validation updates"""
            try:
                self.logger.info(f"[WEBSOCKET] Validation updates client connected: {path}")
                
                while True:
                    # Send validation status update
                    status = await self.get_validation_status()
                    await websocket.send(json.dumps({
                        'type': 'validation_status',
                        'data': status,
                        'timestamp': datetime.now().isoformat()
                    }))
                    
                    # Send active alerts
                    alerts = await self.get_active_alerts()
                    if alerts.get('total_alerts', 0) > 0:
                        await websocket.send(json.dumps({
                            'type': 'validation_alerts',
                            'data': alerts,
                            'timestamp': datetime.now().isoformat()
                        }))
                    
                    await asyncio.sleep(30)  # Update every 30 seconds
                    
            except Exception as e:
                self.logger.error(f"WebSocket validation handler error: {e}")
        
        return websocket_handler
    
    async def integrate_with_market_engine(self, market_engine):
        """Integrate validation with existing market engine"""
        try:
            # Wrap market engine methods with validation
            if hasattr(market_engine, 'get_quote'):
                original_get_quote = market_engine.get_quote
                market_engine.get_quote = validate_market_data(original_get_quote)
            
            if hasattr(market_engine, 'get_historical_data'):
                original_get_historical = market_engine.get_historical_data
                market_engine.get_historical_data = validate_market_data(original_get_historical)
            
            self.logger.info("[INTEGRATION] Market engine validation integration complete")
            
        except Exception as e:
            self.logger.error(f"Market engine integration error: {e}")
    
    async def integrate_with_trading_engine(self, trading_engine):
        """Integrate validation with existing trading engine"""
        try:
            # Wrap trading engine methods with validation
            if hasattr(trading_engine, 'generate_signal'):
                original_generate_signal = trading_engine.generate_signal
                trading_engine.generate_signal = validate_trading_signal(original_generate_signal)
            
            if hasattr(trading_engine, 'execute_trade'):
                original_execute_trade = trading_engine.execute_trade
                trading_engine.execute_trade = validate_trading_signal(original_execute_trade)
            
            self.logger.info("[INTEGRATION] Trading engine validation integration complete")
            
        except Exception as e:
            self.logger.error(f"Trading engine integration error: {e}")
    
    def create_validation_dashboard_data(self) -> Dict[str, Any]:
        """Create data for validation dashboard display"""
        try:
            status = self.validation_framework.get_validation_framework_status()
            
            # Create dashboard-friendly data structure
            dashboard_data = {
                'overall_health': status.get('overall_health', 'UNKNOWN'),
                'health_color': self._get_health_color(status.get('overall_health', 'UNKNOWN')),
                'data_sources': [],
                'recent_alerts': [],
                'performance_metrics': {},
                'safety_status': 'UNKNOWN'
            }
            
            # Data sources status
            data_quality = status.get('data_quality_status', {})
            for source, source_data in data_quality.get('data_sources', {}).items():
                dashboard_data['data_sources'].append({
                    'name': source.upper(),
                    'status': source_data.get('status', 'UNKNOWN'),
                    'quality_score': source_data.get('quality_score', 0),
                    'last_update': source_data.get('last_successful_fetch')
                })
            
            # Recent alerts
            user_safety = status.get('user_safety_status', {})
            for severity, alerts in user_safety.get('active_alerts', {}).items():
                for alert in alerts[:5]:  # Top 5 alerts
                    dashboard_data['recent_alerts'].append({
                        'severity': severity,
                        'title': alert.get('title', 'Unknown Alert'),
                        'timestamp': alert.get('timestamp'),
                        'action_required': alert.get('user_action_required', False)
                    })
            
            # Performance metrics
            trading_accuracy = status.get('trading_accuracy_status', {})
            dashboard_data['performance_metrics'] = {
                'lee_method_accuracy': trading_accuracy.get('lee_method_accuracy', {}).get('accuracy', 0),
                'six_point_accuracy': trading_accuracy.get('six_point_analysis_accuracy', 0),
                'total_alerts': user_safety.get('total_active_alerts', 0)
            }
            
            dashboard_data['safety_status'] = user_safety.get('safety_status', 'UNKNOWN')
            
            return dashboard_data
            
        except Exception as e:
            self.logger.error(f"Error creating dashboard data: {e}")
            return {'error': str(e)}
    
    def _get_health_color(self, health_status: str) -> str:
        """Get color code for health status"""
        color_map = {
            'EXCELLENT': '#28a745',  # Green
            'GOOD': '#6f42c1',       # Purple
            'WARNING': '#ffc107',    # Yellow
            'DEGRADED': '#fd7e14',   # Orange
            'CRITICAL': '#dc3545'    # Red
        }
        return color_map.get(health_status, '#6c757d')  # Gray for unknown

# Global integration instance
validation_integration = AtlasValidationIntegration()
