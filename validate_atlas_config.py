#!/usr/bin/env python3
"""
A.T.L.A.S. Configuration Validation Script
Validates all configuration settings for consistency across the system
"""

import os
import sys
import json
import requests
from pathlib import Path
from typing import Dict, List, Tuple

class AtlasConfigValidator:
    """Validates A.T.L.A.S. configuration consistency"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.config_files = [
            'config.py',
            '.env',
            '.env.example',
            'atlas_interface.html',
            'atlas_production_server.py'
        ]
        
    def validate_all(self) -> Dict:
        """Run all validation checks"""
        print("🔍 A.T.L.A.S. Configuration Validation")
        print("=" * 50)
        
        # Check port consistency
        self.validate_port_consistency()
        
        # Check WebSocket endpoints
        self.validate_websocket_endpoints()
        
        # Check API endpoints
        self.validate_api_endpoints()
        
        # Check file existence
        self.validate_file_existence()
        
        # Test connectivity
        self.test_connectivity()
        
        return self.generate_report()
    
    def validate_port_consistency(self):
        """Check port configuration consistency"""
        print("\n📡 Validating Port Configuration...")
        
        ports_found = {}
        
        # Check config.py
        if os.path.exists('config.py'):
            with open('config.py', 'r', encoding='utf-8') as f:
                content = f.read()
                if 'PORT' in content:
                    # Extract port from config.py
                    for line in content.split('\n'):
                        if 'self.PORT = int(os.getenv("PORT"' in line:
                            default_port = line.split('"')[-2]
                            ports_found['config.py'] = default_port
        
        # Check .env files
        for env_file in ['.env', '.env.example']:
            if os.path.exists(env_file):
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('PORT='):
                            ports_found[env_file] = line.split('=')[1].strip()
        
        # Check HTML file
        if os.path.exists('atlas_interface.html'):
            with open('atlas_interface.html', 'r', encoding='utf-8') as f:
                content = f.read()
                if 'window.location.port ||' in content:
                    # Extract default port from HTML
                    for line in content.split('\n'):
                        if 'window.location.port ||' in line and '8002' in line:
                            ports_found['atlas_interface.html'] = '8002'
        
        # Validate consistency
        unique_ports = set(ports_found.values())
        if len(unique_ports) > 1:
            self.issues.append(f"Port inconsistency found: {ports_found}")
        else:
            print(f"✅ Port configuration consistent: {list(unique_ports)[0] if unique_ports else 'Not found'}")
    
    def validate_websocket_endpoints(self):
        """Check WebSocket endpoint consistency"""
        print("\n🔌 Validating WebSocket Endpoints...")
        
        endpoints_found = []
        
        if os.path.exists('atlas_interface.html'):
            with open('atlas_interface.html', 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                if '/ws/scanner' in content:
                    endpoints_found.append('/ws/scanner')
                if '/ws/${sessionId}' in content:
                    endpoints_found.append('/ws/${sessionId}')
        
        if os.path.exists('atlas_production_server.py'):
            with open('atlas_production_server.py', 'r', encoding='utf-8') as f:
                content = f.read()
                if '@app.websocket("/ws/scanner")' in content:
                    endpoints_found.append('/ws/scanner (backend)')
        
        print(f"📡 WebSocket endpoints found: {endpoints_found}")
        
        if '/ws/${sessionId}' in endpoints_found:
            self.warnings.append("Dynamic WebSocket endpoint '/ws/${sessionId}' found - ensure backend supports this pattern")
    
    def validate_api_endpoints(self):
        """Check API endpoint consistency"""
        print("\n🌐 Validating API Endpoints...")
        
        # Common API endpoints to check
        expected_endpoints = [
            '/api/v1/health',
            '/api/v1/lee_method/signals',
            '/api/v1/chat/message',
            '/api/v1/market_data/{symbol}'
        ]
        
        if os.path.exists('atlas_production_server.py'):
            with open('atlas_production_server.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
                for endpoint in expected_endpoints:
                    # Check for endpoint definition
                    endpoint_pattern = endpoint.replace('{symbol}', '')
                    if endpoint_pattern in content:
                        print(f"✅ Found endpoint: {endpoint}")
                    else:
                        self.warnings.append(f"Endpoint not found in server: {endpoint}")
    
    def validate_file_existence(self):
        """Check if required files exist"""
        print("\n📁 Validating File Existence...")
        
        required_files = [
            'atlas_production_server.py',
            'atlas_interface.html',
            'config.py',
            'requirements.txt'
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
            else:
                self.issues.append(f"Required file missing: {file_path}")
    
    def test_connectivity(self):
        """Test actual connectivity to the server"""
        print("\n🔗 Testing Connectivity...")
        
        # Try to connect to health endpoint
        ports_to_test = ['8002', '8001', '8080']
        
        for port in ports_to_test:
            try:
                response = requests.get(f'http://localhost:{port}/api/v1/health', timeout=5)
                if response.status_code == 200:
                    print(f"✅ Server responding on port {port}")
                    return
            except requests.exceptions.RequestException:
                continue
        
        self.warnings.append("No server found responding on common ports (8002, 8001, 8080)")
    
    def generate_report(self) -> Dict:
        """Generate validation report"""
        print("\n" + "=" * 50)
        print("📊 VALIDATION REPORT")
        print("=" * 50)
        
        if not self.issues and not self.warnings:
            print("✅ All configuration checks passed!")
            status = "PASS"
        elif self.issues:
            print("❌ Critical issues found:")
            for issue in self.issues:
                print(f"  • {issue}")
            status = "FAIL"
        else:
            status = "PASS_WITH_WARNINGS"
        
        if self.warnings:
            print("⚠️ Warnings:")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        return {
            "status": status,
            "issues": self.issues,
            "warnings": self.warnings,
            "summary": {
                "total_issues": len(self.issues),
                "total_warnings": len(self.warnings)
            }
        }

def main():
    """Main validation function"""
    validator = AtlasConfigValidator()
    report = validator.validate_all()
    
    # Exit with appropriate code
    if report["status"] == "FAIL":
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
