#!/usr/bin/env python3
"""
CRITICAL PRODUCTION RELIABILITY TEST for Atlas V4 Enhanced
Tests confidence scores, error handling, and system stability
"""

import asyncio
import json
import httpx
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionReliabilityTester:
    def __init__(self):
        self.base_url = "http://localhost:8002"
        self.min_confidence_trading = 0.85
        self.min_confidence_educational = 0.75
        self.min_confidence_general = 0.70
        self.test_results = []
        
    async def run_comprehensive_tests(self):
        """Run all production reliability tests"""
        logger.info("🚀 STARTING ATLAS V4 ENHANCED PRODUCTION RELIABILITY TESTS")
        logger.info("=" * 80)
        
        test_suites = [
            ("Confidence Score Validation", self.test_confidence_scores),
            ("Error Handling Verification", self.test_error_handling),
            ("Lee Method Integration", self.test_lee_method_integration),
            ("Stock Analysis Reliability", self.test_stock_analysis),
            ("System Stability", self.test_system_stability)
        ]
        
        overall_success = True
        
        for suite_name, test_method in test_suites:
            logger.info(f"\n📋 TESTING: {suite_name}")
            logger.info("-" * 50)
            
            try:
                success = await test_method()
                if not success:
                    overall_success = False
                    logger.error(f"❌ FAILED: {suite_name}")
                else:
                    logger.info(f"✅ PASSED: {suite_name}")
            except Exception as e:
                logger.error(f"❌ EXCEPTION in {suite_name}: {e}")
                overall_success = False
        
        # Generate final report
        await self.generate_final_report(overall_success)
        return overall_success
    
    async def test_confidence_scores(self):
        """Test that confidence scores meet production standards"""
        test_cases = [
            {
                "name": "Greeting Response",
                "message": "Hello",
                "expected_min_confidence": self.min_confidence_general,
                "expected_intent": "greeting"
            },
            {
                "name": "Stock Analysis Request",
                "message": "What is AAPL trading at?",
                "expected_min_confidence": self.min_confidence_trading,
                "expected_intent": "stock_analysis"
            },
            {
                "name": "Educational Query",
                "message": "How does the Lee Method work?",
                "expected_min_confidence": self.min_confidence_educational,
                "expected_intent": "education"
            },
            {
                "name": "Trading Plan Request",
                "message": "I want to make $5000 in 30 days",
                "expected_min_confidence": self.min_confidence_trading,
                "expected_intent": "trading_plan"
            }
        ]
        
        all_passed = True
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for test_case in test_cases:
                try:
                    response = await client.post(
                        f"{self.base_url}/api/v1/chat/message",
                        json={
                            "message": test_case["message"],
                            "context": "general",
                            "session_id": f"confidence_test_{test_case['name'].lower().replace(' ', '_')}"
                        }
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        confidence = data.get('confidence', 0.0)
                        intent = data.get('intent_type', 'unknown')
                        
                        # Check confidence threshold
                        if confidence >= test_case["expected_min_confidence"]:
                            logger.info(f"✅ {test_case['name']}: Confidence {confidence:.3f} >= {test_case['expected_min_confidence']:.3f}")
                        else:
                            logger.error(f"❌ {test_case['name']}: Confidence {confidence:.3f} < {test_case['expected_min_confidence']:.3f}")
                            all_passed = False
                        
                        # Check for errors in response
                        response_text = data.get('response', '')
                        if "error" in response_text.lower() or "exception" in response_text.lower():
                            logger.error(f"❌ {test_case['name']}: Error detected in response")
                            all_passed = False
                        
                        self.test_results.append({
                            "test": test_case['name'],
                            "confidence": confidence,
                            "intent": intent,
                            "passed": confidence >= test_case["expected_min_confidence"]
                        })
                        
                    else:
                        logger.error(f"❌ {test_case['name']}: HTTP {response.status_code}")
                        all_passed = False
                        
                except Exception as e:
                    logger.error(f"❌ {test_case['name']}: Exception {e}")
                    all_passed = False
                
                await asyncio.sleep(1)  # Rate limiting
        
        return all_passed
    
    async def test_error_handling(self):
        """Test error handling and graceful degradation"""
        test_cases = [
            {
                "name": "Invalid Symbol",
                "message": "What is INVALIDSTOCK trading at?",
                "should_handle_gracefully": True
            },
            {
                "name": "Empty Message",
                "message": "",
                "should_handle_gracefully": True
            },
            {
                "name": "Very Long Message",
                "message": "A" * 1000,
                "should_handle_gracefully": True
            }
        ]
        
        all_passed = True
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for test_case in test_cases:
                try:
                    response = await client.post(
                        f"{self.base_url}/api/v1/chat/message",
                        json={
                            "message": test_case["message"],
                            "context": "general",
                            "session_id": f"error_test_{test_case['name'].lower().replace(' ', '_')}"
                        }
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        response_text = data.get('response', '')
                        
                        # Should not crash or return empty response
                        if len(response_text.strip()) > 0:
                            logger.info(f"✅ {test_case['name']}: Handled gracefully")
                        else:
                            logger.error(f"❌ {test_case['name']}: Empty response")
                            all_passed = False
                    else:
                        logger.error(f"❌ {test_case['name']}: HTTP {response.status_code}")
                        all_passed = False
                        
                except Exception as e:
                    logger.error(f"❌ {test_case['name']}: Exception {e}")
                    all_passed = False
                
                await asyncio.sleep(1)
        
        return all_passed
    
    async def test_lee_method_integration(self):
        """Test Lee Method integration specifically"""
        test_cases = [
            "How does the Lee Method work?",
            "Run a Lee Method scan",
            "Show me Lee Method signals"
        ]
        
        all_passed = True
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for i, message in enumerate(test_cases):
                try:
                    response = await client.post(
                        f"{self.base_url}/api/v1/chat/message",
                        json={
                            "message": message,
                            "context": "general",
                            "session_id": f"lee_method_test_{i}"
                        }
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        response_text = data.get('response', '')
                        confidence = data.get('confidence', 0.0)
                        
                        # Should not contain "NoneType" errors
                        if "NoneType" not in response_text and "'NoneType' object has no attribute" not in response_text:
                            logger.info(f"✅ Lee Method Test {i+1}: No NoneType errors")
                        else:
                            logger.error(f"❌ Lee Method Test {i+1}: NoneType error detected")
                            all_passed = False
                        
                        # Should have reasonable confidence even for errors
                        if confidence >= 0.30:  # Minimum for any response
                            logger.info(f"✅ Lee Method Test {i+1}: Confidence {confidence:.3f}")
                        else:
                            logger.error(f"❌ Lee Method Test {i+1}: Confidence too low {confidence:.3f}")
                            all_passed = False
                            
                    else:
                        logger.error(f"❌ Lee Method Test {i+1}: HTTP {response.status_code}")
                        all_passed = False
                        
                except Exception as e:
                    logger.error(f"❌ Lee Method Test {i+1}: Exception {e}")
                    all_passed = False
                
                await asyncio.sleep(1)
        
        return all_passed
    
    async def test_stock_analysis(self):
        """Test stock analysis functionality"""
        symbols = ["AAPL", "MSFT", "GOOGL"]
        all_passed = True
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for symbol in symbols:
                try:
                    response = await client.post(
                        f"{self.base_url}/api/v1/chat/message",
                        json={
                            "message": f"Analyze {symbol} stock",
                            "context": "general",
                            "session_id": f"stock_test_{symbol.lower()}"
                        }
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        confidence = data.get('confidence', 0.0)
                        
                        if confidence >= self.min_confidence_trading:
                            logger.info(f"✅ {symbol} Analysis: Confidence {confidence:.3f}")
                        else:
                            logger.error(f"❌ {symbol} Analysis: Confidence {confidence:.3f} too low")
                            all_passed = False
                            
                    else:
                        logger.error(f"❌ {symbol} Analysis: HTTP {response.status_code}")
                        all_passed = False
                        
                except Exception as e:
                    logger.error(f"❌ {symbol} Analysis: Exception {e}")
                    all_passed = False
                
                await asyncio.sleep(1)
        
        return all_passed
    
    async def test_system_stability(self):
        """Test system stability under load"""
        logger.info("Testing system stability with rapid requests...")
        
        all_passed = True
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            tasks = []
            for i in range(10):  # 10 concurrent requests
                task = client.post(
                    f"{self.base_url}/api/v1/chat/message",
                    json={
                        "message": f"Hello test {i}",
                        "context": "general",
                        "session_id": f"stability_test_{i}"
                    }
                )
                tasks.append(task)
            
            try:
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                
                success_count = 0
                for i, response in enumerate(responses):
                    if isinstance(response, Exception):
                        logger.error(f"❌ Stability Test {i}: Exception {response}")
                        all_passed = False
                    elif response.status_code == 200:
                        success_count += 1
                    else:
                        logger.error(f"❌ Stability Test {i}: HTTP {response.status_code}")
                        all_passed = False
                
                logger.info(f"✅ Stability Test: {success_count}/10 requests successful")
                if success_count < 8:  # Allow 2 failures
                    all_passed = False
                    
            except Exception as e:
                logger.error(f"❌ Stability Test: Exception {e}")
                all_passed = False
        
        return all_passed
    
    async def generate_final_report(self, overall_success):
        """Generate final test report"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 FINAL PRODUCTION RELIABILITY REPORT")
        logger.info("=" * 80)
        
        if overall_success:
            logger.info("🎉 OVERALL STATUS: PRODUCTION READY ✅")
            logger.info("✅ All confidence scores meet minimum thresholds")
            logger.info("✅ Error handling is robust and graceful")
            logger.info("✅ Lee Method integration is stable")
            logger.info("✅ System handles concurrent requests")
        else:
            logger.error("⚠️  OVERALL STATUS: NEEDS ATTENTION ❌")
            logger.error("❌ Some tests failed - review logs above")
            logger.error("❌ System not ready for production trading")
        
        # Summary statistics
        if self.test_results:
            avg_confidence = sum(r['confidence'] for r in self.test_results) / len(self.test_results)
            passed_count = sum(1 for r in self.test_results if r['passed'])
            
            logger.info(f"\n📈 CONFIDENCE STATISTICS:")
            logger.info(f"   Average Confidence: {avg_confidence:.3f}")
            logger.info(f"   Tests Passed: {passed_count}/{len(self.test_results)}")
            logger.info(f"   Success Rate: {passed_count/len(self.test_results)*100:.1f}%")
        
        logger.info(f"\n⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 80)

async def main():
    """Run production reliability tests"""
    tester = ProductionReliabilityTester()
    success = await tester.run_comprehensive_tests()
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
