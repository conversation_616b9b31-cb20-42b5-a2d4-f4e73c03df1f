apiVersion: v1
kind: Secret
metadata:
  name: atlas-api-keys
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: secrets
type: Opaque
data:
  # Base64 encoded API keys (replace with actual values)
  # Use: echo -n "your_api_key_here" | base64
  GROK_API_KEY: eW91cl9ncm9rX2FwaV9rZXlfaGVyZQ==
  ALPACA_API_KEY: eW91cl9hbHBhY2FfYXBpX2tleV9oZXJl
  ALPACA_SECRET_KEY: eW91cl9hbHBhY2Ffc2VjcmV0X2tleV9oZXJl
  FMP_API_KEY: eW91cl9mbXBfYXBpX2tleV9oZXJl
  OPENAI_API_KEY: eW91cl9vcGVuYWlfYXBpX2tleV9oZXJl
  
  # Database credentials (if using)
  DB_USERNAME: YXRsYXNfdXNlcg==
  DB_PASSWORD: c2VjdXJlX3Bhc3N3b3JkXzEyMw==
  
  # Redis credentials (if using)
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmRfMTIz
  
  # JWT secret for session management
  JWT_SECRET_KEY: c3VwZXJfc2VjdXJlX2p3dF9rZXlfZm9yX3Byb2R1Y3Rpb24=
  
  # Encryption key for sensitive data
  ENCRYPTION_KEY: ZW5jcnlwdGlvbl9rZXlfZm9yX3NlbnNpdGl2ZV9kYXRh
---
apiVersion: v1
kind: Secret
metadata:
  name: atlas-tls-cert
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: tls
type: kubernetes.io/tls
data:
  # TLS certificate and key (replace with actual values)
  # Use: cat cert.pem | base64 -w 0
  # Use: cat key.pem | base64 -w 0
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...
---
apiVersion: v1
kind: Secret
metadata:
  name: atlas-monitoring-auth
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: monitoring
type: Opaque
data:
  # Monitoring system authentication
  PROMETHEUS_USERNAME: cHJvbWV0aGV1cw==
  PROMETHEUS_PASSWORD: bW9uaXRvcmluZ19wYXNzd29yZA==
  GRAFANA_ADMIN_PASSWORD: Z3JhZmFuYV9hZG1pbl9wYXNzd29yZA==
