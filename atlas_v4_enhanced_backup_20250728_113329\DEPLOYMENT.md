# A.T.L.A.S. Multi-Agent System - Production Deployment Guide

## Overview

This guide covers the production deployment of the A.T.L.A.S. (Advanced Trading & Learning Analysis System) Multi-Agent System using Kubernetes, Docker, and comprehensive monitoring.

## Architecture

The production deployment includes:

- **Multi-Agent System**: 6 specialized agents working in coordination
- **Auto-scaling**: Horizontal Pod Autoscaler for dynamic scaling
- **Monitoring**: Prometheus metrics and health checks
- **Security**: RBAC, Network policies, and encrypted secrets
- **High Availability**: Multiple replicas with anti-affinity rules
- **Load Balancing**: NGINX ingress with rate limiting

## Prerequisites

### Required Tools

- Docker 20.10+
- Kubernetes 1.25+
- kubectl configured for your cluster
- Helm 3.0+ (optional, for monitoring stack)

### Required Resources

- **Minimum**: 8 CPU cores, 16GB RAM
- **Recommended**: 16 CPU cores, 32GB RAM
- **Storage**: 100GB+ for logs and metrics

### Required Secrets

Before deployment, you must configure the following API keys:

1. **Grok API Key**: For AI-powered analysis
2. **Alpaca API Keys**: For market data and trading
3. **Financial Modeling Prep API Key**: For financial data
4. **JWT Secret**: For session management
5. **Encryption Key**: For sensitive data encryption

## Quick Start

### 1. Clone and Configure

```bash
git clone <repository-url>
cd atlas_v4_enhanced

# Copy and edit environment configuration
cp .env.production .env.local
# Edit .env.local with your specific values
```

### 2. Configure Secrets

Edit `k8s/secrets.yaml` and replace the base64-encoded values with your actual API keys:

```bash
# Encode your API keys
echo -n "your_actual_grok_api_key" | base64
echo -n "your_actual_alpaca_api_key" | base64
# ... etc
```

### 3. Deploy

```bash
# Full deployment
./deploy.sh deploy

# Or step by step
./deploy.sh build
./deploy.sh push
./deploy.sh deploy
```

### 4. Verify Deployment

```bash
# Check status
./deploy.sh status

# View logs
./deploy.sh logs

# Test health endpoint
kubectl port-forward -n atlas-trading service/atlas-service 8080:80
curl http://localhost:8080/api/v1/monitoring/health
```

## Configuration

### Environment Variables

Key configuration options in `.env.production`:

```bash
# Core settings
ENVIRONMENT=production
ORCHESTRATION_MODE=hybrid
MAX_CONCURRENT_REQUESTS=100

# Security
SESSION_TIMEOUT=3600
RATE_LIMIT_WINDOW=60
MAX_REQUESTS_PER_WINDOW=100

# Monitoring
PROMETHEUS_PORT=8000
HEALTH_CHECK_INTERVAL=30
```

### Kubernetes Resources

The deployment creates:

- **Namespace**: `atlas-trading`
- **Deployment**: 3-20 replicas with auto-scaling
- **Services**: ClusterIP and headless services
- **Ingress**: NGINX with TLS termination
- **ConfigMaps**: Application configuration
- **Secrets**: API keys and certificates
- **PVCs**: Persistent storage for logs and metrics

### Auto-scaling Configuration

```yaml
# HPA settings
minReplicas: 3
maxReplicas: 20
targetCPUUtilization: 70%
targetMemoryUtilization: 80%
```

## Monitoring

### Prometheus Metrics

The system exposes comprehensive metrics:

- **Agent Performance**: Request counts, durations, confidence scores
- **System Resources**: CPU, memory, disk usage
- **Trading Metrics**: Signal generation, compliance checks
- **Security Events**: Authentication, rate limiting

### Health Checks

Multiple health check endpoints:

- `/api/v1/monitoring/health` - Overall system health
- `/api/v1/monitoring/metrics` - Performance metrics
- `/api/v1/monitoring/dashboard` - Comprehensive dashboard

### Alerting Rules

Pre-configured alerts for:

- High CPU/memory usage (>80%)
- Agent failures (>5 in 5 minutes)
- Compliance violations
- Component health issues

## Security

### Network Security

- **Network Policies**: Restrict pod-to-pod communication
- **Ingress Rules**: Rate limiting and IP restrictions
- **TLS Termination**: HTTPS with Let's Encrypt certificates

### RBAC Configuration

- **Service Accounts**: Minimal required permissions
- **Roles**: Namespace-scoped access control
- **Pod Security**: Non-root containers, read-only filesystem

### Secrets Management

- **Encrypted Storage**: All secrets encrypted at rest
- **Secret Rotation**: Automated key rotation support
- **Audit Logging**: All secret access logged

## Scaling

### Horizontal Scaling

Automatic scaling based on:

- CPU utilization (70% threshold)
- Memory utilization (80% threshold)
- Request rate (10 requests/second per pod)

### Vertical Scaling

Resource limits per pod:

```yaml
resources:
  requests:
    cpu: 1000m
    memory: 2Gi
  limits:
    cpu: 2000m
    memory: 4Gi
```

## Troubleshooting

### Common Issues

1. **Pod Startup Failures**
   ```bash
   kubectl describe pod -n atlas-trading -l app=atlas-multi-agent-system
   kubectl logs -n atlas-trading -l app=atlas-multi-agent-system
   ```

2. **API Key Issues**
   ```bash
   kubectl get secrets -n atlas-trading
   kubectl describe secret atlas-api-keys -n atlas-trading
   ```

3. **Network Connectivity**
   ```bash
   kubectl exec -n atlas-trading -it <pod-name> -- curl http://localhost:8001/health
   ```

### Performance Issues

1. **High CPU Usage**
   - Check agent workload distribution
   - Verify auto-scaling configuration
   - Review resource limits

2. **Memory Leaks**
   - Monitor memory usage trends
   - Check garbage collection metrics
   - Review log retention settings

3. **Slow Response Times**
   - Check external API latency
   - Verify database connection pooling
   - Review caching configuration

### Debugging Commands

```bash
# Get all resources
kubectl get all -n atlas-trading

# Check events
kubectl get events -n atlas-trading --sort-by='.lastTimestamp'

# Pod shell access
kubectl exec -n atlas-trading -it <pod-name> -- /bin/bash

# Port forwarding for debugging
kubectl port-forward -n atlas-trading service/atlas-service 8080:80
kubectl port-forward -n atlas-trading service/prometheus 9090:9090
```

## Maintenance

### Updates

```bash
# Update deployment
./deploy.sh update

# Rollback if needed
./deploy.sh rollback
```

### Backup

```bash
# Backup configuration
kubectl get all -n atlas-trading -o yaml > atlas-backup.yaml

# Backup persistent data
kubectl exec -n atlas-trading <pod-name> -- tar czf - /app/logs | gzip > logs-backup.tar.gz
```

### Log Management

```bash
# View recent logs
kubectl logs -n atlas-trading -l app=atlas-multi-agent-system --tail=100

# Follow logs
kubectl logs -n atlas-trading -l app=atlas-multi-agent-system -f

# Export logs
kubectl logs -n atlas-trading -l app=atlas-multi-agent-system --since=24h > atlas-logs.txt
```

## Performance Optimization

### Resource Tuning

1. **CPU Optimization**
   - Adjust worker processes based on CPU cores
   - Tune garbage collection settings
   - Optimize async operations

2. **Memory Optimization**
   - Configure appropriate heap sizes
   - Implement memory pooling
   - Monitor for memory leaks

3. **I/O Optimization**
   - Use SSD storage for databases
   - Implement connection pooling
   - Cache frequently accessed data

### Monitoring Optimization

1. **Metrics Collection**
   - Adjust collection intervals
   - Implement metric sampling
   - Use efficient storage formats

2. **Log Management**
   - Implement log rotation
   - Use structured logging
   - Compress archived logs

## Support

### Documentation

- [API Documentation](./API.md)
- [Agent Architecture](./AGENTS.md)
- [Security Guide](./SECURITY.md)
- [Monitoring Guide](./MONITORING.md)

### Contact

For production support:
- Email: <EMAIL>
- Slack: #atlas-production
- On-call: +1-XXX-XXX-XXXX

### SLA

- **Uptime**: 99.9%
- **Response Time**: <10 seconds (95th percentile)
- **Recovery Time**: <15 minutes for critical issues
