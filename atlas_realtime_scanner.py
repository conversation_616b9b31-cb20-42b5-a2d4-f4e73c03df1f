"""
A.T.L.A.S. Real-Time Scanner - Production Implementation
Integrates Lee Method Scanner with Enhanced Realtime Scanner for comprehensive market scanning.
"""

import asyncio
import logging
import pytz
from datetime import datetime, time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Import working scanner implementations
from atlas_lee_method import LeeMethodScanner, AtlasLeeMethodRealtimeScanner, LeeMethodSignal
from atlas_enhanced_realtime_scanner import EnhancedRealtimeScanner, ScannerConfig as EnhancedConfig

logger = logging.getLogger(__name__)

class ScannerStatus(Enum):
    """Scanner status enumeration"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    ERROR = "error"

@dataclass
class ScannerConfig:
    """Scanner configuration with market hours support"""
    enabled: bool = True
    market_hours_only: bool = True
    scan_interval: int = 12
    min_confidence_threshold: float = 0.65  # Increased from 0.4
    pattern_sensitivity: float = 0.5         # Decreased from 0.7 (more strict)
    allow_weak_signals: bool = False         # Disabled weak signals
    max_symbols: int = 50

    # Market hours in Eastern Time
    market_open: time = time(9, 30)  # 9:30 AM ET
    market_close: time = time(16, 0)  # 4:00 PM ET

@dataclass
class ScanResult:
    """Scan result data structure"""
    symbol: str
    timestamp: datetime
    signal_strength: float
    confidence: float
    pattern_type: str
    current_price: float
    volume_ratio: float
    market_hours: bool

@dataclass
class PatternAlert:
    """Pattern alert data structure"""
    symbol: str
    alert_type: str
    message: str
    timestamp: datetime
    confidence: float

class AtlasRealtimeScanner:
    """Production A.T.L.A.S. Real-Time Scanner with proper market hours detection"""

    def __init__(self, config: ScannerConfig = None):
        self.config = config or ScannerConfig()
        self.logger = logging.getLogger(__name__)
        self.status = ScannerStatus.STOPPED
        self.scan_count = 0
        self.active_signals: Dict[str, ScanResult] = {}
        self.alerts: List[PatternAlert] = []

        # Initialize scanner components
        self.lee_method_scanner = LeeMethodScanner()
        self.enhanced_scanner = EnhancedRealtimeScanner()
        self.realtime_scanner = AtlasLeeMethodRealtimeScanner()

        # Configure Lee Method scanner with stricter settings
        self.lee_method_scanner.configure_pattern_sensitivity(
            use_flexible_patterns=False,  # Disable flexible patterns
            min_confidence_threshold=self.config.min_confidence_threshold,
            pattern_sensitivity=self.config.pattern_sensitivity,
            allow_weak_signals=self.config.allow_weak_signals
        )

        self.logger.info("✅ AtlasRealtimeScanner initialized with production settings")

    def _is_market_hours(self) -> bool:
        """Check if current time is within market hours (Eastern Time)"""
        if not self.config.market_hours_only:
            return True

        try:
            # Get current time in Eastern Time
            et_tz = pytz.timezone('US/Eastern')
            current_time_et = datetime.now(et_tz).time()

            # Check if within market hours
            is_market_hours = self.config.market_open <= current_time_et <= self.config.market_close

            # Check if it's a weekday (Monday=0, Sunday=6)
            current_date_et = datetime.now(et_tz)
            is_weekday = current_date_et.weekday() < 5  # Monday-Friday

            return is_market_hours and is_weekday

        except Exception as e:
            self.logger.error(f"Error checking market hours: {e}")
            return False

    async def initialize(self):
        """Initialize the scanner components"""
        try:
            self.logger.info("🔄 Initializing A.T.L.A.S. Real-Time Scanner...")

            # Initialize Lee Method scanner
            await self.lee_method_scanner.initialize()

            # Initialize enhanced scanner
            # Enhanced scanner doesn't have initialize method, it's ready on creation

            self.status = ScannerStatus.RUNNING
            self.logger.info("✅ Scanner initialization completed")
            return True

        except Exception as e:
            self.logger.error(f"❌ Scanner initialization failed: {e}")
            self.status = ScannerStatus.ERROR
            return False

    async def start_scanner(self):
        """Start the real-time scanner"""
        if self.status == ScannerStatus.RUNNING:
            self.logger.warning("Scanner is already running")
            return True

        try:
            self.status = ScannerStatus.STARTING
            self.logger.info("🚀 Starting A.T.L.A.S. Real-Time Scanner...")

            # Start the realtime scanner component
            await self.realtime_scanner.start_scanning()

            self.status = ScannerStatus.RUNNING
            self.logger.info("✅ Scanner started successfully")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to start scanner: {e}")
            self.status = ScannerStatus.ERROR
            return False

    async def stop_scanner(self):
        """Stop the real-time scanner"""
        try:
            self.logger.info("🛑 Stopping A.T.L.A.S. Real-Time Scanner...")

            # Stop the realtime scanner component
            await self.realtime_scanner.stop_scanning()

            self.status = ScannerStatus.STOPPED
            self.logger.info("✅ Scanner stopped successfully")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to stop scanner: {e}")
            return False

    async def scan_symbol(self, symbol: str) -> Optional[ScanResult]:
        """Scan a single symbol for patterns"""
        if not self._is_market_hours():
            self.logger.debug(f"Markets closed, skipping scan for {symbol}")
            return None

        try:
            # Use Lee Method scanner for pattern detection
            signal = await self.lee_method_scanner.scan_symbol(symbol)

            if signal and signal.confidence >= self.config.min_confidence_threshold:
                # Convert to ScanResult
                scan_result = ScanResult(
                    symbol=symbol,
                    timestamp=signal.timestamp,
                    signal_strength=signal.signal_strength.value,
                    confidence=signal.confidence,
                    pattern_type=signal.pattern_type,
                    current_price=signal.current_price,
                    volume_ratio=signal.volume_ratio,
                    market_hours=self._is_market_hours()
                )

                # Store active signal
                self.active_signals[symbol] = scan_result
                self.scan_count += 1

                return scan_result

            return None

        except Exception as e:
            self.logger.error(f"Error scanning {symbol}: {e}")
            return None

    async def get_active_signals(self) -> List[ScanResult]:
        """Get all active signals"""
        if not self._is_market_hours():
            # Clear signals when markets are closed
            self.active_signals.clear()
            return []

        # Get signals from realtime scanner
        realtime_signals = self.realtime_scanner.active_signals

        # Convert to ScanResult format
        results = []
        for symbol, signal in realtime_signals.items():
            if signal.confidence >= self.config.min_confidence_threshold:
                scan_result = ScanResult(
                    symbol=symbol,
                    timestamp=signal.timestamp,
                    signal_strength=signal.signal_strength.value,
                    confidence=signal.confidence,
                    pattern_type=signal.pattern_type,
                    current_price=signal.current_price,
                    volume_ratio=signal.volume_ratio,
                    market_hours=self._is_market_hours()
                )
                results.append(scan_result)

        return results

    async def get_scanner_status(self) -> Dict[str, Any]:
        """Get comprehensive scanner status"""
        market_hours = self._is_market_hours()

        return {
            "status": self.status.value,
            "is_running": self.status == ScannerStatus.RUNNING,
            "scan_count": self.scan_count,
            "active_signals": len(self.active_signals),
            "market_hours": market_hours,
            "market_status": "OPEN" if market_hours else "CLOSED",
            "config": {
                "min_confidence": self.config.min_confidence_threshold,
                "pattern_sensitivity": self.config.pattern_sensitivity,
                "allow_weak_signals": self.config.allow_weak_signals,
                "market_hours_only": self.config.market_hours_only
            },
            "last_update": datetime.now().isoformat()
        }

# Export the main classes for compatibility
__all__ = ['AtlasRealtimeScanner', 'ScannerConfig', 'ScannerStatus', 'ScanResult', 'PatternAlert']
