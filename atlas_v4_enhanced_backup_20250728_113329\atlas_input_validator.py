"""
A.T.L.A.S. Input Validation Module
Comprehensive input validation for all user inputs and API data
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime
import json
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass


class AtlasInputValidator:
    """Comprehensive input validator for A.T.L.A.S. system"""
    
    # Valid stock symbol patterns
    SYMBOL_PATTERN = re.compile(r'^[A-Z]{1,5}$')
    
    # Valid email pattern
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    
    # Dangerous patterns to reject
    DANGEROUS_PATTERNS = [
        re.compile(r'<script.*?>', re.IGNORECASE),
        re.compile(r'javascript:', re.IGNORECASE),
        re.compile(r'eval\s*\(', re.IGNORECASE),
        re.compile(r'exec\s*\(', re.IGNORECASE),
        re.compile(r'__import__', re.IGNORECASE),
        re.compile(r'subprocess', re.IGNORECASE),
        re.compile(r'os\.system', re.IGNORECASE),
    ]
    
    # Known legitimate stock symbols (S&P 500 subset for validation)
    LEGITIMATE_SYMBOLS = {
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'BRK.B', 'UNH', 'JNJ',
        'V', 'PG', 'JPM', 'HD', 'CVX', 'MA', 'PFE', 'ABBV', 'BAC', 'KO', 'AVGO', 'PEP',
        'TMO', 'COST', 'DIS', 'ABT', 'DHR', 'VZ', 'ADBE', 'CMCSA', 'NKE', 'TXN', 'XOM',
        'WMT', 'NEE', 'LLY', 'QCOM', 'ACN', 'BMY', 'HON', 'UPS', 'PM', 'T', 'SBUX',
        'LOW', 'IBM', 'MDT', 'AMGN', 'GS', 'SPGI', 'CAT', 'BLK', 'AXP', 'GILD'
    }
    
    @staticmethod
    def validate_symbol(symbol: Any) -> Tuple[bool, str]:
        """Validate stock symbol"""
        try:
            if not symbol:
                return False, "Symbol cannot be empty"
            
            if not isinstance(symbol, str):
                return False, "Symbol must be a string"
            
            symbol = symbol.upper().strip()
            
            # Check basic format
            if not AtlasInputValidator.SYMBOL_PATTERN.match(symbol):
                return False, "Symbol must be 1-5 uppercase letters"
            
            # Check against known legitimate symbols
            if symbol not in AtlasInputValidator.LEGITIMATE_SYMBOLS:
                logger.warning(f"Symbol {symbol} not in known legitimate symbols list")
                # Don't reject, but log for review
            
            return True, symbol
            
        except Exception as e:
            return False, f"Symbol validation error: {str(e)}"
    
    @staticmethod
    def validate_price(price: Any) -> Tuple[bool, Union[float, str]]:
        """Validate price value"""
        try:
            if price is None:
                return False, "Price cannot be None"
            
            # Convert to float
            if isinstance(price, str):
                price = price.strip()
                if not price:
                    return False, "Price cannot be empty string"
                price = float(price)
            elif not isinstance(price, (int, float)):
                return False, "Price must be a number"
            
            # Validate range
            if price <= 0:
                return False, "Price must be positive"
            
            if price > 1000000:  # $1M limit
                return False, "Price exceeds maximum limit ($1,000,000)"
            
            # Check for reasonable precision (max 4 decimal places)
            if round(price, 4) != price:
                price = round(price, 4)
            
            return True, price
            
        except ValueError:
            return False, "Invalid price format"
        except Exception as e:
            return False, f"Price validation error: {str(e)}"
    
    @staticmethod
    def validate_quantity(quantity: Any) -> Tuple[bool, Union[int, str]]:
        """Validate quantity/shares"""
        try:
            if quantity is None:
                return False, "Quantity cannot be None"
            
            # Convert to int
            if isinstance(quantity, str):
                quantity = quantity.strip()
                if not quantity:
                    return False, "Quantity cannot be empty string"
                quantity = int(float(quantity))  # Handle "100.0" format
            elif isinstance(quantity, float):
                quantity = int(quantity)
            elif not isinstance(quantity, int):
                return False, "Quantity must be a number"
            
            # Validate range
            if quantity <= 0:
                return False, "Quantity must be positive"
            
            if quantity > 1000000:  # 1M shares limit
                return False, "Quantity exceeds maximum limit (1,000,000 shares)"
            
            return True, quantity
            
        except ValueError:
            return False, "Invalid quantity format"
        except Exception as e:
            return False, f"Quantity validation error: {str(e)}"
    
    @staticmethod
    def validate_percentage(percentage: Any) -> Tuple[bool, Union[float, str]]:
        """Validate percentage value (0-100)"""
        try:
            if percentage is None:
                return False, "Percentage cannot be None"
            
            if isinstance(percentage, str):
                percentage = percentage.strip().rstrip('%')
                if not percentage:
                    return False, "Percentage cannot be empty"
                percentage = float(percentage)
            elif not isinstance(percentage, (int, float)):
                return False, "Percentage must be a number"
            
            # Validate range
            if percentage < 0:
                return False, "Percentage cannot be negative"
            
            if percentage > 100:
                return False, "Percentage cannot exceed 100%"
            
            return True, percentage
            
        except ValueError:
            return False, "Invalid percentage format"
        except Exception as e:
            return False, f"Percentage validation error: {str(e)}"
    
    @staticmethod
    def validate_user_message(message: Any) -> Tuple[bool, Union[str, str]]:
        """Validate user message for security and content"""
        try:
            if not message:
                return False, "Message cannot be empty"
            
            if not isinstance(message, str):
                return False, "Message must be a string"
            
            message = message.strip()
            
            # Check length
            if len(message) > 10000:  # 10KB limit
                return False, "Message too long (max 10,000 characters)"
            
            # Check for dangerous patterns
            for pattern in AtlasInputValidator.DANGEROUS_PATTERNS:
                if pattern.search(message):
                    logger.warning(f"Dangerous pattern detected in message: {pattern.pattern}")
                    return False, "Message contains potentially dangerous content"
            
            # Basic sanitization
            message = message.replace('\x00', '')  # Remove null bytes
            
            return True, message
            
        except Exception as e:
            return False, f"Message validation error: {str(e)}"
    
    @staticmethod
    def validate_json_data(data: Any) -> Tuple[bool, Union[Dict, str]]:
        """Validate JSON data structure"""
        try:
            if data is None:
                return False, "Data cannot be None"
            
            # If string, try to parse as JSON
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except json.JSONDecodeError as e:
                    return False, f"Invalid JSON format: {str(e)}"
            
            # Must be dict or list
            if not isinstance(data, (dict, list)):
                return False, "Data must be a JSON object or array"
            
            # Check for reasonable size
            json_str = json.dumps(data)
            if len(json_str) > 1000000:  # 1MB limit
                return False, "JSON data too large (max 1MB)"
            
            return True, data
            
        except Exception as e:
            return False, f"JSON validation error: {str(e)}"
    
    @staticmethod
    def validate_market_data(data: Dict[str, Any]) -> Tuple[bool, Union[Dict, str]]:
        """Validate market data structure"""
        try:
            if not isinstance(data, dict):
                return False, "Market data must be a dictionary"
            
            # Required fields
            required_fields = ['symbol', 'price', 'timestamp']
            for field in required_fields:
                if field not in data:
                    return False, f"Missing required field: {field}"
            
            # Validate symbol
            symbol_valid, symbol_result = AtlasInputValidator.validate_symbol(data['symbol'])
            if not symbol_valid:
                return False, f"Invalid symbol: {symbol_result}"
            data['symbol'] = symbol_result
            
            # Validate price
            price_valid, price_result = AtlasInputValidator.validate_price(data['price'])
            if not price_valid:
                return False, f"Invalid price: {price_result}"
            data['price'] = price_result
            
            # Validate timestamp
            timestamp = data['timestamp']
            if isinstance(timestamp, str):
                try:
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    return False, "Invalid timestamp format"
            elif not isinstance(timestamp, datetime):
                return False, "Timestamp must be datetime or ISO string"
            
            # Check if timestamp is too old (more than 24 hours)
            age = (datetime.now() - timestamp.replace(tzinfo=None)).total_seconds()
            if age > 86400:  # 24 hours
                logger.warning(f"Market data is {age/3600:.1f} hours old")
            
            return True, data
            
        except Exception as e:
            return False, f"Market data validation error: {str(e)}"
    
    @staticmethod
    def validate_dataframe(df: Any) -> Tuple[bool, Union[pd.DataFrame, str]]:
        """Validate pandas DataFrame for market data"""
        try:
            if not isinstance(df, pd.DataFrame):
                return False, "Data must be a pandas DataFrame"
            
            if df.empty:
                return False, "DataFrame cannot be empty"
            
            # Check for required columns
            required_columns = ['close']
            for col in required_columns:
                if col not in df.columns:
                    return False, f"Missing required column: {col}"
            
            # Check for NaN values
            if df['close'].isna().any():
                logger.warning("NaN values detected in close prices")
                df = df.dropna(subset=['close'])
                if df.empty:
                    return False, "No valid data after removing NaN values"
            
            # Check for infinite values
            if np.isinf(df['close']).any():
                return False, "Infinite values detected in close prices"
            
            # Check for reasonable price ranges
            min_price = df['close'].min()
            max_price = df['close'].max()
            
            if min_price <= 0:
                return False, "Invalid negative or zero prices detected"
            
            if max_price > 1000000:
                return False, "Unreasonably high prices detected"
            
            return True, df
            
        except Exception as e:
            return False, f"DataFrame validation error: {str(e)}"
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for safe file operations"""
        if not filename:
            return "default"
        
        # Remove dangerous characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # Remove path traversal attempts
        filename = filename.replace('..', '_')
        
        # Limit length
        if len(filename) > 255:
            filename = filename[:255]
        
        return filename or "default"


# Global validator instance
validator = AtlasInputValidator()
