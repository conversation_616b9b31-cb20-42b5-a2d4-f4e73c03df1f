#!/usr/bin/env python3
"""
Test script to verify the beginner-friendly Grok system functionality
Tests the example queries from the user's requirements
"""

import asyncio
import requests
import json
import time

def test_beginner_queries():
    """Test the three main beginner query types from the user's examples"""
    
    base_url = "http://localhost:8002"
    
    # Test queries from the user's examples
    test_cases = [
        {
            "name": "Profit Target Query",
            "message": "Come up with a plan to make me $1,500 in 2 weeks",
            "expected_intent": "beginner_trading_plan",
            "expected_features": [
                "WHY THIS PLAN",
                "WIN/LOSS PROBABILITY", 
                "MONEY IN/OUT",
                "SMART STOP PLANS",
                "MARKET CONTEXT",
                "CONFIDENCE SCORE"
            ]
        },
        {
            "name": "Lee Method Scanning",
            "message": "Scan the market for Lee Method signals with net earnings potential",
            "expected_intent": "beginner_market_scan",
            "expected_features": [
                "volume spike",
                "momentum pattern",
                "earnings",
                "entry/exit zones"
            ]
        },
        {
            "name": "Weekly Investment Query",
            "message": "Can you find good setups to invest in this week?",
            "expected_intent": "beginner_market_scan",
            "expected_features": [
                "high-probability setups",
                "low to medium risk",
                "entry/exit",
                "expected return"
            ]
        },
        {
            "name": "Educational Query",
            "message": "What is RSI and how does it work for beginners?",
            "expected_intent": "beginner_learning",
            "expected_features": [
                "SIMPLE EXPLANATION",
                "HOW IT WORKS",
                "REAL EXAMPLE",
                "WHEN TO USE IT",
                "BEGINNER MISTAKES"
            ]
        },
        {
            "name": "Basic Beginner Query",
            "message": "I'm a beginner trader, help me understand options",
            "expected_intent": "beginner_learning",
            "expected_features": [
                "beginner",
                "explain",
                "educational"
            ]
        }
    ]
    
    print("🚀 Testing Atlas V5 Enhanced Beginner-Friendly Grok System")
    print("=" * 80)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 TEST {i}: {test_case['name']}")
        print(f"Query: \"{test_case['message']}\"")
        print("-" * 60)
        
        try:
            # Send chat request
            chat_data = {
                "message": test_case['message'],
                "conversation_id": f"test_beginner_{i}"
            }
            
            start_time = time.time()
            response = requests.post(f"{base_url}/api/v1/chat", 
                                   json=chat_data, timeout=45)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                print(f"✅ Response received ({response_time:.1f}s)")
                print(f"📄 Response length: {len(response_text)} characters")
                
                # Check for expected features
                features_found = []
                for feature in test_case['expected_features']:
                    if feature.lower() in response_text.lower():
                        features_found.append(feature)
                
                print(f"🎯 Expected features found: {len(features_found)}/{len(test_case['expected_features'])}")
                for feature in features_found:
                    print(f"   ✓ {feature}")
                
                missing_features = [f for f in test_case['expected_features'] if f not in features_found]
                if missing_features:
                    print(f"❌ Missing features:")
                    for feature in missing_features:
                        print(f"   ✗ {feature}")
                
                # Show response preview
                preview = response_text[:300] + "..." if len(response_text) > 300 else response_text
                print(f"\n📋 Response Preview:")
                print(f"{preview}")
                
                # Check if it looks like structured format
                structured_indicators = ["**", "•", "Entry:", "Target:", "Stop:", "Confidence:"]
                structured_count = sum(1 for indicator in structured_indicators if indicator in response_text)
                is_structured = structured_count >= 2
                
                print(f"📊 Structured format: {'✅ Yes' if is_structured else '❌ No'} ({structured_count} indicators)")
                
                results.append({
                    'test': test_case['name'],
                    'success': True,
                    'response_time': response_time,
                    'features_found': len(features_found),
                    'total_features': len(test_case['expected_features']),
                    'structured': is_structured,
                    'response_length': len(response_text)
                })
                
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Error: {response.text}")
                results.append({
                    'test': test_case['name'],
                    'success': False,
                    'error': f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append({
                'test': test_case['name'],
                'success': False,
                'error': str(e)
            })
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 BEGINNER GROK SYSTEM TEST RESULTS")
    print("=" * 80)
    
    successful_tests = [r for r in results if r.get('success', False)]
    total_tests = len(results)
    
    print(f"Overall Success Rate: {len(successful_tests)}/{total_tests} ({len(successful_tests)/total_tests*100:.1f}%)")
    
    if successful_tests:
        avg_response_time = sum(r['response_time'] for r in successful_tests) / len(successful_tests)
        avg_features = sum(r['features_found'] for r in successful_tests) / len(successful_tests)
        structured_count = sum(1 for r in successful_tests if r.get('structured', False))
        
        print(f"Average Response Time: {avg_response_time:.1f} seconds")
        print(f"Average Features Found: {avg_features:.1f}")
        print(f"Structured Responses: {structured_count}/{len(successful_tests)}")
    
    print("\n📋 Individual Test Results:")
    for result in results:
        if result.get('success'):
            print(f"✅ {result['test']}: {result['features_found']}/{result['total_features']} features, "
                  f"{result['response_time']:.1f}s, {'structured' if result.get('structured') else 'unstructured'}")
        else:
            print(f"❌ {result['test']}: {result.get('error', 'Unknown error')}")
    
    # Final assessment
    print("\n" + "=" * 80)
    if len(successful_tests) >= 4:  # At least 80% success rate
        print("🎉 BEGINNER GROK SYSTEM IS WORKING EXCELLENTLY!")
        print("✅ System successfully handles beginner queries with structured responses")
        print("✅ Educational content is being generated appropriately")
        print("✅ Trading plans include the required 6-point format elements")
        print("✅ Market scanning provides beginner-friendly analysis")
        
        print("\n🌐 Ready for beginner users at: http://localhost:8002")
        print("💬 Try these test messages in the web interface:")
        for test_case in test_cases[:3]:  # Show first 3 examples
            print(f"   • \"{test_case['message']}\"")
            
    else:
        print("⚠️  BEGINNER GROK SYSTEM NEEDS IMPROVEMENT")
        print("Some tests failed - review the errors above")
    
    return len(successful_tests) >= 4

if __name__ == "__main__":
    success = test_beginner_queries()
    exit(0 if success else 1)
