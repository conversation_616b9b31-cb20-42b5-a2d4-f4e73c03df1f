"""
A.T.L.A.S. Real-Time Processing Core
Core real-time processing functionality for the Atlas trading system
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
import threading
from concurrent.futures import ThreadPoolExecutor
import queue

# Core imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'core'))

try:
    from models import EngineStatus, Quote, TTMSqueezeSignal
    from config import get_api_config
except ImportError:
    # Fallback definitions
    from enum import Enum
    class EngineStatus(Enum):
        INITIALIZING = "initializing"
        ACTIVE = "active"
        STOPPED = "stopped"
        FAILED = "failed"

    def get_api_config():
        return {}

logger = logging.getLogger(__name__)

@dataclass
class RealtimeEvent:
    """Real-time event data structure"""
    event_type: str
    timestamp: datetime
    data: Dict[str, Any]
    priority: int = 1
    source: str = "atlas"

class RealtimeEventBus:
    """Event bus for real-time communication between components"""
    
    def __init__(self):
        self.subscribers: Dict[str, List[Callable]] = {}
        self.event_queue = queue.Queue()
        self.is_running = False
        self.worker_thread = None
        self.logger = logging.getLogger(__name__)
    
    def subscribe(self, event_type: str, callback: Callable):
        """Subscribe to real-time events"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)
        self.logger.info(f"[REALTIME] Subscribed to {event_type}")
    
    def publish(self, event: RealtimeEvent):
        """Publish real-time event"""
        self.event_queue.put(event)
    
    def start(self):
        """Start the event bus"""
        if self.is_running:
            return
        
        self.is_running = True
        self.worker_thread = threading.Thread(target=self._process_events)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        self.logger.info("[REALTIME] Event bus started")
    
    def stop(self):
        """Stop the event bus"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        self.logger.info("[REALTIME] Event bus stopped")
    
    def _process_events(self):
        """Process events in background thread"""
        while self.is_running:
            try:
                event = self.event_queue.get(timeout=1)
                self._dispatch_event(event)
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"[REALTIME] Event processing error: {e}")
    
    def _dispatch_event(self, event: RealtimeEvent):
        """Dispatch event to subscribers"""
        if event.event_type in self.subscribers:
            for callback in self.subscribers[event.event_type]:
                try:
                    callback(event)
                except Exception as e:
                    self.logger.error(f"[REALTIME] Callback error: {e}")

class RealtimeDataManager:
    """Manages real-time data streams and caching"""
    
    def __init__(self):
        self.data_cache: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        self.cache_ttl = 30  # seconds
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    def set_data(self, key: str, data: Any):
        """Set real-time data with timestamp"""
        with self.lock:
            self.data_cache[key] = data
            self.cache_timestamps[key] = datetime.now()
    
    def get_data(self, key: str) -> Optional[Any]:
        """Get real-time data if not expired"""
        with self.lock:
            if key not in self.data_cache:
                return None
            
            timestamp = self.cache_timestamps.get(key)
            if timestamp and (datetime.now() - timestamp).seconds > self.cache_ttl:
                # Data expired
                del self.data_cache[key]
                del self.cache_timestamps[key]
                return None
            
            return self.data_cache[key]
    
    def clear_expired(self):
        """Clear expired data from cache"""
        with self.lock:
            now = datetime.now()
            expired_keys = []
            
            for key, timestamp in self.cache_timestamps.items():
                if (now - timestamp).seconds > self.cache_ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.data_cache[key]
                del self.cache_timestamps[key]

class AtlasRealtimeCore:
    """Core real-time processing engine for Atlas"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.event_bus = RealtimeEventBus()
        self.data_manager = RealtimeDataManager()
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.logger = logging.getLogger(__name__)
        
        # Real-time metrics
        self.metrics = {
            "events_processed": 0,
            "data_updates": 0,
            "errors": 0,
            "uptime_start": datetime.now()
        }
    
    async def initialize(self):
        """Initialize the real-time core"""
        try:
            self.logger.info("[REALTIME] Initializing real-time core...")
            
            # Start event bus
            self.event_bus.start()
            
            # Subscribe to system events
            self.event_bus.subscribe("market_data", self._handle_market_data)
            self.event_bus.subscribe("scanner_signal", self._handle_scanner_signal)
            self.event_bus.subscribe("system_alert", self._handle_system_alert)
            
            self.status = EngineStatus.ACTIVE
            self.logger.info("[REALTIME] Real-time core initialized successfully")
            
        except Exception as e:
            self.logger.error(f"[REALTIME] Initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise
    
    async def shutdown(self):
        """Shutdown the real-time core"""
        self.logger.info("[REALTIME] Shutting down real-time core...")
        
        self.event_bus.stop()
        self.executor.shutdown(wait=True)
        self.status = EngineStatus.STOPPED
        
        self.logger.info("[REALTIME] Real-time core shutdown complete")
    
    def _handle_market_data(self, event: RealtimeEvent):
        """Handle market data events"""
        try:
            symbol = event.data.get("symbol")
            quote_data = event.data.get("quote")
            
            if symbol and quote_data:
                self.data_manager.set_data(f"quote_{symbol}", quote_data)
                self.metrics["data_updates"] += 1
                
        except Exception as e:
            self.logger.error(f"[REALTIME] Market data handling error: {e}")
            self.metrics["errors"] += 1
    
    def _handle_scanner_signal(self, event: RealtimeEvent):
        """Handle scanner signal events"""
        try:
            signal_data = event.data
            symbol = signal_data.get("symbol")
            
            if symbol:
                self.data_manager.set_data(f"signal_{symbol}", signal_data)
                self.logger.info(f"[REALTIME] Scanner signal for {symbol}")
                
        except Exception as e:
            self.logger.error(f"[REALTIME] Scanner signal handling error: {e}")
            self.metrics["errors"] += 1
    
    def _handle_system_alert(self, event: RealtimeEvent):
        """Handle system alert events"""
        try:
            alert_data = event.data
            self.logger.info(f"[REALTIME] System alert: {alert_data.get('message', 'Unknown')}")
            
        except Exception as e:
            self.logger.error(f"[REALTIME] System alert handling error: {e}")
            self.metrics["errors"] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get real-time processing metrics"""
        uptime = datetime.now() - self.metrics["uptime_start"]
        
        return {
            **self.metrics,
            "uptime_seconds": uptime.total_seconds(),
            "status": self.status.value,
            "cache_size": len(self.data_manager.data_cache)
        }

# Global real-time core instance
realtime_core = AtlasRealtimeCore()

# Export main components
__all__ = [
    'RealtimeEvent',
    'RealtimeEventBus', 
    'RealtimeDataManager',
    'AtlasRealtimeCore',
    'realtime_core'
]
