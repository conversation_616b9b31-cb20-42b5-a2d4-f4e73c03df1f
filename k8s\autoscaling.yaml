apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: atlas-hpa
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: atlas-multi-agent-system
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: atlas_orchestration_requests_per_second
      target:
        type: AverageValue
        averageValue: "10"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 4
        periodSeconds: 60
      selectPolicy: Max
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: atlas-pdb
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: availability
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: atlas-multi-agent-system
      component: api
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: atlas-service-account
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: rbac
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: atlas-trading
  name: atlas-role
  labels:
    app: atlas-multi-agent-system
    component: rbac
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: atlas-role-binding
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: rbac
subjects:
- kind: ServiceAccount
  name: atlas-service-account
  namespace: atlas-trading
roleRef:
  kind: Role
  name: atlas-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: atlas-monitoring-network-policy
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: monitoring-security
spec:
  podSelector:
    matchLabels:
      app: prometheus
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: grafana
    ports:
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: atlas-multi-agent-system
    ports:
    - protocol: TCP
      port: 8000
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to: []
    ports:
    - protocol: TCP
      port: 443
---
apiVersion: v1
kind: LimitRange
metadata:
  name: atlas-container-limits
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: resource-management
spec:
  limits:
  - default:
      cpu: "2"
      memory: "4Gi"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    max:
      storage: "100Gi"
    min:
      storage: "1Gi"
    type: PersistentVolumeClaim
