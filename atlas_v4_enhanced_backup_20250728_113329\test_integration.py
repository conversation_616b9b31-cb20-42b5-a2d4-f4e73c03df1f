#!/usr/bin/env python3
"""
A.T.L.A.S. Scanner Integration Test
Final validation of complete scanner system integration
"""

import asyncio
import sys
import logging

# Add current directory to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

async def test_complete_integration():
    """Test complete scanner system integration"""
    logger.info("🔍 Testing complete scanner integration...")
    
    try:
        # Test all scanner imports
        from atlas_realtime_scanner import AtlasRealtimeScanner
        from atlas_lee_method import LeeMethodScanner, AtlasLeeMethodRealtimeScanner
        from atlas_enhanced_realtime_scanner import EnhancedRealtimeScanner
        
        logger.info("✅ All scanner imports successful")
        
        # Test main scanner initialization and functionality
        scanner = AtlasRealtimeScanner()
        await scanner.initialize()
        
        # Test market hours detection
        market_hours = scanner._is_market_hours()
        logger.info(f"✅ Market hours detection: {'OPEN' if market_hours else 'CLOSED'}")
        
        # Test scanner status
        status = await scanner.get_scanner_status()
        logger.info(f"✅ Scanner status: {status['status']}")
        logger.info(f"✅ Market status: {status['market_status']}")
        
        # Test Lee Method scanner
        lee_scanner = LeeMethodScanner()
        market_status = lee_scanner.get_market_status()
        logger.info(f"✅ Lee Method market status: {market_status['market_status']}")
        
        # Test configuration
        config_valid = (
            lee_scanner.confidence_threshold == 0.65 and
            lee_scanner.pattern_sensitivity == 0.5 and
            not lee_scanner.allow_weak_signals and
            not lee_scanner.use_flexible_patterns
        )
        logger.info(f"✅ Configuration validation: {'PASSED' if config_valid else 'FAILED'}")
        
        # Test signal scanning (if markets are open)
        if market_hours:
            logger.info("🔍 Testing signal scanning (markets are open)...")
            test_symbols = ['AAPL', 'MSFT']
            signals = await lee_scanner.scan_multiple_symbols(test_symbols)
            logger.info(f"✅ Signal scanning test completed: {len(signals)} signals found")
        else:
            logger.info("ℹ️  Markets closed - skipping signal scanning test")
        
        logger.info("🎉 Complete scanner integration test PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_complete_integration())
    if success:
        print("\n🎯 SCANNER MODULE INTEGRATION: SUCCESS")
    else:
        print("\n🚨 SCANNER MODULE INTEGRATION: FAILED")
