"""
A.T.L.A.S. Machine Learning Analytics Engine
Advanced ML-powered analytics for trading predictions and market analysis
"""

import asyncio
import logging
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Core imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'core'))

try:
    from models import EngineStatus, Quote, MLPredictionResult
    from config import get_api_config
except ImportError:
    # Fallback definitions
    from enum import Enum
    class EngineStatus(Enum):
        INITIALIZING = "initializing"
        ACTIVE = "active"
        STOPPED = "stopped"
        FAILED = "failed"

    class Quote:
        pass

    class MLPredictionResult:
        pass

    def get_api_config():
        return {}

# ML imports with graceful fallbacks
try:
    from sklearn.ensemble import RandomForestRegressor, <PERSON>radientBoostingRegressor
    from sklearn.linear_model import LinearRegression
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

logger = logging.getLogger(__name__)

class MLModelType(Enum):
    """Machine learning model types"""
    LINEAR_REGRESSION = "linear_regression"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    LSTM_NEURAL_NETWORK = "lstm_neural_network"
    ENSEMBLE = "ensemble"

@dataclass
class MLPrediction:
    """ML prediction result"""
    symbol: str
    prediction_type: str
    predicted_value: float
    confidence: float
    model_used: str
    features_used: List[str]
    timestamp: datetime
    horizon_days: int = 1

@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_name: str
    mse: float
    mae: float
    r2_score: float
    accuracy_percentage: float
    last_updated: datetime

class FeatureEngineer:
    """Feature engineering for ML models"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def create_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create technical analysis features"""
        try:
            # Price-based features
            df['price_change'] = df['close'].pct_change()
            df['price_volatility'] = df['price_change'].rolling(window=20).std()
            
            # Moving averages
            df['sma_5'] = df['close'].rolling(window=5).mean()
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()
            
            # Moving average ratios
            df['sma_5_20_ratio'] = df['sma_5'] / df['sma_20']
            df['sma_20_50_ratio'] = df['sma_20'] / df['sma_50']
            
            # Volume features
            if 'volume' in df.columns:
                df['volume_sma'] = df['volume'].rolling(window=20).mean()
                df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # RSI approximation
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            bb_period = 20
            bb_std = 2
            df['bb_middle'] = df['close'].rolling(window=bb_period).mean()
            bb_std_dev = df['close'].rolling(window=bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std_dev * bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std_dev * bb_std)
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # MACD approximation
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            return df
            
        except Exception as e:
            self.logger.error(f"[ML] Feature engineering failed: {e}")
            return df
    
    def create_lag_features(self, df: pd.DataFrame, target_col: str = 'close', lags: List[int] = [1, 2, 3, 5]) -> pd.DataFrame:
        """Create lagged features"""
        try:
            for lag in lags:
                df[f'{target_col}_lag_{lag}'] = df[target_col].shift(lag)
            return df
        except Exception as e:
            self.logger.error(f"[ML] Lag feature creation failed: {e}")
            return df

class MLPredictor:
    """Machine learning predictor for stock prices"""
    
    def __init__(self, model_type: MLModelType = MLModelType.ENSEMBLE):
        self.model_type = model_type
        self.models = {}
        self.scalers = {}
        self.feature_engineer = FeatureEngineer()
        self.performance_metrics = {}
        self.logger = logging.getLogger(__name__)
        
        # Feature columns to use
        self.feature_columns = [
            'price_change', 'price_volatility', 'sma_5_20_ratio', 'sma_20_50_ratio',
            'rsi', 'bb_position', 'macd', 'macd_histogram',
            'close_lag_1', 'close_lag_2', 'close_lag_3', 'close_lag_5'
        ]
    
    def prepare_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for ML training"""
        try:
            # Create features
            df = self.feature_engineer.create_technical_features(df)
            df = self.feature_engineer.create_lag_features(df)
            
            # Create target (next day's price change)
            df['target'] = df['close'].shift(-1) / df['close'] - 1
            
            # Select features that exist in the dataframe
            available_features = [col for col in self.feature_columns if col in df.columns]
            
            if not available_features:
                raise ValueError("No features available for training")
            
            # Prepare X and y
            X = df[available_features].dropna()
            y = df.loc[X.index, 'target']
            
            # Remove any remaining NaN values
            mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
            X = X[mask]
            y = y[mask]
            
            return X.values, y.values, available_features
            
        except Exception as e:
            self.logger.error(f"[ML] Data preparation failed: {e}")
            return np.array([]), np.array([]), []
    
    def train_model(self, symbol: str, df: pd.DataFrame) -> bool:
        """Train ML model for a symbol"""
        try:
            if not SKLEARN_AVAILABLE:
                self.logger.warning("[ML] Scikit-learn not available - using fallback predictions")
                return False
            
            X, y, features = self.prepare_data(df)
            
            if len(X) < 50:  # Need minimum data for training
                self.logger.warning(f"[ML] Insufficient data for {symbol}: {len(X)} samples")
                return False
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train models based on type
            if self.model_type == MLModelType.RANDOM_FOREST:
                model = RandomForestRegressor(n_estimators=100, random_state=42)
            elif self.model_type == MLModelType.GRADIENT_BOOSTING:
                model = GradientBoostingRegressor(n_estimators=100, random_state=42)
            elif self.model_type == MLModelType.ENSEMBLE:
                # Create ensemble of models
                rf_model = RandomForestRegressor(n_estimators=50, random_state=42)
                gb_model = GradientBoostingRegressor(n_estimators=50, random_state=42)
                lr_model = LinearRegression()
                
                rf_model.fit(X_train_scaled, y_train)
                gb_model.fit(X_train_scaled, y_train)
                lr_model.fit(X_train_scaled, y_train)
                
                # Store ensemble
                model = {
                    'random_forest': rf_model,
                    'gradient_boosting': gb_model,
                    'linear_regression': lr_model
                }
            else:
                model = LinearRegression()
            
            # Train single model or ensemble
            if isinstance(model, dict):
                # Ensemble predictions for evaluation
                rf_pred = model['random_forest'].predict(X_test_scaled)
                gb_pred = model['gradient_boosting'].predict(X_test_scaled)
                lr_pred = model['linear_regression'].predict(X_test_scaled)
                y_pred = (rf_pred + gb_pred + lr_pred) / 3
            else:
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
            
            # Calculate performance metrics
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            # Calculate directional accuracy
            direction_actual = np.sign(y_test)
            direction_pred = np.sign(y_pred)
            accuracy = np.mean(direction_actual == direction_pred) * 100
            
            # Store model and metrics
            self.models[symbol] = model
            self.scalers[symbol] = scaler
            self.performance_metrics[symbol] = ModelPerformance(
                model_name=self.model_type.value,
                mse=mse,
                mae=mae,
                r2_score=r2,
                accuracy_percentage=accuracy,
                last_updated=datetime.now()
            )
            
            self.logger.info(f"[ML] Model trained for {symbol} - Accuracy: {accuracy:.1f}%, R²: {r2:.3f}")
            return True
            
        except Exception as e:
            self.logger.error(f"[ML] Model training failed for {symbol}: {e}")
            return False
    
    def predict(self, symbol: str, df: pd.DataFrame, horizon_days: int = 1) -> Optional[MLPrediction]:
        """Make prediction for a symbol"""
        try:
            if symbol not in self.models:
                self.logger.warning(f"[ML] No trained model for {symbol}")
                return self._fallback_prediction(symbol, df, horizon_days)
            
            # Prepare current data
            df_features = self.feature_engineer.create_technical_features(df)
            df_features = self.feature_engineer.create_lag_features(df_features)
            
            # Get latest features
            available_features = [col for col in self.feature_columns if col in df_features.columns]
            latest_features = df_features[available_features].iloc[-1:].values
            
            if np.isnan(latest_features).any():
                self.logger.warning(f"[ML] NaN values in features for {symbol}")
                return self._fallback_prediction(symbol, df, horizon_days)
            
            # Scale features
            scaler = self.scalers[symbol]
            latest_features_scaled = scaler.transform(latest_features)
            
            # Make prediction
            model = self.models[symbol]
            if isinstance(model, dict):
                # Ensemble prediction
                rf_pred = model['random_forest'].predict(latest_features_scaled)[0]
                gb_pred = model['gradient_boosting'].predict(latest_features_scaled)[0]
                lr_pred = model['linear_regression'].predict(latest_features_scaled)[0]
                prediction = (rf_pred + gb_pred + lr_pred) / 3
                model_name = "ensemble"
            else:
                prediction = model.predict(latest_features_scaled)[0]
                model_name = self.model_type.value
            
            # Calculate confidence based on model performance
            performance = self.performance_metrics.get(symbol)
            confidence = min(performance.accuracy_percentage / 100, 0.95) if performance else 0.5
            
            return MLPrediction(
                symbol=symbol,
                prediction_type="price_change",
                predicted_value=prediction,
                confidence=confidence,
                model_used=model_name,
                features_used=available_features,
                timestamp=datetime.now(),
                horizon_days=horizon_days
            )
            
        except Exception as e:
            self.logger.error(f"[ML] Prediction failed for {symbol}: {e}")
            return self._fallback_prediction(symbol, df, horizon_days)
    
    def _fallback_prediction(self, symbol: str, df: pd.DataFrame, horizon_days: int) -> MLPrediction:
        """Fallback prediction using simple technical analysis"""
        try:
            # Simple momentum-based prediction
            recent_returns = df['close'].pct_change().tail(5)
            avg_return = recent_returns.mean()
            volatility = recent_returns.std()
            
            # Simple prediction based on recent momentum
            prediction = avg_return * 0.5  # Conservative momentum continuation
            confidence = max(0.3, 1 - volatility * 2)  # Lower confidence with higher volatility
            
            return MLPrediction(
                symbol=symbol,
                prediction_type="price_change",
                predicted_value=prediction,
                confidence=min(confidence, 0.7),  # Cap fallback confidence
                model_used="fallback_momentum",
                features_used=["recent_returns", "volatility"],
                timestamp=datetime.now(),
                horizon_days=horizon_days
            )
            
        except Exception as e:
            self.logger.error(f"[ML] Fallback prediction failed for {symbol}: {e}")
            return MLPrediction(
                symbol=symbol,
                prediction_type="price_change",
                predicted_value=0.0,
                confidence=0.1,
                model_used="error_fallback",
                features_used=[],
                timestamp=datetime.now(),
                horizon_days=horizon_days
            )

class AtlasMLAnalytics:
    """Main ML Analytics engine for Atlas"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.predictor = MLPredictor(MLModelType.ENSEMBLE)
        self.trained_symbols = set()
        self.prediction_cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize the ML analytics engine"""
        try:
            self.logger.info("[ML] Initializing ML Analytics engine...")
            
            if not SKLEARN_AVAILABLE:
                self.logger.warning("[ML] Scikit-learn not available - using fallback mode")
            
            self.status = EngineStatus.ACTIVE
            self.logger.info("[ML] ML Analytics engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"[ML] Initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise
    
    async def get_prediction(self, symbol: str, market_data: pd.DataFrame, horizon_days: int = 1) -> Optional[MLPrediction]:
        """Get ML prediction for a symbol"""
        try:
            # Check cache first
            cache_key = f"{symbol}_{horizon_days}"
            if cache_key in self.prediction_cache:
                cached_pred, timestamp = self.prediction_cache[cache_key]
                if (datetime.now() - timestamp).seconds < self.cache_ttl:
                    return cached_pred
            
            # Train model if not already trained
            if symbol not in self.trained_symbols:
                success = self.predictor.train_model(symbol, market_data)
                if success:
                    self.trained_symbols.add(symbol)
            
            # Make prediction
            prediction = self.predictor.predict(symbol, market_data, horizon_days)
            
            # Cache prediction
            if prediction:
                self.prediction_cache[cache_key] = (prediction, datetime.now())
            
            return prediction
            
        except Exception as e:
            self.logger.error(f"[ML] Prediction request failed for {symbol}: {e}")
            return None
    
    def get_model_performance(self, symbol: str) -> Optional[ModelPerformance]:
        """Get model performance metrics for a symbol"""
        return self.predictor.performance_metrics.get(symbol)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get ML system status"""
        return {
            "status": self.status.value,
            "sklearn_available": SKLEARN_AVAILABLE,
            "tensorflow_available": TENSORFLOW_AVAILABLE,
            "trained_symbols": len(self.trained_symbols),
            "cached_predictions": len(self.prediction_cache),
            "model_type": self.predictor.model_type.value
        }

# Global ML analytics instance
ml_analytics = AtlasMLAnalytics()

# Export main components
__all__ = [
    'MLModelType',
    'MLPrediction',
    'ModelPerformance',
    'FeatureEngineer',
    'MLPredictor',
    'AtlasMLAnalytics',
    'ml_analytics'
]
