"""
A.T.L.A.S. Comprehensive Data Validation Framework
Integrates all validation systems for complete data accuracy and user safety assurance
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import pandas as pd
import numpy as np

# Import our validation systems
from atlas_data_quality_monitor import data_quality_monitor, DataQualityLevel
from atlas_trading_accuracy_monitor import trading_accuracy_monitor, TradingSignal
from atlas_user_safety_system import user_safety_system, SafetyCheckType, AlertSeverity

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Comprehensive validation result"""
    is_valid: bool
    quality_level: DataQualityLevel
    safety_status: str
    warnings: List[str]
    recommendations: List[str]
    details: Dict[str, Any]
    timestamp: datetime

class AtlasComprehensiveValidationFramework:
    """Master validation framework that coordinates all validation systems"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize validation components
        self.data_quality_monitor = data_quality_monitor
        self.trading_accuracy_monitor = trading_accuracy_monitor
        self.user_safety_system = user_safety_system
        
        # Validation statistics
        self.validation_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'warnings_generated': 0,
            'critical_issues_detected': 0,
            'last_validation': None
        }
        
        # Integration settings
        self.validation_settings = {
            'require_all_sources_valid': False,  # Allow trading with partial data
            'minimum_data_quality_level': DataQualityLevel.ACCEPTABLE,
            'enable_cross_validation': True,
            'enable_user_safety_checks': True,
            'enable_trading_accuracy_monitoring': True,
            'auto_halt_on_critical_issues': True
        }
        
        self.logger.info("[VALIDATION_FRAMEWORK] Comprehensive Validation Framework initialized")
    
    async def validate_market_data_comprehensive(self, symbol: str, data: Dict[str, Any], 
                                               source: str) -> ValidationResult:
        """Comprehensive market data validation using all systems"""
        try:
            warnings = []
            recommendations = []
            details = {}
            
            # 1. Data Quality Validation
            data_valid, quality_level, quality_issues = await self.data_quality_monitor.validate_market_data(
                symbol, data, source
            )
            
            details['data_quality'] = {
                'valid': data_valid,
                'quality_level': quality_level.value,
                'issues': quality_issues
            }
            
            if quality_issues:
                warnings.extend([f"Data Quality: {issue}" for issue in quality_issues])
            
            # 2. User Safety Checks
            safety_valid, safety_warnings = await self.user_safety_system.check_data_safety(
                symbol, data, source
            )
            
            details['user_safety'] = {
                'valid': safety_valid,
                'warnings_count': len(safety_warnings),
                'warnings': [w.message for w in safety_warnings]
            }
            
            if safety_warnings:
                warnings.extend([f"Safety: {w.message}" for w in safety_warnings])
                recommendations.extend([w.recommendation for w in safety_warnings])
            
            # 3. Mathematical Validation (if applicable)
            if 'calculations' in data:
                calc_valid, calc_warnings = await self._validate_calculations(data['calculations'])
                details['calculations'] = {
                    'valid': calc_valid,
                    'warnings': calc_warnings
                }
                if calc_warnings:
                    warnings.extend([f"Calculation: {w}" for w in calc_warnings])
            
            # 4. Determine overall validation result
            overall_valid = (
                data_valid and 
                safety_valid and 
                quality_level.value not in ['CRITICAL'] and
                (not self.validation_settings['require_all_sources_valid'] or data_valid)
            )
            
            # 5. Generate safety status
            if not overall_valid or quality_level == DataQualityLevel.CRITICAL:
                safety_status = "CRITICAL"
            elif quality_level == DataQualityLevel.POOR or len(warnings) > 3:
                safety_status = "WARNING"
            elif quality_level in [DataQualityLevel.ACCEPTABLE, DataQualityLevel.GOOD]:
                safety_status = "ACCEPTABLE"
            else:
                safety_status = "EXCELLENT"
            
            # 6. Add general recommendations
            if not overall_valid:
                recommendations.extend([
                    "Do not make trading decisions based on this data",
                    "Verify data from alternative sources",
                    "Wait for data quality to improve"
                ])
            elif safety_status == "WARNING":
                recommendations.extend([
                    "Exercise caution when using this data",
                    "Consider waiting for fresher data",
                    "Cross-reference with other sources"
                ])
            
            # 7. Update statistics
            self._update_validation_stats(overall_valid, len(warnings), safety_status == "CRITICAL")
            
            # 8. Generate user alerts if needed
            if safety_status == "CRITICAL":
                await self.user_safety_system.generate_user_alert(
                    SafetyCheckType.DATA_QUALITY,
                    AlertSeverity.CRITICAL,
                    f"Critical Data Quality Issue - {symbol}",
                    f"Data validation failed for {symbol} from {source}. Do not use for trading.",
                    details,
                    user_action_required=True
                )
            
            result = ValidationResult(
                is_valid=overall_valid,
                quality_level=quality_level,
                safety_status=safety_status,
                warnings=warnings,
                recommendations=recommendations,
                details=details,
                timestamp=datetime.now()
            )
            
            self.logger.info(f"[VALIDATION] {symbol} from {source}: {safety_status} "
                           f"(Quality: {quality_level.value}, Valid: {overall_valid})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Comprehensive validation error: {e}")
            return ValidationResult(
                is_valid=False,
                quality_level=DataQualityLevel.CRITICAL,
                safety_status="CRITICAL",
                warnings=[f"Validation system error: {str(e)}"],
                recommendations=["Do not use this data - system error occurred"],
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def validate_trading_signal_comprehensive(self, signal: TradingSignal) -> ValidationResult:
        """Comprehensive trading signal validation"""
        try:
            warnings = []
            recommendations = []
            details = {}
            
            # 1. Record signal for accuracy tracking
            signal_id = await self.trading_accuracy_monitor.record_trading_signal(signal)
            details['signal_tracking'] = {'signal_id': signal_id}
            
            # 2. Validate signal parameters
            param_valid, param_warnings = self._validate_signal_parameters(signal)
            if param_warnings:
                warnings.extend(param_warnings)
            
            # 3. Check current performance metrics
            performance_metrics = await self.trading_accuracy_monitor.calculate_performance_metrics()
            details['performance_context'] = {
                'current_win_rate': performance_metrics.win_rate,
                'average_return': performance_metrics.average_return,
                'total_signals': performance_metrics.total_signals
            }
            
            # 4. Risk assessment
            risk_level, risk_warnings = self._assess_signal_risk(signal, performance_metrics)
            details['risk_assessment'] = {'risk_level': risk_level}
            if risk_warnings:
                warnings.extend(risk_warnings)
            
            # 5. Generate recommendations
            recommendations.extend(self._generate_signal_recommendations(signal, performance_metrics, risk_level))
            
            # 6. Determine overall validity
            overall_valid = param_valid and risk_level != "CRITICAL"
            
            # 7. Safety status
            if risk_level == "CRITICAL":
                safety_status = "CRITICAL"
            elif risk_level == "HIGH" or len(warnings) > 2:
                safety_status = "WARNING"
            else:
                safety_status = "ACCEPTABLE"
            
            # 8. Generate alerts for high-risk signals
            if risk_level == "CRITICAL":
                await self.user_safety_system.generate_user_alert(
                    SafetyCheckType.TRADING_RISK,
                    AlertSeverity.CRITICAL,
                    f"High-Risk Trading Signal - {signal.symbol}",
                    f"Trading signal for {signal.symbol} has critical risk factors",
                    details,
                    user_action_required=True
                )
            
            result = ValidationResult(
                is_valid=overall_valid,
                quality_level=DataQualityLevel.GOOD if overall_valid else DataQualityLevel.POOR,
                safety_status=safety_status,
                warnings=warnings,
                recommendations=recommendations,
                details=details,
                timestamp=datetime.now()
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Signal validation error: {e}")
            return ValidationResult(
                is_valid=False,
                quality_level=DataQualityLevel.CRITICAL,
                safety_status="CRITICAL",
                warnings=[f"Signal validation error: {str(e)}"],
                recommendations=["Do not execute this signal - validation failed"],
                details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    async def _validate_calculations(self, calculations: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate mathematical calculations"""
        try:
            warnings = []
            all_valid = True
            
            for calc_type, calc_data in calculations.items():
                if 'inputs' in calc_data and 'result' in calc_data:
                    is_valid, message = await self.data_quality_monitor.validate_calculation(
                        calc_type, calc_data['inputs'], calc_data['result']
                    )
                    if not is_valid:
                        warnings.append(f"{calc_type}: {message}")
                        all_valid = False
            
            return all_valid, warnings
            
        except Exception as e:
            return False, [f"Calculation validation error: {str(e)}"]
    
    def _validate_signal_parameters(self, signal: TradingSignal) -> Tuple[bool, List[str]]:
        """Validate trading signal parameters"""
        warnings = []
        
        # Check confidence level
        if signal.confidence < 0.5:
            warnings.append(f"Low confidence signal: {signal.confidence:.1%}")
        
        # Check price validity
        if signal.entry_price <= 0:
            warnings.append(f"Invalid entry price: ${signal.entry_price}")
        
        # Check target/stop loss relationship
        if signal.target_price and signal.stop_loss:
            if signal.signal_type == "BUY":
                if signal.target_price <= signal.entry_price:
                    warnings.append("Target price should be above entry price for BUY signal")
                if signal.stop_loss >= signal.entry_price:
                    warnings.append("Stop loss should be below entry price for BUY signal")
            elif signal.signal_type == "SELL":
                if signal.target_price >= signal.entry_price:
                    warnings.append("Target price should be below entry price for SELL signal")
                if signal.stop_loss <= signal.entry_price:
                    warnings.append("Stop loss should be above entry price for SELL signal")
        
        return len(warnings) == 0, warnings
    
    def _assess_signal_risk(self, signal: TradingSignal, performance_metrics) -> Tuple[str, List[str]]:
        """Assess trading signal risk level"""
        warnings = []
        risk_factors = 0
        
        # Low confidence
        if signal.confidence < 0.6:
            risk_factors += 1
            warnings.append("Low confidence increases risk")
        
        # Poor recent performance
        if performance_metrics.total_signals > 5 and performance_metrics.win_rate < 0.5:
            risk_factors += 1
            warnings.append("Recent poor performance increases risk")
        
        # High volatility symbol (basic check)
        if signal.entry_price > 500:  # High-priced stocks often more volatile
            risk_factors += 1
            warnings.append("High-priced stock may have higher volatility")
        
        # Determine risk level
        if risk_factors >= 3:
            return "CRITICAL", warnings
        elif risk_factors >= 2:
            return "HIGH", warnings
        elif risk_factors >= 1:
            return "MEDIUM", warnings
        else:
            return "LOW", warnings
    
    def _generate_signal_recommendations(self, signal: TradingSignal, performance_metrics, risk_level: str) -> List[str]:
        """Generate signal-specific recommendations"""
        recommendations = []
        
        if risk_level == "CRITICAL":
            recommendations.extend([
                "Consider avoiding this signal due to high risk factors",
                "If proceeding, use very small position size",
                "Set tight stop losses to limit potential losses"
            ])
        elif risk_level == "HIGH":
            recommendations.extend([
                "Use reduced position size due to elevated risk",
                "Monitor the position closely",
                "Consider waiting for better setup"
            ])
        elif risk_level == "MEDIUM":
            recommendations.extend([
                "Standard position sizing appropriate",
                "Follow your risk management rules",
                "Monitor for any changes in conditions"
            ])
        else:  # LOW risk
            recommendations.extend([
                "Signal appears to have good risk/reward profile",
                "Follow standard position sizing rules",
                "Consider this a favorable setup"
            ])
        
        # Performance-based recommendations
        if performance_metrics.total_signals > 10:
            if performance_metrics.average_return < 0.2:
                recommendations.append("Recent performance below target - consider reducing position sizes")
            elif performance_metrics.average_return > 0.5:
                recommendations.append("Strong recent performance - maintain current strategy")
        
        return recommendations
    
    def _update_validation_stats(self, is_valid: bool, warning_count: int, is_critical: bool):
        """Update validation statistics"""
        self.validation_stats['total_validations'] += 1
        if is_valid:
            self.validation_stats['passed_validations'] += 1
        else:
            self.validation_stats['failed_validations'] += 1
        
        self.validation_stats['warnings_generated'] += warning_count
        if is_critical:
            self.validation_stats['critical_issues_detected'] += 1
        
        self.validation_stats['last_validation'] = datetime.now()
    
    def get_validation_framework_status(self) -> Dict[str, Any]:
        """Get comprehensive validation framework status"""
        try:
            # Get status from all subsystems
            data_quality_report = self.data_quality_monitor.get_data_quality_report()
            accuracy_report = self.trading_accuracy_monitor.get_accuracy_report()
            safety_status = self.user_safety_system.get_user_safety_status()
            
            # Calculate overall system health
            overall_health = "EXCELLENT"
            if safety_status.get('safety_status') == 'CRITICAL':
                overall_health = "CRITICAL"
            elif data_quality_report.get('overall_status') in ['CRITICAL', 'POOR']:
                overall_health = "DEGRADED"
            elif safety_status.get('safety_status') == 'WARNING':
                overall_health = "WARNING"
            
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_health': overall_health,
                'validation_statistics': self.validation_stats,
                'data_quality_status': data_quality_report,
                'trading_accuracy_status': accuracy_report,
                'user_safety_status': safety_status,
                'framework_settings': self.validation_settings,
                'monitoring_active': True
            }
            
        except Exception as e:
            self.logger.error(f"Error getting framework status: {e}")
            return {'error': str(e)}
    
    async def start_continuous_monitoring(self):
        """Start continuous monitoring of all validation systems"""
        self.logger.info("[VALIDATION_FRAMEWORK] Starting continuous monitoring...")
        
        # Start individual monitoring systems
        monitoring_tasks = [
            self.data_quality_monitor.start_monitoring(),
            self._continuous_cleanup_task()
        ]
        
        await asyncio.gather(*monitoring_tasks)
    
    async def _continuous_cleanup_task(self):
        """Continuous cleanup task"""
        while True:
            try:
                await self.user_safety_system.cleanup_old_alerts()
                await asyncio.sleep(300)  # Clean up every 5 minutes
            except Exception as e:
                self.logger.error(f"Cleanup task error: {e}")
                await asyncio.sleep(300)

# Global instance
validation_framework = AtlasComprehensiveValidationFramework()
