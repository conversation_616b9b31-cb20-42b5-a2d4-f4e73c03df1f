"""
A.T.L.A.S. Multi-API Manager
Intelligent API key rotation, load balancing, and data source management for high-volume scanning
"""

import asyncio
import logging
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
import json
from collections import defaultdict, deque

from atlas_rate_limiter import APIProvider, AtlasRateLimiter
from config import settings

logger = logging.getLogger(__name__)


@dataclass
class APIKeyConfig:
    """Configuration for individual API key"""
    key: str
    provider: APIProvider
    tier: str = "free"  # free, basic, premium, enterprise
    requests_per_minute: int = 250
    requests_per_second: int = 5
    daily_limit: int = 10000
    monthly_limit: int = 100000
    is_active: bool = True
    last_used: Optional[datetime] = None
    usage_today: int = 0
    usage_this_month: int = 0
    error_count: int = 0
    success_rate: float = 1.0


@dataclass
class DataSourceHealth:
    """Health metrics for data sources"""
    provider: APIProvider
    response_time_avg: float = 0.0
    success_rate: float = 1.0
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
    consecutive_failures: int = 0
    is_healthy: bool = True


class MultiAPIManager:
    """Advanced multi-API manager with intelligent load balancing"""
    
    def __init__(self):
        self.api_keys: Dict[APIProvider, List[APIKeyConfig]] = defaultdict(list)
        self.source_health: Dict[APIProvider, DataSourceHealth] = {}
        self.rate_limiter = AtlasRateLimiter()
        
        # Load balancing
        self.current_key_index: Dict[APIProvider, int] = defaultdict(int)
        self.key_usage_tracking: Dict[str, Dict[str, Any]] = {}
        
        # Performance metrics
        self.request_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'requests_by_provider': defaultdict(int),
            'errors_by_provider': defaultdict(int)
        }
        
        # Session management
        self.sessions: Dict[APIProvider, aiohttp.ClientSession] = {}
        self.session_timeout = aiohttp.ClientTimeout(total=30)
        
        logger.info("Multi-API Manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize API manager with multiple keys and health checks"""
        try:
            # Load API keys from configuration
            await self._load_api_keys()
            
            # Initialize health monitoring
            await self._initialize_health_monitoring()
            
            # Create HTTP sessions
            await self._create_sessions()
            
            # Start rate limiter
            await self.rate_limiter.start_processing()
            
            logger.info(f"Multi-API Manager initialized with {self._get_total_keys()} API keys")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Multi-API Manager: {e}")
            return False
    
    async def _load_api_keys(self):
        """Load API keys from configuration and environment"""
        try:
            # FMP API Keys (support multiple keys for higher limits)
            fmp_keys = []
            
            # Primary FMP key
            if settings.FMP_API_KEY:
                fmp_keys.append(APIKeyConfig(
                    key=settings.FMP_API_KEY,
                    provider=APIProvider.FMP,
                    tier="premium",  # Assuming paid tier
                    requests_per_minute=3000,
                    requests_per_second=50,
                    daily_limit=100000,
                    monthly_limit=1000000
                ))
            
            # Additional FMP keys (if configured)
            for i in range(2, 6):  # Support up to 5 FMP keys
                key_env = f"FMP_API_KEY_{i}"
                key_value = getattr(settings, key_env, None)
                if key_value:
                    fmp_keys.append(APIKeyConfig(
                        key=key_value,
                        provider=APIProvider.FMP,
                        tier="premium",
                        requests_per_minute=3000,
                        requests_per_second=50,
                        daily_limit=100000,
                        monthly_limit=1000000
                    ))
            
            self.api_keys[APIProvider.FMP] = fmp_keys
            
            # Alpaca API Keys
            if settings.ALPACA_API_KEY:
                self.api_keys[APIProvider.ALPACA].append(APIKeyConfig(
                    key=settings.ALPACA_API_KEY,
                    provider=APIProvider.ALPACA,
                    tier="premium",
                    requests_per_minute=1000,
                    requests_per_second=20,
                    daily_limit=50000,
                    monthly_limit=500000
                ))
            
            # Polygon API Keys (if configured)
            polygon_key = getattr(settings, 'POLYGON_API_KEY', None)
            if polygon_key:
                self.api_keys[APIProvider.POLYGON].append(APIKeyConfig(
                    key=polygon_key,
                    provider=APIProvider.POLYGON,
                    tier="premium",
                    requests_per_minute=1000,
                    requests_per_second=20,
                    daily_limit=50000,
                    monthly_limit=500000
                ))
            
            # Alpha Vantage API Keys (if configured)
            av_key = getattr(settings, 'ALPHA_VANTAGE_API_KEY', None)
            if av_key:
                self.api_keys[APIProvider.ALPHA_VANTAGE].append(APIKeyConfig(
                    key=av_key,
                    provider=APIProvider.ALPHA_VANTAGE,
                    tier="premium",
                    requests_per_minute=75,
                    requests_per_second=5,
                    daily_limit=5000,
                    monthly_limit=50000
                ))
            
            # Log loaded keys (without exposing actual keys)
            for provider, keys in self.api_keys.items():
                logger.info(f"Loaded {len(keys)} API keys for {provider.value}")
            
        except Exception as e:
            logger.error(f"Error loading API keys: {e}")
    
    async def _initialize_health_monitoring(self):
        """Initialize health monitoring for all providers"""
        for provider in APIProvider:
            self.source_health[provider] = DataSourceHealth(provider=provider)
    
    async def _create_sessions(self):
        """Create HTTP sessions for each provider"""
        for provider in self.api_keys.keys():
            self.sessions[provider] = aiohttp.ClientSession(
                timeout=self.session_timeout,
                headers={'User-Agent': 'ATLAS-Trading-System/1.0'}
            )
    
    def _get_total_keys(self) -> int:
        """Get total number of API keys across all providers"""
        return sum(len(keys) for keys in self.api_keys.values())
    
    async def get_best_api_key(self, provider: APIProvider) -> Optional[APIKeyConfig]:
        """Get the best available API key for a provider using intelligent selection with rotation"""
        try:
            available_keys = [key for key in self.api_keys.get(provider, [])
                            if key.is_active and not self._is_key_exhausted(key)]

            if not available_keys:
                logger.warning(f"No available API keys for {provider.value}")
                # Try to reactivate keys that might have recovered
                await self._attempt_key_recovery(provider)
                available_keys = [key for key in self.api_keys.get(provider, [])
                                if key.is_active and not self._is_key_exhausted(key)]

                if not available_keys:
                    return None

            # Implement intelligent key rotation
            best_key = await self._select_key_with_rotation(provider, available_keys)

            # Update usage tracking
            best_key.last_used = datetime.now()
            self._update_key_usage(best_key)

            return best_key

        except Exception as e:
            logger.error(f"Error selecting API key for {provider.value}: {e}")
            return None

    async def _select_key_with_rotation(self, provider: APIProvider, available_keys: List[APIKeyConfig]) -> APIKeyConfig:
        """Select API key using intelligent rotation strategy"""
        try:
            # Strategy 1: Load balancing - prefer least used keys
            if len(available_keys) > 1:
                # Sort by usage (ascending) and health score (descending)
                available_keys.sort(key=lambda k: (k.usage_today, -self._calculate_key_score(k)))

                # Select from top 3 candidates to balance load and performance
                top_candidates = available_keys[:min(3, len(available_keys))]

                # Weighted random selection from top candidates
                weights = [self._calculate_key_score(key) for key in top_candidates]
                total_weight = sum(weights)

                if total_weight > 0:
                    import random
                    rand_val = random.uniform(0, total_weight)
                    cumulative = 0

                    for i, weight in enumerate(weights):
                        cumulative += weight
                        if rand_val <= cumulative:
                            return top_candidates[i]

                # Fallback to first candidate
                return top_candidates[0]

            # Single key available
            return available_keys[0]

        except Exception as e:
            logger.error(f"Error in key rotation selection: {e}")
            return available_keys[0] if available_keys else None

    async def _attempt_key_recovery(self, provider: APIProvider):
        """Attempt to recover disabled keys that might be working again"""
        try:
            disabled_keys = [key for key in self.api_keys.get(provider, []) if not key.is_active]

            for key in disabled_keys:
                # Only attempt recovery if key has been disabled for at least 5 minutes
                if key.last_used and (datetime.now() - key.last_used).total_seconds() > 300:
                    # Reset error count and reactivate for testing
                    key.error_count = 0
                    key.success_rate = 0.5  # Give it a chance
                    key.is_active = True

                    logger.info(f"Attempting recovery of API key for {provider.value}")
                    break  # Only recover one key at a time

        except Exception as e:
            logger.error(f"Error in key recovery: {e}")

    def rotate_to_next_key(self, provider: APIProvider, current_key: APIKeyConfig):
        """Manually rotate to next available key (for error handling)"""
        try:
            available_keys = [key for key in self.api_keys.get(provider, [])
                            if key.is_active and key != current_key and not self._is_key_exhausted(key)]

            if available_keys:
                # Temporarily reduce current key's priority
                current_key.error_count += 5
                current_key.success_rate = max(0.1, current_key.success_rate - 0.2)

                logger.info(f"Rotated away from problematic API key for {provider.value}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error rotating API key: {e}")
            return False
    
    def _is_key_exhausted(self, key: APIKeyConfig) -> bool:
        """Check if API key has reached its limits"""
        now = datetime.now()
        
        # Check daily limit
        if key.usage_today >= key.daily_limit:
            return True
        
        # Check monthly limit
        if key.usage_this_month >= key.monthly_limit:
            return True
        
        # Check if key has too many recent errors
        if key.error_count > 10 and key.success_rate < 0.5:
            return True
        
        return False
    
    def _calculate_key_score(self, key: APIKeyConfig) -> float:
        """Calculate health score for API key selection"""
        score = key.success_rate * 0.4  # Success rate weight
        
        # Usage factor (prefer less used keys)
        usage_factor = 1.0 - (key.usage_today / key.daily_limit)
        score += usage_factor * 0.3
        
        # Error factor
        error_factor = max(0, 1.0 - (key.error_count / 100))
        score += error_factor * 0.2
        
        # Recency factor (prefer recently successful keys)
        if key.last_used:
            hours_since_use = (datetime.now() - key.last_used).total_seconds() / 3600
            recency_factor = max(0, 1.0 - (hours_since_use / 24))  # Decay over 24 hours
            score += recency_factor * 0.1
        
        return score
    
    def _update_key_usage(self, key: APIKeyConfig):
        """Update usage statistics for API key"""
        key.usage_today += 1
        key.usage_this_month += 1
        
        # Reset daily usage if new day
        now = datetime.now()
        if key.last_used and key.last_used.date() != now.date():
            key.usage_today = 1
    
    async def make_request(self, provider: APIProvider, endpoint: str, 
                          params: Dict[str, Any] = None, priority: int = 1) -> Optional[Dict[str, Any]]:
        """Make API request with intelligent key selection and retry logic"""
        try:
            # Get best API key
            api_key = await self.get_best_api_key(provider)
            if not api_key:
                logger.error(f"No available API key for {provider.value}")
                return None
            
            # Build request
            session = self.sessions.get(provider)
            if not session:
                logger.error(f"No session available for {provider.value}")
                return None
            
            # Prepare URL and parameters
            base_urls = {
                APIProvider.FMP: "https://financialmodelingprep.com/api/v3",
                APIProvider.ALPACA: "https://paper-api.alpaca.markets/v2",
                APIProvider.POLYGON: "https://api.polygon.io/v2",
                APIProvider.ALPHA_VANTAGE: "https://www.alphavantage.co/query"
            }
            
            base_url = base_urls.get(provider)
            if not base_url:
                logger.error(f"No base URL configured for {provider.value}")
                return None
            
            url = f"{base_url}/{endpoint.lstrip('/')}"
            
            # Add API key to parameters
            if not params:
                params = {}
            
            if provider == APIProvider.FMP:
                params['apikey'] = api_key.key
            elif provider == APIProvider.ALPACA:
                # Alpaca uses headers for authentication
                pass
            elif provider == APIProvider.POLYGON:
                params['apikey'] = api_key.key
            elif provider == APIProvider.ALPHA_VANTAGE:
                params['apikey'] = api_key.key
            
            # Make request with rate limiting
            start_time = time.time()
            
            async with session.get(url, params=params) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    
                    # Update success metrics
                    await self._update_success_metrics(provider, api_key, response_time)
                    
                    return data
                else:
                    # Handle error
                    error_text = await response.text()
                    logger.warning(f"API request failed: {response.status} - {error_text}")
                    
                    await self._update_error_metrics(provider, api_key, response.status)
                    return None
            
        except Exception as e:
            logger.error(f"Error making API request to {provider.value}: {e}")
            if 'api_key' in locals():
                await self._update_error_metrics(provider, api_key, 0)
            return None
    
    async def _update_success_metrics(self, provider: APIProvider, key: APIKeyConfig, response_time: float):
        """Update success metrics for provider and key"""
        # Update key metrics
        key.error_count = max(0, key.error_count - 1)  # Reduce error count on success
        key.success_rate = min(1.0, key.success_rate + 0.01)  # Gradually improve success rate
        
        # Update provider health
        health = self.source_health[provider]
        health.last_success = datetime.now()
        health.consecutive_failures = 0
        health.is_healthy = True
        
        # Update response time (exponential moving average)
        if health.response_time_avg == 0:
            health.response_time_avg = response_time
        else:
            health.response_time_avg = 0.9 * health.response_time_avg + 0.1 * response_time
        
        # Update global metrics
        self.request_metrics['total_requests'] += 1
        self.request_metrics['successful_requests'] += 1
        self.request_metrics['requests_by_provider'][provider] += 1
    
    async def _update_error_metrics(self, provider: APIProvider, key: APIKeyConfig, status_code: int):
        """Update error metrics for provider and key"""
        # Update key metrics
        key.error_count += 1
        key.success_rate = max(0.0, key.success_rate - 0.05)  # Reduce success rate on error
        
        # Disable key if too many errors
        if key.error_count > 20 and key.success_rate < 0.3:
            key.is_active = False
            logger.warning(f"Disabled API key for {provider.value} due to high error rate")
        
        # Update provider health
        health = self.source_health[provider]
        health.last_failure = datetime.now()
        health.consecutive_failures += 1
        
        if health.consecutive_failures > 5:
            health.is_healthy = False
            logger.warning(f"Marked {provider.value} as unhealthy due to consecutive failures")
        
        # Update global metrics
        self.request_metrics['total_requests'] += 1
        self.request_metrics['failed_requests'] += 1
        self.request_metrics['errors_by_provider'][provider] += 1
    
    def get_provider_health(self, provider: APIProvider) -> Optional[DataSourceHealth]:
        """Get health status for specific provider"""
        return self.source_health.get(provider)
    
    def get_healthy_providers(self) -> List[APIProvider]:
        """Get list of healthy providers"""
        return [provider for provider, health in self.source_health.items() if health.is_healthy]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics"""
        return {
            'total_api_keys': self._get_total_keys(),
            'keys_by_provider': {provider.value: len(keys) for provider, keys in self.api_keys.items()},
            'healthy_providers': [p.value for p in self.get_healthy_providers()],
            'request_metrics': self.request_metrics,
            'provider_health': {
                provider.value: {
                    'is_healthy': health.is_healthy,
                    'success_rate': health.success_rate,
                    'avg_response_time': health.response_time_avg,
                    'consecutive_failures': health.consecutive_failures
                }
                for provider, health in self.source_health.items()
            }
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            # Close all sessions
            for session in self.sessions.values():
                await session.close()
            
            # Stop rate limiter
            await self.rate_limiter.stop_processing()
            
            logger.info("Multi-API Manager cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
