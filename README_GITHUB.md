# 🚀 A.T.L.A.S. v5.0 - Advanced Trading & Learning Analytics System

[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104%2B-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

## 📋 Overview

A.T.L.A.S. v5.0 is a comprehensive, AI-powered trading system that combines real-time market analysis, advanced pattern recognition, and intelligent trading strategies. Built for both beginners and experienced traders, it provides actionable trading insights with flexible profit targets and timeframes.

### 🎯 Key Features

- **🤖 AI-Powered Trading Plans**: Generate custom trading strategies with flexible profit targets ($100-$10,000+) and timeframes (1 day to 1 month+)
- **📊 Real-Time Market Analysis**: Live data from Alpaca, FMP, and Yahoo Finance APIs
- **🔍 Lee Method Scanner**: Advanced pattern recognition for high-probability trading setups
- **💬 Conversational Interface**: Natural language trading requests with Grok AI integration
- **📱 Web Interface**: Modern, responsive web UI with real-time WebSocket updates
- **⚡ Terminal Interface**: Command-line access for advanced users
- **🛡️ Risk Management**: Built-in position sizing, stop-losses, and portfolio protection
- **📈 Live Paper Trading**: Test strategies with real market data before risking capital

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- API keys for:
  - [Alpaca Trading](https://alpaca.markets/) (Paper trading account)
  - [Financial Modeling Prep](https://financialmodelingprep.com/)
  - [Grok AI](https://x.ai/) (Optional, for enhanced AI features)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/atlas-v5.git
   cd atlas-v5
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   ```bash
   cp .env.template .env
   # Edit .env with your API keys
   ```

4. **Start the system**
   ```bash
   python atlas_production_server.py
   ```

5. **Access the web interface**
   - Open http://localhost:8002 in your browser
   - Start trading with natural language commands!

## 💬 Usage Examples

### Flexible Trading Targets

A.T.L.A.S. v5.0 accepts completely flexible profit targets and timeframes:

```
"I want to make $500 in 2 weeks"
"Generate $1000 profit in 1 month"
"Help me earn $200 in 5 days"
"Create a trading plan for $100 today"
"I need $2500 in 3 weeks"
```

### Natural Language Commands

```
"Scan the market for Lee Method signals"
"What's AAPL trading at right now?"
"Show me my current positions"
"Find high-probability trading setups"
"Analyze TSLA for potential trades"
```

## 🏗️ Architecture

### Core Components

- **`atlas_production_server.py`** - FastAPI web server and API endpoints
- **`atlas_ai_engine.py`** - Main AI processing engine with flexible target parsing
- **`atlas_lee_method.py`** - Advanced pattern recognition scanner
- **`atlas_trading_plan_engine.py`** - Comprehensive trading plan generation
- **`atlas_beginner_grok_system.py`** - Beginner-friendly AI responses
- **`atlas_interface.html`** - Modern web interface with real-time updates

### Data Sources

- **Alpaca Markets API** - Real-time stock data and paper trading
- **Financial Modeling Prep** - Fundamental data and market insights
- **Yahoo Finance** - Backup market data and historical information
- **Grok AI** - Enhanced natural language processing

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Trading APIs
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret
FMP_API_KEY=your_fmp_key

# AI Enhancement
GROK_API_KEY=your_grok_key  # Optional

# Trading Settings
PAPER_TRADING=true
MAX_DAILY_LOSS=0.02  # 2% max daily loss
MAX_POSITION_SIZE=0.10  # 10% max position size
```

### Trading Parameters

- **Flexible Profit Targets**: $50 - $50,000+ (no hardcoded limits)
- **Flexible Timeframes**: 1 day to 12 months (intelligent defaults)
- **Risk Management**: Configurable stop-losses and position sizing
- **Paper Trading**: Safe testing environment with real market data

## 📊 Features in Detail

### 🎯 Flexible Trading Targets

Unlike traditional systems with fixed targets, A.T.L.A.S. v5.0 provides:

- **Dynamic Target Recognition**: Automatically extracts profit amounts from natural language
- **Intelligent Timeframe Matching**: Matches realistic timeframes to target amounts
- **Context-Aware Defaults**: Uses message context for intelligent defaults when amounts aren't specified
- **Scalable Strategies**: Adjusts trading approach based on target size and timeframe

### 🔍 Advanced Market Analysis

- **Lee Method Pattern Recognition**: Identifies high-probability trading setups
- **Real-Time Data Processing**: Live market data with sub-second updates
- **Multi-Timeframe Analysis**: Analyzes patterns across different time horizons
- **Risk-Adjusted Recommendations**: All suggestions include proper risk management

### 🤖 AI-Powered Intelligence

- **Natural Language Processing**: Understands complex trading requests
- **Grok AI Integration**: Enhanced reasoning and market analysis
- **Beginner-Friendly Responses**: Tailored explanations for different experience levels
- **Continuous Learning**: Adapts to user preferences and market conditions

## 🛡️ Security & Compliance

- **Paper Trading First**: All trading starts in simulation mode
- **API Key Protection**: Secure environment variable management
- **Input Validation**: Comprehensive sanitization of all user inputs
- **Audit Logging**: Complete transaction and decision logging
- **Risk Limits**: Built-in safeguards prevent excessive losses

## 📈 Performance

- **Response Time**: < 2 seconds for most queries
- **Real-Time Updates**: WebSocket connections for live data
- **Scalability**: Handles multiple concurrent users
- **Reliability**: 99.9% uptime with automatic error recovery

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Test flexible trading targets
python test_flexible_trading_targets.py

# Test WebSocket connections
python test_websocket_connection.py

# Test terminal-web integration
python test_terminal_web_integration.py

# Full diagnostic
python atlas_connection_diagnostic.py
```

## 📝 API Documentation

### REST Endpoints

- `GET /api/v1/health` - System health check
- `POST /api/v1/chat` - Natural language trading interface
- `GET /api/v1/trading/positions` - Current trading positions
- `GET /api/v1/lee_method/signals` - Active trading signals
- `GET /api/v1/websocket-metrics` - WebSocket connection status

### WebSocket Streams

- `ws://localhost:8002/ws/scanner` - Real-time market updates
- Real-time trading alerts and notifications
- Live position updates and market data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

A.T.L.A.S. v5.0 is for educational and research purposes only. All trading involves risk of loss. Never trade with money you cannot afford to lose. Always consult with qualified financial advisors before making investment decisions.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Join our community discussions
- **Email**: <EMAIL>

## 🎉 Acknowledgments

- Built with FastAPI, Python, and modern web technologies
- Powered by Alpaca Markets, FMP, and Grok AI
- Inspired by the trading community's need for flexible, intelligent tools

---

**Made with ❤️ by the A.T.L.A.S. Development Team**
