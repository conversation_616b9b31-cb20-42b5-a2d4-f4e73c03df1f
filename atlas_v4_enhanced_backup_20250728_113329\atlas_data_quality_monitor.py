"""
A.T.L.A.S. Data Quality Monitor
Comprehensive real-time data validation, accuracy checking, and quality assurance system
"""

import asyncio
import logging
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
import numpy as np
import aiohttp
import json
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class DataQualityLevel(Enum):
    """Data quality assessment levels"""
    EXCELLENT = "EXCELLENT"
    GOOD = "GOOD"
    ACCEPTABLE = "ACCEPTABLE"
    POOR = "POOR"
    CRITICAL = "CRITICAL"

class DataSourceStatus(Enum):
    """Data source status levels"""
    ACTIVE = "ACTIVE"
    DEGRADED = "DEGRADED"
    FAILED = "FAILED"
    UNKNOWN = "UNKNOWN"

@dataclass
class DataQualityMetric:
    """Individual data quality metric"""
    metric_name: str
    value: float
    threshold: float
    status: str
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class DataSourceHealth:
    """Health status of a data source"""
    source_name: str
    status: DataSourceStatus
    last_successful_fetch: Optional[datetime]
    error_count: int
    success_rate: float
    avg_response_time: float
    data_quality_score: float
    issues: List[str] = field(default_factory=list)

class AtlasDataQualityMonitor:
    """Comprehensive data quality monitoring and validation system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Data quality thresholds
        self.quality_thresholds = {
            'price_staleness_seconds': 30,  # Market hours
            'price_staleness_seconds_after_hours': 300,  # After hours
            'price_change_threshold': 0.20,  # 20% max change per minute
            'volume_anomaly_threshold': 5.0,  # 5x normal volume
            'bid_ask_spread_threshold': 0.05,  # 5% max spread
            'data_completeness_threshold': 0.95,  # 95% complete data required
            'calculation_accuracy_threshold': 0.001,  # 0.1% calculation tolerance
            'api_response_time_threshold': 5.0,  # 5 seconds max response time
            'success_rate_threshold': 0.95  # 95% success rate required
        }
        
        # Data source monitoring
        self.data_sources = {
            'fmp': DataSourceHealth('FMP', DataSourceStatus.UNKNOWN, None, 0, 0.0, 0.0, 0.0),
            'alpaca': DataSourceHealth('Alpaca', DataSourceStatus.UNKNOWN, None, 0, 0.0, 0.0, 0.0),
            'yfinance': DataSourceHealth('YFinance', DataSourceStatus.UNKNOWN, None, 0, 0.0, 0.0, 0.0)
        }
        
        # Quality metrics tracking
        self.quality_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.price_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.volume_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Alert system
        self.quality_alerts: List[Dict[str, Any]] = []
        self.alert_cooldowns: Dict[str, datetime] = {}
        self.alert_cooldown_period = 300  # 5 minutes
        
        # Cross-validation cache
        self.cross_validation_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 60  # 1 minute cache
        
        # Mathematical validation
        self.calculation_validators = {}
        self._initialize_calculation_validators()
        
        self.logger.info("[DATA_QUALITY] Data Quality Monitor initialized")
    
    def _initialize_calculation_validators(self):
        """Initialize mathematical calculation validators"""
        self.calculation_validators = {
            'moving_average': self._validate_moving_average,
            'rsi': self._validate_rsi,
            'macd': self._validate_macd,
            'bollinger_bands': self._validate_bollinger_bands,
            'portfolio_metrics': self._validate_portfolio_metrics
        }
    
    async def validate_market_data(self, symbol: str, data: Dict[str, Any], 
                                 source: str) -> Tuple[bool, DataQualityLevel, List[str]]:
        """Comprehensive market data validation"""
        try:
            issues = []
            quality_score = 100.0
            
            # 1. Basic data structure validation
            if not self._validate_data_structure(data):
                issues.append("Invalid data structure")
                quality_score -= 30
            
            # 2. Price validation
            price_valid, price_issues = await self._validate_price_data(symbol, data, source)
            if not price_valid:
                issues.extend(price_issues)
                quality_score -= 25
            
            # 3. Timestamp validation
            timestamp_valid, timestamp_issues = self._validate_timestamp(data, source)
            if not timestamp_valid:
                issues.extend(timestamp_issues)
                quality_score -= 20
            
            # 4. Cross-source validation
            cross_valid, cross_issues = await self._cross_validate_data(symbol, data, source)
            if not cross_valid:
                issues.extend(cross_issues)
                quality_score -= 15
            
            # 5. Historical consistency validation
            consistency_valid, consistency_issues = self._validate_historical_consistency(symbol, data)
            if not consistency_valid:
                issues.extend(consistency_issues)
                quality_score -= 10
            
            # Determine quality level
            quality_level = self._determine_quality_level(quality_score)
            
            # Record metrics
            await self._record_quality_metrics(symbol, source, quality_score, issues)
            
            # Update source health
            self._update_source_health(source, quality_score, len(issues) == 0)
            
            # Generate alerts if needed
            if quality_level in [DataQualityLevel.POOR, DataQualityLevel.CRITICAL]:
                await self._generate_quality_alert(symbol, source, quality_level, issues)
            
            is_valid = quality_level not in [DataQualityLevel.CRITICAL]
            return is_valid, quality_level, issues
            
        except Exception as e:
            self.logger.error(f"Data validation error for {symbol} from {source}: {e}")
            return False, DataQualityLevel.CRITICAL, [f"Validation error: {str(e)}"]
    
    def _validate_data_structure(self, data: Dict[str, Any]) -> bool:
        """Validate basic data structure"""
        required_fields = ['symbol', 'price', 'timestamp']
        return all(field in data for field in required_fields)
    
    async def _validate_price_data(self, symbol: str, data: Dict[str, Any], 
                                 source: str) -> Tuple[bool, List[str]]:
        """Validate price data accuracy and reasonableness"""
        issues = []
        
        try:
            price = float(data.get('price', 0))
            
            # Basic price validation
            if price <= 0:
                issues.append(f"Invalid price: {price}")
                return False, issues
            
            if price > 1000000:  # $1M sanity check
                issues.append(f"Extreme price detected: ${price:,.2f}")
            
            # Price change validation (if we have history)
            if symbol in self.price_history and self.price_history[symbol]:
                last_price = self.price_history[symbol][-1]['price']
                time_diff = (datetime.now() - self.price_history[symbol][-1]['timestamp']).total_seconds()
                
                if time_diff < 300:  # Within 5 minutes
                    price_change = abs(price - last_price) / last_price
                    if price_change > self.quality_thresholds['price_change_threshold']:
                        issues.append(f"Extreme price change: {price_change:.1%} in {time_diff:.0f}s")
            
            # Store price history
            self.price_history[symbol].append({
                'price': price,
                'timestamp': datetime.now(),
                'source': source
            })
            
            return len(issues) == 0, issues
            
        except Exception as e:
            issues.append(f"Price validation error: {str(e)}")
            return False, issues
    
    def _validate_timestamp(self, data: Dict[str, Any], source: str) -> Tuple[bool, List[str]]:
        """Validate timestamp freshness and accuracy"""
        issues = []
        
        try:
            timestamp = data.get('timestamp')
            if not timestamp:
                issues.append("Missing timestamp")
                return False, issues
            
            # Convert to datetime if needed
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            
            # Check staleness
            age_seconds = (datetime.now() - timestamp.replace(tzinfo=None)).total_seconds()
            
            # Different thresholds for market hours vs after hours
            from atlas_market_core import AtlasMarketEngine
            market_engine = AtlasMarketEngine()
            is_market_hours = market_engine._is_market_hours() if hasattr(market_engine, '_is_market_hours') else True
            
            threshold = (self.quality_thresholds['price_staleness_seconds'] if is_market_hours 
                        else self.quality_thresholds['price_staleness_seconds_after_hours'])
            
            if age_seconds > threshold:
                issues.append(f"Stale data: {age_seconds:.0f}s old (threshold: {threshold}s)")
            
            return len(issues) == 0, issues
            
        except Exception as e:
            issues.append(f"Timestamp validation error: {str(e)}")
            return False, issues
    
    async def _cross_validate_data(self, symbol: str, data: Dict[str, Any], 
                                 source: str) -> Tuple[bool, List[str]]:
        """Cross-validate data against other sources"""
        issues = []
        
        try:
            # Check cache first
            cache_key = f"{symbol}_{int(time.time() // self.cache_ttl)}"
            if cache_key in self.cross_validation_cache:
                cached_data = self.cross_validation_cache[cache_key]
                
                # Compare prices
                price = float(data.get('price', 0))
                for cached_source, cached_price in cached_data.items():
                    if cached_source != source and cached_price > 0:
                        price_diff = abs(price - cached_price) / cached_price
                        if price_diff > 0.02:  # 2% difference threshold
                            issues.append(f"Price discrepancy with {cached_source}: {price_diff:.1%}")
            
            # Store in cache for future comparisons
            if cache_key not in self.cross_validation_cache:
                self.cross_validation_cache[cache_key] = {}
            self.cross_validation_cache[cache_key][source] = float(data.get('price', 0))
            
            return len(issues) == 0, issues
            
        except Exception as e:
            self.logger.warning(f"Cross-validation error for {symbol}: {e}")
            return True, []  # Don't fail validation on cross-validation errors
    
    def _validate_historical_consistency(self, symbol: str, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate data consistency with historical patterns"""
        issues = []
        
        try:
            # Volume validation
            volume = data.get('volume', 0)
            if volume and symbol in self.volume_history and self.volume_history[symbol]:
                recent_volumes = [v['volume'] for v in list(self.volume_history[symbol])[-10:]]
                if recent_volumes:
                    avg_volume = statistics.mean(recent_volumes)
                    if avg_volume > 0 and volume > avg_volume * self.quality_thresholds['volume_anomaly_threshold']:
                        issues.append(f"Volume anomaly: {volume:,} vs avg {avg_volume:,.0f}")
            
            # Store volume history
            if volume:
                self.volume_history[symbol].append({
                    'volume': volume,
                    'timestamp': datetime.now()
                })
            
            return len(issues) == 0, issues
            
        except Exception as e:
            self.logger.warning(f"Historical consistency validation error for {symbol}: {e}")
            return True, []
    
    def _determine_quality_level(self, quality_score: float) -> DataQualityLevel:
        """Determine data quality level based on score"""
        if quality_score >= 95:
            return DataQualityLevel.EXCELLENT
        elif quality_score >= 85:
            return DataQualityLevel.GOOD
        elif quality_score >= 70:
            return DataQualityLevel.ACCEPTABLE
        elif quality_score >= 50:
            return DataQualityLevel.POOR
        else:
            return DataQualityLevel.CRITICAL
    
    async def _record_quality_metrics(self, symbol: str, source: str, 
                                    quality_score: float, issues: List[str]):
        """Record quality metrics for monitoring"""
        metric = DataQualityMetric(
            metric_name=f"{source}_quality_score",
            value=quality_score,
            threshold=85.0,
            status="PASS" if quality_score >= 85 else "FAIL",
            timestamp=datetime.now(),
            details={
                'symbol': symbol,
                'source': source,
                'issues': issues,
                'issue_count': len(issues)
            }
        )
        
        self.quality_metrics[f"{source}_quality"].append(metric)
    
    def _update_source_health(self, source: str, quality_score: float, success: bool):
        """Update data source health metrics"""
        if source in self.data_sources:
            health = self.data_sources[source]
            
            if success:
                health.last_successful_fetch = datetime.now()
                health.status = DataSourceStatus.ACTIVE
            else:
                health.error_count += 1
                if health.error_count > 5:
                    health.status = DataSourceStatus.FAILED
                elif health.error_count > 2:
                    health.status = DataSourceStatus.DEGRADED
            
            # Update quality score (rolling average)
            if hasattr(health, '_quality_scores'):
                health._quality_scores.append(quality_score)
                if len(health._quality_scores) > 10:
                    health._quality_scores.pop(0)
                health.data_quality_score = statistics.mean(health._quality_scores)
            else:
                health._quality_scores = [quality_score]
                health.data_quality_score = quality_score
    
    async def _generate_quality_alert(self, symbol: str, source: str, 
                                    quality_level: DataQualityLevel, issues: List[str]):
        """Generate data quality alert"""
        alert_key = f"{source}_{symbol}_{quality_level.value}"
        
        # Check cooldown
        if alert_key in self.alert_cooldowns:
            if (datetime.now() - self.alert_cooldowns[alert_key]).total_seconds() < self.alert_cooldown_period:
                return
        
        alert = {
            'type': 'DATA_QUALITY_ALERT',
            'severity': 'CRITICAL' if quality_level == DataQualityLevel.CRITICAL else 'WARNING',
            'symbol': symbol,
            'source': source,
            'quality_level': quality_level.value,
            'issues': issues,
            'timestamp': datetime.now().isoformat(),
            'message': f"Data quality {quality_level.value} for {symbol} from {source}: {', '.join(issues)}"
        }
        
        self.quality_alerts.append(alert)
        self.alert_cooldowns[alert_key] = datetime.now()
        
        self.logger.warning(f"[DATA_QUALITY_ALERT] {alert['message']}")
    
    # Mathematical calculation validators
    def _validate_moving_average(self, data: pd.Series, period: int, result: float) -> Tuple[bool, str]:
        """Validate moving average calculation"""
        try:
            if len(data) < period:
                return False, f"Insufficient data for {period}-period MA"
            
            expected = data.tail(period).mean()
            tolerance = abs(expected * self.quality_thresholds['calculation_accuracy_threshold'])
            
            if abs(result - expected) > tolerance:
                return False, f"MA calculation error: expected {expected:.4f}, got {result:.4f}"
            
            return True, "Valid"
            
        except Exception as e:
            return False, f"MA validation error: {str(e)}"
    
    def _validate_rsi(self, data: pd.Series, period: int, result: float) -> Tuple[bool, str]:
        """Validate RSI calculation"""
        try:
            if not (0 <= result <= 100):
                return False, f"RSI out of range: {result}"

            # Basic RSI calculation check
            if len(data) < period + 1:
                return False, f"Insufficient data for {period}-period RSI"

            return True, "Valid"

        except Exception as e:
            return False, f"RSI validation error: {str(e)}"

    def _validate_macd(self, data: pd.Series, result: Dict[str, float]) -> Tuple[bool, str]:
        """Validate MACD calculation"""
        try:
            required_keys = ['macd', 'signal', 'histogram']
            if not all(key in result for key in required_keys):
                return False, "Missing MACD components"

            # Check for reasonable values
            for key, value in result.items():
                if not isinstance(value, (int, float)) or np.isnan(value) or np.isinf(value):
                    return False, f"Invalid {key} value: {value}"

            return True, "Valid"

        except Exception as e:
            return False, f"MACD validation error: {str(e)}"

    def _validate_bollinger_bands(self, data: pd.Series, result: Dict[str, float]) -> Tuple[bool, str]:
        """Validate Bollinger Bands calculation"""
        try:
            required_keys = ['upper', 'middle', 'lower']
            if not all(key in result for key in required_keys):
                return False, "Missing Bollinger Band components"

            # Check logical order: upper > middle > lower
            if not (result['upper'] > result['middle'] > result['lower']):
                return False, f"Invalid band order: {result}"

            return True, "Valid"

        except Exception as e:
            return False, f"Bollinger Bands validation error: {str(e)}"

    def _validate_portfolio_metrics(self, portfolio_data: Dict[str, Any],
                                  result: Dict[str, float]) -> Tuple[bool, str]:
        """Validate portfolio calculation metrics"""
        try:
            # Check for required metrics
            required_metrics = ['total_value', 'daily_return', 'total_return']
            missing_metrics = [m for m in required_metrics if m not in result]
            if missing_metrics:
                return False, f"Missing portfolio metrics: {missing_metrics}"

            # Validate total value is positive
            if result.get('total_value', 0) < 0:
                return False, f"Negative portfolio value: {result['total_value']}"

            # Validate return percentages are reasonable
            for return_key in ['daily_return', 'total_return']:
                if return_key in result:
                    return_val = result[return_key]
                    if abs(return_val) > 1.0:  # 100% change limit
                        return False, f"Extreme {return_key}: {return_val:.1%}"

            return True, "Valid"

        except Exception as e:
            return False, f"Portfolio validation error: {str(e)}"

    async def validate_calculation(self, calc_type: str, inputs: Dict[str, Any],
                                 result: Any) -> Tuple[bool, str]:
        """Validate mathematical calculations"""
        try:
            if calc_type not in self.calculation_validators:
                return True, f"No validator for {calc_type}"

            validator = self.calculation_validators[calc_type]

            if calc_type == 'moving_average':
                return validator(inputs['data'], inputs['period'], result)
            elif calc_type == 'rsi':
                return validator(inputs['data'], inputs['period'], result)
            elif calc_type in ['macd', 'bollinger_bands']:
                return validator(inputs['data'], result)
            elif calc_type == 'portfolio_metrics':
                return validator(inputs['portfolio_data'], result)
            else:
                return True, "Unknown calculation type"

        except Exception as e:
            return False, f"Calculation validation error: {str(e)}"

    def get_data_quality_report(self) -> Dict[str, Any]:
        """Generate comprehensive data quality report"""
        try:
            current_time = datetime.now()

            # Calculate overall quality scores
            source_scores = {}
            for source, health in self.data_sources.items():
                source_scores[source] = {
                    'status': health.status.value,
                    'quality_score': health.data_quality_score,
                    'success_rate': health.success_rate,
                    'error_count': health.error_count,
                    'last_successful_fetch': health.last_successful_fetch.isoformat() if health.last_successful_fetch else None,
                    'issues': health.issues
                }

            # Recent quality metrics
            recent_metrics = {}
            for metric_type, metrics in self.quality_metrics.items():
                if metrics:
                    recent = list(metrics)[-10:]  # Last 10 metrics
                    recent_metrics[metric_type] = {
                        'count': len(recent),
                        'avg_score': statistics.mean([m.value for m in recent]),
                        'pass_rate': sum(1 for m in recent if m.status == 'PASS') / len(recent),
                        'latest': recent[-1].value if recent else 0
                    }

            # Active alerts
            recent_alerts = [
                alert for alert in self.quality_alerts
                if (current_time - datetime.fromisoformat(alert['timestamp'])).total_seconds() < 3600
            ]

            return {
                'timestamp': current_time.isoformat(),
                'overall_status': self._calculate_overall_status(),
                'data_sources': source_scores,
                'quality_metrics': recent_metrics,
                'active_alerts': recent_alerts,
                'alert_count': len(recent_alerts),
                'monitoring_active': True
            }

        except Exception as e:
            self.logger.error(f"Error generating quality report: {e}")
            return {'error': str(e)}

    def _calculate_overall_status(self) -> str:
        """Calculate overall system data quality status"""
        try:
            active_sources = sum(1 for health in self.data_sources.values()
                               if health.status == DataSourceStatus.ACTIVE)
            total_sources = len(self.data_sources)

            if active_sources == total_sources:
                return "EXCELLENT"
            elif active_sources >= total_sources * 0.8:
                return "GOOD"
            elif active_sources >= total_sources * 0.5:
                return "ACCEPTABLE"
            else:
                return "CRITICAL"

        except Exception:
            return "UNKNOWN"

    async def start_monitoring(self):
        """Start continuous data quality monitoring"""
        self.logger.info("[DATA_QUALITY] Starting continuous monitoring...")

        while True:
            try:
                # Clean old cache entries
                current_time = int(time.time() // self.cache_ttl)
                old_keys = [k for k in self.cross_validation_cache.keys()
                           if int(k.split('_')[-1]) < current_time - 5]
                for key in old_keys:
                    del self.cross_validation_cache[key]

                # Clean old alerts
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.quality_alerts = [
                    alert for alert in self.quality_alerts
                    if datetime.fromisoformat(alert['timestamp']) > cutoff_time
                ]

                await asyncio.sleep(60)  # Monitor every minute

            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(60)

# Global instance
data_quality_monitor = AtlasDataQualityMonitor()
