# A.T.L.A.S. Multi-Timeframe Lee Method Scanner

## Overview

The enhanced A.T.L.A.S. Lee Method Scanner now supports **multi-timeframe analysis** with **daily and weekly patterns** plus **optional TTM Squeeze filtering**. This provides a more comprehensive view of market opportunities and improves signal quality through timeframe confirmation.

## Key Features

### 🎯 Multi-Timeframe Analysis
- **Daily Patterns**: Traditional 1-day Lee Method signals for short-term opportunities
- **Weekly Patterns**: Weekly Lee Method signals for stronger, longer-term trends
- **Timeframe Comparison**: Automatic comparison and confidence weighting
- **Best Signal Selection**: Intelligent selection of highest-confidence signal across timeframes

### 📊 TTM Squeeze Integration
- **Optional Filter**: TTM Squeeze can be used as an additional confirmation filter
- **Squeeze Status Detection**: Identifies active squeezes and squeeze releases
- **Momentum Direction**: Tracks bullish/bearish momentum within squeeze patterns
- **Confidence Boosting**: Signals with favorable TTM Squeeze conditions get confidence boosts

### 🚀 Enhanced Confidence Scoring
- **Timeframe Weighting**: Weekly signals receive 15% confidence boost
- **TTM Squeeze Bonus**: Squeeze release + bullish momentum = 20% confidence boost
- **Combined Analysis**: Multi-factor confidence calculation for better signal quality

## Usage Examples

### Basic Multi-Timeframe Scan
```python
from atlas_lee_method import LeeMethodScanner

scanner = LeeMethodScanner()
await scanner.initialize()

# Scan symbol across both daily and weekly timeframes
results = await scanner.scan_symbol_multi_timeframe('AAPL', include_ttm_squeeze=True)

print(f"Total signals: {results['summary']['total_signals']}")
print(f"Best confidence: {results['summary']['best_confidence']:.3f}")
print(f"Has weekly signal: {results['summary']['has_weekly_signal']}")
```

### Traditional Single Timeframe (Backward Compatible)
```python
# Still supports traditional single timeframe scanning
signal = await scanner.scan_symbol('AAPL', timeframes=['1day'])
if signal:
    print(f"Daily signal: {signal.confidence:.3f} confidence")

# Or weekly only
weekly_signal = await scanner.scan_symbol('AAPL', timeframes=['1week'])
if weekly_signal:
    print(f"Weekly signal: {weekly_signal.confidence:.3f} confidence")
```

### TTM Squeeze Analysis
```python
# Get detailed TTM Squeeze status
df = await scanner.fetch_historical_data('AAPL', timeframe='1day')
df_with_indicators = scanner.calculate_ttm_squeeze(df)
squeeze_status = scanner._check_ttm_squeeze_status(df_with_indicators)

print(f"Squeeze active: {squeeze_status['active']}")
print(f"Momentum direction: {squeeze_status['momentum_direction']}")
print(f"Squeeze release: {squeeze_status['squeeze_release']}")
```

## Signal Structure

### Multi-Timeframe Results
```python
{
    'symbol': 'AAPL',
    'timestamp': '2024-01-15T10:30:00',
    'signals': {
        '1day': {
            'signal_type': 'bullish_momentum',
            'confidence': 0.75,
            'entry_price': 185.50,
            'target_price': 192.00,
            'stop_loss': 182.00,
            'timeframe': '1day',
            'ttm_squeeze_active': True,
            'strength': 'STRONG'
        },
        '1week': {
            'signal_type': 'bullish_momentum', 
            'confidence': 0.82,
            'entry_price': 185.50,
            'target_price': 195.00,
            'stop_loss': 180.00,
            'timeframe': '1week',
            'ttm_squeeze_active': False,
            'strength': 'STRONG'
        }
    },
    'best_signal': {
        'timeframe': '1week',
        'confidence': 0.82,
        # ... (best signal details)
    },
    'summary': {
        'total_signals': 2,
        'best_confidence': 0.82,
        'has_daily_signal': True,
        'has_weekly_signal': True
    }
}
```

## Configuration Options

### Scanner Configuration
```python
# Enhanced scanner config supports multi-timeframe scanning
config = ScannerConfig(
    max_symbols=250,  # Scale to 250+ symbols
    enable_dynamic_prioritization=True,
    volatility_threshold=0.02,
    
    # Multi-tier scanning intervals
    ultra_priority_interval=2,   # seconds - Top 25 symbols
    high_priority_interval=5,    # seconds - Next 75 symbols  
    medium_priority_interval=15, # seconds - Next 100 symbols
    low_priority_interval=60     # seconds - Remaining symbols
)
```

### TTM Squeeze Settings
```python
# TTM Squeeze is calculated automatically with these parameters:
# - MACD: 12, 26, 9
# - Bollinger Bands: 20 period, 2 standard deviations
# - Keltner Channels: 20 period, 1.5 ATR multiplier
# - Momentum: Linear regression slope of MACD histogram
```

## Performance Optimizations

### 🚀 Intelligent Data Fetching
- **Weekly Resampling**: Daily data is resampled to weekly for efficiency
- **Batch Processing**: Multiple symbols processed in parallel
- **Smart Caching**: NO caching system as requested - always fresh data

### 📊 Timeframe-Specific Logic
- **Daily Patterns**: Focus on short-term momentum shifts
- **Weekly Patterns**: Emphasize longer-term trend confirmations
- **Cross-Timeframe Validation**: Weekly signals validate daily signals

### ⚡ Scalability Features
- **250+ Symbol Support**: Enhanced to handle large symbol universes
- **Tiered Scanning**: Different intervals for different priority symbols
- **Parallel Processing**: Concurrent scanning across timeframes

## Testing

Run the comprehensive test suite:
```bash
python test_multi_timeframe_scanner.py
```

This will test:
- ✅ Multi-timeframe scanning functionality
- ✅ TTM Squeeze integration
- ✅ Traditional vs multi-timeframe comparison
- ✅ Signal quality and confidence scoring
- ✅ Data fetching across timeframes

## Benefits

### 🎯 Improved Signal Quality
- **Higher Confidence**: Weekly confirmation increases signal reliability
- **Reduced False Positives**: Multi-timeframe validation filters weak signals
- **Better Risk/Reward**: Weekly targets provide better risk/reward ratios

### 📈 Enhanced Market Coverage
- **Short-term Opportunities**: Daily patterns for quick trades
- **Long-term Trends**: Weekly patterns for position trades
- **Comprehensive Analysis**: Full market perspective across timeframes

### 🔧 Flexible Implementation
- **Backward Compatible**: Existing code continues to work
- **Optional TTM Squeeze**: Can be enabled/disabled as needed
- **Configurable Timeframes**: Easy to add new timeframes in the future

## Integration with Real-Time Scanner

The multi-timeframe Lee Method integrates seamlessly with the real-time scanner:

```python
# Real-time scanner automatically uses multi-timeframe analysis
scanner = AtlasRealtimeScanner(config)
await scanner.initialize()

# Scanner will automatically:
# 1. Fetch both daily and weekly data
# 2. Apply Lee Method to both timeframes  
# 3. Calculate TTM Squeeze indicators
# 4. Select best signals across timeframes
# 5. Apply confidence boosting for favorable conditions
```

## Next Steps

1. **Test the Implementation**: Run `test_multi_timeframe_scanner.py`
2. **Configure Symbol Universe**: Set up your 250+ symbol list
3. **Adjust Timeframe Intervals**: Customize scanning frequencies
4. **Monitor Performance**: Track signal quality and system performance
5. **Scale Gradually**: Start with smaller symbol sets and scale up

The enhanced multi-timeframe Lee Method scanner provides a robust foundation for professional-grade trading signal generation with improved accuracy and comprehensive market coverage.
