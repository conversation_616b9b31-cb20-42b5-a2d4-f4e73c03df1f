# 🔧 A.T.L.A.S. Operational Guide

## 📋 **Daily Operations Manual**

This guide covers day-to-day operations, monitoring, and maintenance procedures for the A.T.L.A.S. Trading System in production.

---

## 🌅 **Daily Startup Procedures**

### **Morning Checklist (Before Market Open)**

```bash
# 1. Check system status
sudo systemctl status atlas nginx postgresql redis

# 2. Review overnight logs
sudo tail -100 /var/log/atlas/atlas.log | grep -i error
sudo tail -50 /var/log/nginx/error.log

# 3. Verify database connectivity
psql -h localhost -U atlas_user -d atlas_production -c "SELECT NOW();"

# 4. Check disk space
df -h
du -sh /opt/atlas/logs/*

# 5. Verify API connectivity
curl -s https://your-domain.com/api/health | jq .

# 6. Check scanner status
curl -s https://your-domain.com/api/scanner/status | jq .
```

### **Market Hours Monitoring (9:30 AM - 4:00 PM ET)**

1. **Real-time Monitoring**
   - Monitor Grafana dashboards
   - Watch for scanner alerts
   - Check system resource usage
   - Verify trading signals

2. **Alert Response**
   - Acknowledge critical alerts within 5 minutes
   - Investigate warning alerts within 15 minutes
   - Document all incidents

3. **Performance Checks**
   - Scanner response times < 2 seconds
   - API response times < 500ms
   - Database query times < 100ms
   - Memory usage < 80%

---

## 🌙 **End-of-Day Procedures**

### **Market Close Checklist (After 4:00 PM ET)**

```bash
# 1. Generate daily reports
cd /opt/atlas/app
python generate_daily_report.py

# 2. Backup trading data
sudo /opt/atlas/scripts/backup.sh

# 3. Clean up temporary files
find /tmp -name "atlas_*" -mtime +1 -delete

# 4. Rotate logs if needed
sudo logrotate /etc/logrotate.d/atlas

# 5. Check system health
python system_health_check.py

# 6. Update system metrics
python update_metrics.py
```

---

## 📊 **Monitoring & Alerting**

### **Key Metrics to Monitor**

1. **System Metrics**
   - CPU usage < 80%
   - Memory usage < 80%
   - Disk usage < 85%
   - Network latency < 50ms

2. **Application Metrics**
   - Scanner uptime > 99.9%
   - API response time < 500ms
   - Error rate < 0.1%
   - Signal detection rate

3. **Trading Metrics**
   - Paper trading performance
   - Signal accuracy
   - Risk management compliance
   - Portfolio tracking

### **Alert Levels**

**🔴 CRITICAL (Immediate Response Required)**
- System down
- Database unavailable
- Security breach detected
- Trading system malfunction

**🟡 WARNING (Response Within 15 Minutes)**
- High resource usage
- Slow response times
- API rate limiting
- Minor errors

**🟢 INFO (Monitor and Log)**
- Successful operations
- Performance metrics
- Backup completions
- Routine maintenance

### **Grafana Dashboard URLs**

- **System Overview**: `https://your-domain.com:3000/d/atlas-system`
- **Trading Performance**: `https://your-domain.com:3000/d/atlas-trading`
- **Scanner Metrics**: `https://your-domain.com:3000/d/atlas-scanner`
- **Security Dashboard**: `https://your-domain.com:3000/d/atlas-security`

---

## 🔧 **Routine Maintenance**

### **Weekly Tasks**

```bash
# Monday: System updates
sudo apt update && sudo apt list --upgradable
# Review and apply security updates

# Tuesday: Database maintenance
sudo -u postgres psql atlas_production -c "VACUUM ANALYZE;"

# Wednesday: Log analysis
sudo /opt/atlas/scripts/analyze_logs.sh

# Thursday: Performance review
python /opt/atlas/scripts/performance_report.py

# Friday: Security audit
sudo /opt/atlas/scripts/security_check.sh

# Weekend: Backup verification
sudo /opt/atlas/scripts/verify_backups.sh
```

### **Monthly Tasks**

1. **Security Review**
   - Update SSL certificates if needed
   - Review user access logs
   - Check for security vulnerabilities
   - Update security policies

2. **Performance Optimization**
   - Database index optimization
   - Query performance analysis
   - Resource usage trends
   - Capacity planning

3. **Backup Testing**
   - Test restore procedures
   - Verify backup integrity
   - Update disaster recovery plans
   - Document recovery times

---

## 🚨 **Incident Response**

### **Severity Levels**

**Severity 1 (Critical)**
- Complete system outage
- Data loss or corruption
- Security breach
- Trading system failure

**Severity 2 (High)**
- Partial system outage
- Performance degradation
- API failures
- Scanner malfunction

**Severity 3 (Medium)**
- Minor functionality issues
- Slow response times
- Non-critical errors
- Warning alerts

**Severity 4 (Low)**
- Cosmetic issues
- Documentation updates
- Enhancement requests
- Informational alerts

### **Response Procedures**

1. **Immediate Response (0-5 minutes)**
   - Acknowledge the incident
   - Assess severity level
   - Notify stakeholders if critical
   - Begin initial investigation

2. **Investigation (5-30 minutes)**
   - Gather system information
   - Check logs and metrics
   - Identify root cause
   - Implement temporary fixes

3. **Resolution (30 minutes - 4 hours)**
   - Implement permanent fix
   - Test the solution
   - Monitor for stability
   - Document the incident

4. **Post-Incident (Within 24 hours)**
   - Conduct post-mortem review
   - Update documentation
   - Implement preventive measures
   - Communicate lessons learned

---

## 📈 **Performance Tuning**

### **Database Optimization**

```sql
-- Weekly database maintenance
VACUUM ANALYZE;

-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Monitor database size
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### **Application Optimization**

```bash
# Monitor Python memory usage
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB')
"

# Check Gunicorn worker status
sudo systemctl status atlas
ps aux | grep gunicorn

# Monitor Redis performance
redis-cli info memory
redis-cli info stats
```

---

## 🔒 **Security Operations**

### **Daily Security Checks**

```bash
# Check failed login attempts
sudo grep "Failed password" /var/log/auth.log | tail -20

# Monitor fail2ban status
sudo fail2ban-client status
sudo fail2ban-client status atlas

# Check SSL certificate expiry
openssl x509 -in /etc/ssl/certs/atlas.crt -text -noout | grep "Not After"

# Review security logs
sudo grep -i "security\|breach\|attack" /var/log/atlas/atlas.log
```

### **Weekly Security Tasks**

1. **Access Review**
   - Review user access logs
   - Check for unauthorized access attempts
   - Verify API key usage
   - Monitor admin activities

2. **Vulnerability Assessment**
   - Run security scans
   - Check for outdated packages
   - Review security advisories
   - Update security configurations

---

## 📋 **Troubleshooting Quick Reference**

### **Common Issues & Solutions**

1. **High CPU Usage**
   ```bash
   # Identify process
   top -p $(pgrep -f atlas)
   
   # Check for runaway processes
   ps aux --sort=-%cpu | head -10
   
   # Restart if necessary
   sudo systemctl restart atlas
   ```

2. **Memory Leaks**
   ```bash
   # Monitor memory usage
   free -h
   
   # Check application memory
   ps aux --sort=-%mem | head -10
   
   # Restart application
   sudo systemctl restart atlas
   ```

3. **Database Connection Issues**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Test connection
   psql -h localhost -U atlas_user -d atlas_production
   
   # Check connection limits
   sudo -u postgres psql -c "SHOW max_connections;"
   ```

4. **Scanner Not Working**
   ```bash
   # Check scanner status
   curl https://your-domain.com/api/scanner/status
   
   # Review scanner logs
   grep "scanner" /var/log/atlas/atlas.log | tail -20
   
   # Restart scanner service
   sudo systemctl restart atlas
   ```

---

## 📞 **Emergency Contacts**

### **Escalation Matrix**

**Level 1: Operations Team**
- Response Time: 5 minutes
- Availability: 24/7 during market hours
- Contact: <EMAIL>

**Level 2: Development Team**
- Response Time: 15 minutes
- Availability: Business hours + on-call
- Contact: <EMAIL>

**Level 3: Management**
- Response Time: 30 minutes
- Availability: On-call for critical issues
- Contact: <EMAIL>

### **External Contacts**

- **Hosting Provider**: <EMAIL>
- **SSL Certificate Provider**: <EMAIL>
- **Database Support**: <EMAIL>
- **Security Team**: <EMAIL>

---

## 📚 **Documentation & Resources**

### **Internal Documentation**
- System Architecture Diagrams
- API Documentation
- Database Schema
- Security Policies
- Disaster Recovery Plans

### **External Resources**
- PostgreSQL Documentation
- Nginx Configuration Guide
- Python Best Practices
- Trading System Regulations
- Security Compliance Guidelines

---

## ✅ **Daily Operations Checklist**

**Morning (Pre-Market)**
- [ ] System status check
- [ ] Log review
- [ ] Database connectivity test
- [ ] API health check
- [ ] Scanner status verification

**During Market Hours**
- [ ] Monitor dashboards
- [ ] Respond to alerts
- [ ] Check performance metrics
- [ ] Verify trading signals
- [ ] Document incidents

**End of Day**
- [ ] Generate daily reports
- [ ] Backup trading data
- [ ] Clean temporary files
- [ ] System health check
- [ ] Update metrics

**Weekly**
- [ ] System updates
- [ ] Database maintenance
- [ ] Log analysis
- [ ] Performance review
- [ ] Security audit

**Monthly**
- [ ] Security review
- [ ] Performance optimization
- [ ] Backup testing
- [ ] Capacity planning
- [ ] Documentation updates

---

**🎯 Following these operational procedures ensures reliable, secure, and high-performance operation of the A.T.L.A.S. Trading System.**
