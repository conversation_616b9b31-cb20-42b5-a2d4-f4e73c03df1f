# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(4, 0, 0, 0),
    prodvers=(4, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'A.T.L.A.S. Trading Systems'),
        StringStruct(u'FileDescription', u'A.T.L.A.S. AI Trading System'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'ATLAS_Trading_System'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2025 A.T.L.A.S. Trading Systems'),
        StringStruct(u'OriginalFilename', u'ATLAS_Trading_System.exe'),
        StringStruct(u'ProductName', u'A.T.L.A.S. AI Trading System'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)