"""
A.T.L.A.S. v5.0 Expanded Universe Performance Verification
Tests data availability, API efficiency, and system performance with expanded stock universe
"""

import asyncio
import time
import logging
from datetime import datetime
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def verify_performance():
    """Comprehensive performance verification for expanded universe"""
    
    print("=" * 70)
    print("A.T.L.A.S. v5.0 EXPANDED UNIVERSE PERFORMANCE VERIFICATION")
    print("=" * 70)
    
    results = {
        'universe_size': 0,
        'initialization_time': 0,
        'filtering_performance': {},
        'api_efficiency': {},
        'memory_usage': {},
        'scanning_performance': {}
    }
    
    try:
        # Test 1: Universe Initialization Performance
        print("\n1. UNIVERSE INITIALIZATION PERFORMANCE")
        print("-" * 50)
        
        start_time = time.time()
        from atlas_expanded_universe import initialize_expanded_universe
        summary = await initialize_expanded_universe()
        init_time = time.time() - start_time
        
        results['universe_size'] = summary.get('total_symbols', 0)
        results['initialization_time'] = init_time
        
        print(f"✅ Universe Size: {results['universe_size']} symbols")
        print(f"✅ Initialization Time: {init_time:.2f} seconds")
        print(f"✅ Performance Rating: {'EXCELLENT' if init_time < 2 else 'GOOD' if init_time < 5 else 'ADEQUATE'}")
        
        # Test 2: Filtering Performance
        print("\n2. FILTERING PERFORMANCE")
        print("-" * 50)
        
        from atlas_expanded_universe import expanded_universe
        
        # Test various filtering operations
        filters = [
            ('High Quality (80+)', {'min_quality_score': 80}),
            ('Large Cap', {'market_cap': 'large'}),
            ('Technology Sector', {'sector': 'technology'}),
            ('High Liquidity', {'liquidity': 'high'}),
            ('Combined Filters', {'market_cap': 'large', 'min_quality_score': 75})
        ]
        
        for filter_name, criteria in filters:
            start_time = time.time()
            filtered_symbols = expanded_universe.get_symbols_by_criteria(**criteria)
            filter_time = time.time() - start_time
            
            results['filtering_performance'][filter_name] = {
                'count': len(filtered_symbols),
                'time': filter_time
            }
            
            print(f"✅ {filter_name}: {len(filtered_symbols)} symbols in {filter_time:.3f}s")
        
        # Test 3: API Efficiency Analysis
        print("\n3. API EFFICIENCY ANALYSIS")
        print("-" * 50)
        
        # Calculate theoretical API usage
        total_symbols = results['universe_size']
        
        # Simulate different scanning scenarios
        scenarios = {
            'Conservative (1 call/symbol/minute)': total_symbols,
            'Moderate (2 calls/symbol/minute)': total_symbols * 2,
            'Aggressive (3 calls/symbol/minute)': total_symbols * 3
        }
        
        # FMP Premium limits
        fmp_limit = 3000  # calls per minute
        
        for scenario, calls_per_minute in scenarios.items():
            efficiency = (calls_per_minute / fmp_limit) * 100
            status = 'EXCELLENT' if efficiency < 50 else 'GOOD' if efficiency < 80 else 'CAUTION'
            
            results['api_efficiency'][scenario] = {
                'calls_per_minute': calls_per_minute,
                'efficiency_percent': efficiency,
                'status': status
            }
            
            print(f"✅ {scenario}: {calls_per_minute} calls/min ({efficiency:.1f}% of limit) - {status}")
        
        # Test 4: Memory Usage Estimation
        print("\n4. MEMORY USAGE ANALYSIS")
        print("-" * 50)
        
        import sys
        
        # Estimate memory usage
        symbol_count = results['universe_size']
        
        # Rough estimates (in MB)
        base_memory = 10  # Base system memory
        symbol_data = symbol_count * 0.001  # ~1KB per symbol
        cache_memory = symbol_count * 0.002  # ~2KB per symbol for caching
        scanner_memory = symbol_count * 0.0005  # ~0.5KB per symbol for scanner
        
        total_estimated = base_memory + symbol_data + cache_memory + scanner_memory
        
        results['memory_usage'] = {
            'base_mb': base_memory,
            'symbol_data_mb': symbol_data,
            'cache_mb': cache_memory,
            'scanner_mb': scanner_memory,
            'total_estimated_mb': total_estimated
        }
        
        print(f"✅ Base System: {base_memory:.1f} MB")
        print(f"✅ Symbol Data: {symbol_data:.1f} MB")
        print(f"✅ Cache Memory: {cache_memory:.1f} MB")
        print(f"✅ Scanner Memory: {scanner_memory:.1f} MB")
        print(f"✅ Total Estimated: {total_estimated:.1f} MB")
        print(f"✅ Memory Status: {'EXCELLENT' if total_estimated < 50 else 'GOOD' if total_estimated < 100 else 'ADEQUATE'}")
        
        # Test 5: Scanning Performance Simulation
        print("\n5. SCANNING PERFORMANCE SIMULATION")
        print("-" * 50)
        
        from atlas_expanded_scanner_config import ExpandedScannerConfig, ScanningTier
        
        config = ExpandedScannerConfig()
        
        # Calculate scanning throughput for each tier
        tier_performance = {}
        
        for tier in ScanningTier:
            interval = config.tier_intervals[tier]
            symbols_per_hour = 3600 / interval  # Theoretical max per symbol
            
            tier_performance[tier.value] = {
                'interval_seconds': interval,
                'scans_per_hour': symbols_per_hour
            }
            
            print(f"✅ {tier.value.replace('_', ' ').title()}: {interval}s interval, {symbols_per_hour:.0f} scans/hour per symbol")
        
        results['scanning_performance'] = tier_performance
        
        # Test 6: Overall System Capacity
        print("\n6. OVERALL SYSTEM CAPACITY")
        print("-" * 50)
        
        # Calculate total system capacity
        batch_size = config.batch_config.batch_size
        batches_needed = (total_symbols + batch_size - 1) // batch_size
        batch_interval = config.batch_config.batch_interval
        
        full_scan_time = batches_needed * batch_interval
        scans_per_hour = 3600 / full_scan_time
        
        print(f"✅ Total Symbols: {total_symbols}")
        print(f"✅ Batch Size: {batch_size} symbols")
        print(f"✅ Batches Needed: {batches_needed}")
        print(f"✅ Full Scan Time: {full_scan_time:.1f} seconds")
        print(f"✅ Full Scans per Hour: {scans_per_hour:.1f}")
        print(f"✅ System Capacity: {'EXCELLENT' if full_scan_time < 120 else 'GOOD' if full_scan_time < 300 else 'ADEQUATE'}")
        
        # Final Performance Summary
        print("\n" + "=" * 70)
        print("PERFORMANCE VERIFICATION SUMMARY")
        print("=" * 70)
        
        # Overall performance score
        performance_factors = [
            ('Initialization', 'EXCELLENT' if results['initialization_time'] < 2 else 'GOOD'),
            ('API Efficiency', results['api_efficiency']['Conservative (1 call/symbol/minute)']['status']),
            ('Memory Usage', 'EXCELLENT' if results['memory_usage']['total_estimated_mb'] < 50 else 'GOOD'),
            ('Scanning Speed', 'EXCELLENT' if full_scan_time < 120 else 'GOOD')
        ]
        
        excellent_count = sum(1 for _, rating in performance_factors if rating == 'EXCELLENT')
        good_count = sum(1 for _, rating in performance_factors if rating == 'GOOD')
        
        overall_rating = 'EXCELLENT' if excellent_count >= 3 else 'GOOD' if excellent_count + good_count >= 3 else 'ADEQUATE'
        
        print(f"\n📊 PERFORMANCE BREAKDOWN:")
        for factor, rating in performance_factors:
            print(f"   • {factor}: {rating}")
        
        print(f"\n🎯 OVERALL PERFORMANCE: {overall_rating}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if results['initialization_time'] > 5:
            print("   • Consider optimizing universe initialization")
        if results['api_efficiency']['Conservative (1 call/symbol/minute)']['efficiency_percent'] > 80:
            print("   • Consider additional API keys for higher capacity")
        if results['memory_usage']['total_estimated_mb'] > 100:
            print("   • Monitor memory usage in production")
        if full_scan_time > 300:
            print("   • Consider reducing universe size or increasing batch size")
        
        print(f"\n✅ SYSTEM READY FOR PRODUCTION WITH {total_symbols} SYMBOLS!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ PERFORMANCE VERIFICATION FAILED: {e}")
        logger.error(f"Performance verification error: {e}")
        return False

async def main():
    """Main verification function"""
    try:
        success = await verify_performance()
        return 0 if success else 1
    except Exception as e:
        print(f"\n💥 Verification crashed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
