#!/usr/bin/env python3
"""
Test script to verify Atlas V5 Enhanced integration improvements
Tests API connectivity, WebSocket streaming, and data source failover
"""

import asyncio
import requests
import json
import time
import websockets
from datetime import datetime

async def test_api_connectivity_improvements():
    """Test enhanced API connectivity and error handling"""
    
    print("🔧 Testing Enhanced API Connectivity")
    print("=" * 60)
    
    base_url = "http://localhost:8002"
    
    # Test API metrics endpoint
    print("\n📊 Testing API Metrics Endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/api-metrics", timeout=10)
        if response.status_code == 200:
            data = response.json()
            metrics = data.get('api_metrics', {})
            
            print(f"✅ API Metrics Retrieved")
            print(f"   Total Endpoints: {data.get('summary', {}).get('total_endpoints', 0)}")
            print(f"   Healthy Endpoints: {data.get('summary', {}).get('healthy_endpoints', 0)}")
            
            # Show endpoint details
            for endpoint_name, endpoint_metrics in metrics.items():
                status = endpoint_metrics.get('status', 'unknown')
                success_rate = endpoint_metrics.get('success_rate', 0)
                print(f"   {endpoint_name}: {status} ({success_rate}% success)")
            
            return True
        else:
            print(f"❌ API Metrics failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Metrics test failed: {e}")
        return False

async def test_websocket_streaming():
    """Test enhanced WebSocket streaming functionality"""
    
    print("\n🌐 Testing Enhanced WebSocket Streaming")
    print("=" * 60)
    
    try:
        # Test WebSocket metrics endpoint first
        response = requests.get("http://localhost:8002/api/v1/websocket-metrics", timeout=10)
        if response.status_code == 200:
            data = response.json()
            metrics = data.get('websocket_metrics', {})
            
            print(f"✅ WebSocket Metrics Retrieved")
            print(f"   Total Connections: {metrics.get('total_connections', 0)}")
            print(f"   Active Streams: {metrics.get('active_streams', 0)}")
            print(f"   Available Stream Types: {', '.join(metrics.get('stream_types', []))}")
        
        # Test WebSocket connection
        print("\n🔌 Testing WebSocket Connection...")
        
        async def websocket_test():
            try:
                uri = "ws://localhost:8002/ws/scanner"
                async with websockets.connect(uri, timeout=10) as websocket:
                    print("✅ WebSocket connected successfully")
                    
                    # Send subscription message
                    subscribe_msg = {
                        "type": "subscribe",
                        "stream_type": "system_status",
                        "parameters": {}
                    }
                    
                    await websocket.send(json.dumps(subscribe_msg))
                    print("📡 Subscription message sent")
                    
                    # Wait for responses
                    messages_received = 0
                    start_time = time.time()
                    
                    while messages_received < 3 and time.time() - start_time < 15:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                            data = json.loads(message)
                            
                            message_type = data.get('type', 'unknown')
                            print(f"📨 Received: {message_type}")
                            
                            messages_received += 1
                            
                        except asyncio.TimeoutError:
                            print("⏱️ WebSocket message timeout")
                            break
                    
                    print(f"✅ WebSocket test completed ({messages_received} messages received)")
                    return True
                    
            except Exception as e:
                print(f"❌ WebSocket test failed: {e}")
                return False
        
        return await websocket_test()
        
    except Exception as e:
        print(f"❌ WebSocket streaming test failed: {e}")
        return False

async def test_data_source_integration():
    """Test enhanced data source integration and failover"""
    
    print("\n📊 Testing Data Source Integration")
    print("=" * 60)
    
    base_url = "http://localhost:8002"
    
    # Test various data endpoints
    test_endpoints = [
        {
            "name": "Health Check",
            "url": f"{base_url}/api/v1/health",
            "method": "GET"
        },
        {
            "name": "Lee Method Signals",
            "url": f"{base_url}/api/v1/lee_method/signals",
            "method": "GET"
        },
        {
            "name": "Market Quote (AAPL)",
            "url": f"{base_url}/api/v1/quote/AAPL",
            "method": "GET"
        },
        {
            "name": "Chat with Grok Integration",
            "url": f"{base_url}/api/v1/chat",
            "method": "POST",
            "data": {"message": "Test Grok integration", "conversation_id": "test_integration"}
        }
    ]
    
    results = []
    
    for test in test_endpoints:
        print(f"\n🔍 Testing: {test['name']}")
        
        try:
            start_time = time.time()
            
            if test['method'] == 'GET':
                response = requests.get(test['url'], timeout=20)
            elif test['method'] == 'POST':
                response = requests.post(test['url'], json=test.get('data', {}), timeout=30)
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"✅ {test['name']}: Success ({response_time:.2f}s)")
                
                # Check response content
                try:
                    data = response.json()
                    if 'error' in data:
                        print(f"⚠️  Response contains error: {data['error']}")
                    else:
                        print(f"📄 Response looks healthy")
                except:
                    print(f"📄 Non-JSON response received")
                
                results.append(True)
            else:
                print(f"❌ {test['name']}: HTTP {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {test['name']}: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 Data Source Integration Success Rate: {success_rate:.1f}%")
    
    return success_rate >= 75  # 75% success rate threshold

async def test_mobile_interface_responsiveness():
    """Test mobile-friendly interface responsiveness"""
    
    print("\n📱 Testing Mobile Interface Responsiveness")
    print("=" * 60)
    
    base_url = "http://localhost:8002"
    
    try:
        # Test main interface with mobile user agent
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        }
        
        response = requests.get(base_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for mobile-friendly elements
            mobile_checks = [
                ('viewport meta tag', 'viewport' in content),
                ('responsive design', 'responsive' in content.lower() or '@media' in content),
                ('mobile CSS', 'mobile' in content.lower()),
                ('touch-friendly', 'touch' in content.lower()),
                ('intent detection', 'intent-detection-bubble' in content)
            ]
            
            print("📱 Mobile Responsiveness Checks:")
            passed_checks = 0
            
            for check_name, passed in mobile_checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}")
                if passed:
                    passed_checks += 1
            
            success_rate = passed_checks / len(mobile_checks) * 100
            print(f"\n📊 Mobile Responsiveness: {success_rate:.1f}%")
            
            return success_rate >= 60  # 60% threshold for mobile features
        else:
            print(f"❌ Interface not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Mobile interface test failed: {e}")
        return False

async def main():
    """Run all integration improvement tests"""
    
    print("🚀 Atlas V5 Enhanced Integration Improvements Test Suite")
    print("=" * 80)
    
    tests = [
        ("API Connectivity Improvements", test_api_connectivity_improvements),
        ("WebSocket Streaming", test_websocket_streaming),
        ("Data Source Integration", test_data_source_integration),
        ("Mobile Interface Responsiveness", test_mobile_interface_responsiveness)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("=" * 80)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 INTEGRATION IMPROVEMENTS TEST RESULTS")
    print("=" * 80)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    print(f"Overall Success Rate: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    print("\n📋 Individual Test Results:")
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    # Final assessment
    print("\n" + "=" * 80)
    if passed_tests >= total_tests * 0.75:  # 75% success rate
        print("🎉 INTEGRATION IMPROVEMENTS SUCCESSFUL!")
        print("✅ Enhanced API connectivity working")
        print("✅ WebSocket streaming optimized")
        print("✅ Data source integration improved")
        print("✅ Mobile interface enhanced")
        
        print(f"\n🌐 System ready with improvements at: http://localhost:8002")
        
    else:
        print("⚠️  INTEGRATION IMPROVEMENTS NEED ATTENTION")
        print("Some integration features are not working optimally")
        print("Review the failed tests above")
    
    return passed_tests >= total_tests * 0.75

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
