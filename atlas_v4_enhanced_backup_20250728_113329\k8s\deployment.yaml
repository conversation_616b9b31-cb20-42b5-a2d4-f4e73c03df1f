apiVersion: apps/v1
kind: Deployment
metadata:
  name: atlas-multi-agent-system
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    version: v4-enhanced
    component: api
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: atlas-multi-agent-system
      component: api
  template:
    metadata:
      labels:
        app: atlas-multi-agent-system
        component: api
        version: v4-enhanced
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: atlas-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: atlas-api
        image: atlas-multi-agent-system:v4-enhanced
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8001
          protocol: TCP
        - name: metrics
          containerPort: 8000
          protocol: TCP
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: atlas-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: atlas-config
              key: LOG_LEVEL
        - name: GROK_API_KEY
          valueFrom:
            secretKeyRef:
              name: atlas-api-keys
              key: GROK_API_KEY
        - name: ALPACA_API_KEY
          valueFrom:
            secretKeyRef:
              name: atlas-api-keys
              key: ALPACA_API_KEY
        - name: ALPACA_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: atlas-api-keys
              key: ALPACA_SECRET_KEY
        - name: FMP_API_KEY
          valueFrom:
            secretKeyRef:
              name: atlas-api-keys
              key: FMP_API_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: atlas-api-keys
              key: JWT_SECRET_KEY
        envFrom:
        - configMapRef:
            name: atlas-config
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /api/v1/monitoring/health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/monitoring/health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/v1/monitoring/health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: audit-logs
          mountPath: /app/audit_logs
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      - name: nginx-sidecar
        image: nginx:1.25-alpine
        ports:
        - name: nginx
          containerPort: 80
          protocol: TCP
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
          readOnly: true
        - name: nginx-cache
          mountPath: /var/cache/nginx
        - name: nginx-run
          mountPath: /var/run
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
            add:
            - NET_BIND_SERVICE
      volumes:
      - name: logs
        emptyDir:
          sizeLimit: 1Gi
      - name: config
        configMap:
          name: atlas-config
      - name: nginx-config
        configMap:
          name: atlas-nginx-config
      - name: nginx-cache
        emptyDir:
          sizeLimit: 100Mi
      - name: nginx-run
        emptyDir:
          sizeLimit: 10Mi
      - name: audit-logs
        persistentVolumeClaim:
          claimName: atlas-audit-logs-pvc
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "atlas-trading"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - atlas-multi-agent-system
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: atlas-audit-logs-pvc
  namespace: atlas-trading
  labels:
    app: atlas-multi-agent-system
    component: storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
