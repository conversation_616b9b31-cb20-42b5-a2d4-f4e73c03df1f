#!/usr/bin/env python3
"""
A.T.L.A.S. v5.0 Connection Diagnostic Tool
Comprehensive diagnostic of terminal-to-web interface communication
"""

import asyncio
import aiohttp
import websockets
import json
import logging
from datetime import datetime
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AtlasConnectionDiagnostic:
    """Comprehensive diagnostic tool for A.T.L.A.S. connections"""
    
    def __init__(self, base_url="http://localhost:8002"):
        self.base_url = base_url
        self.ws_url = f"ws://localhost:8002/ws/scanner"
        self.session = None
        self.issues_found = []
        self.recommendations = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_issue(self, severity, component, description, recommendation=None):
        """Log an issue found during diagnostics"""
        issue = {
            "severity": severity,
            "component": component,
            "description": description,
            "timestamp": datetime.now().isoformat()
        }
        self.issues_found.append(issue)
        
        if recommendation:
            self.recommendations.append(recommendation)
        
        severity_icon = {"CRITICAL": "🚨", "HIGH": "⚠️", "MEDIUM": "⚡", "LOW": "ℹ️"}
        logger.warning(f"{severity_icon.get(severity, '❓')} {severity}: {component} - {description}")
    
    async def diagnose_server_connectivity(self):
        """Diagnose basic server connectivity"""
        logger.info("🔍 Diagnosing server connectivity...")
        
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health", timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Server responding on port 8002")
                    logger.info(f"✅ Server status: {data.get('status', 'unknown')}")
                    logger.info(f"✅ Uptime: {data.get('uptime_seconds', 0):.1f} seconds")
                    return True
                else:
                    self.log_issue("CRITICAL", "Server", f"Health endpoint returned {response.status}")
                    return False
        except Exception as e:
            self.log_issue("CRITICAL", "Server", f"Cannot connect to server: {e}")
            return False
    
    async def diagnose_api_endpoints(self):
        """Diagnose all critical API endpoints"""
        logger.info("🔍 Diagnosing API endpoints...")
        
        endpoints = [
            ("/api/v1/health", "GET", "Health Check"),
            ("/api/v1/lee_method/signals", "GET", "Lee Method Signals"),
            ("/api/v1/lee_method/stats", "GET", "Lee Method Stats"),
            ("/api/v1/trading/positions", "GET", "Trading Positions"),
            ("/api/v1/websocket-metrics", "GET", "WebSocket Metrics"),
            ("/api/v1/chat", "POST", "Chat Interface")
        ]
        
        working_endpoints = 0
        
        for endpoint, method, description in endpoints:
            try:
                if method == "GET":
                    async with self.session.get(f"{self.base_url}{endpoint}", timeout=10) as response:
                        if response.status == 200:
                            logger.info(f"✅ {description}: Working")
                            working_endpoints += 1
                        else:
                            self.log_issue("HIGH", "API", f"{description} returned {response.status}")
                elif method == "POST":
                    test_data = {"message": "diagnostic test", "session_id": "diagnostic"}
                    async with self.session.post(f"{self.base_url}{endpoint}", json=test_data, timeout=15) as response:
                        if response.status == 200:
                            logger.info(f"✅ {description}: Working")
                            working_endpoints += 1
                        else:
                            self.log_issue("HIGH", "API", f"{description} returned {response.status}")
            except Exception as e:
                self.log_issue("HIGH", "API", f"{description} failed: {e}")
        
        logger.info(f"📊 API Endpoints: {working_endpoints}/{len(endpoints)} working")
        return working_endpoints == len(endpoints)
    
    async def diagnose_websocket_connection(self):
        """Diagnose WebSocket connectivity"""
        logger.info("🔍 Diagnosing WebSocket connection...")
        
        try:
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                logger.info("✅ WebSocket connection established")
                
                # Test message sending
                test_message = {"type": "diagnostic_test", "timestamp": datetime.now().isoformat()}
                await websocket.send(json.dumps(test_message))
                logger.info("✅ WebSocket message sent")
                
                # Test message receiving (with timeout)
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    data = json.loads(message)
                    logger.info(f"✅ WebSocket message received: {data.get('type', 'unknown')}")
                    return True
                except asyncio.TimeoutError:
                    logger.info("⚠️ No WebSocket message received (may be normal)")
                    return True  # Connection works, just no immediate response
                    
        except Exception as e:
            self.log_issue("HIGH", "WebSocket", f"Connection failed: {e}")
            return False
    
    async def diagnose_real_data_integrity(self):
        """Diagnose that real data (not mock) is being used"""
        logger.info("🔍 Diagnosing real data integrity...")
        
        try:
            # Check trading positions for real Alpaca data
            async with self.session.get(f"{self.base_url}/api/v1/trading/positions") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == 'live_data':
                        logger.info("✅ Trading positions using live data")
                    else:
                        self.log_issue("MEDIUM", "Data", "Trading positions not marked as live data")
                
            # Check for mock data indicators
            text = await response.text()
            mock_indicators = ["mock", "simulated", "fake", "demo"]
            found_mock = any(indicator in text.lower() for indicator in mock_indicators)
            
            if found_mock:
                self.log_issue("CRITICAL", "Data", "Mock data indicators found in API responses")
                return False
            else:
                logger.info("✅ No mock data indicators found")
                return True
                
        except Exception as e:
            self.log_issue("HIGH", "Data", f"Data integrity check failed: {e}")
            return False
    
    async def diagnose_performance(self):
        """Diagnose system performance"""
        logger.info("🔍 Diagnosing system performance...")
        
        try:
            # Test response times
            start_time = time.time()
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                response_time = (time.time() - start_time) * 1000
                
                if response_time < 1000:  # Less than 1 second
                    logger.info(f"✅ Response time: {response_time:.0f}ms (Good)")
                elif response_time < 3000:  # Less than 3 seconds
                    logger.info(f"⚠️ Response time: {response_time:.0f}ms (Acceptable)")
                    self.log_issue("LOW", "Performance", f"Slow response time: {response_time:.0f}ms")
                else:
                    self.log_issue("MEDIUM", "Performance", f"Very slow response time: {response_time:.0f}ms")
                
                return response_time < 5000  # Fail if over 5 seconds
                
        except Exception as e:
            self.log_issue("HIGH", "Performance", f"Performance test failed: {e}")
            return False
    
    async def run_comprehensive_diagnostic(self):
        """Run comprehensive diagnostic of all systems"""
        logger.info("🚀 Starting A.T.L.A.S. v5.0 Connection Diagnostic")
        logger.info("=" * 60)
        
        diagnostics = [
            ("Server Connectivity", self.diagnose_server_connectivity),
            ("API Endpoints", self.diagnose_api_endpoints),
            ("WebSocket Connection", self.diagnose_websocket_connection),
            ("Real Data Integrity", self.diagnose_real_data_integrity),
            ("System Performance", self.diagnose_performance)
        ]
        
        results = {}
        
        for diagnostic_name, diagnostic_func in diagnostics:
            logger.info(f"\n📋 Running: {diagnostic_name}")
            try:
                result = await diagnostic_func()
                results[diagnostic_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"{status}: {diagnostic_name}")
            except Exception as e:
                logger.error(f"❌ ERROR in {diagnostic_name}: {e}")
                results[diagnostic_name] = False
        
        # Generate diagnostic report
        await self.generate_diagnostic_report(results)
        
        # Overall assessment
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        logger.info("\n" + "=" * 60)
        logger.info("📊 DIAGNOSTIC SUMMARY")
        logger.info("=" * 60)
        
        for diagnostic_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {diagnostic_name}: {status}")
        
        logger.info(f"\n🎯 Overall Result: {passed}/{total} diagnostics passed")
        
        if len(self.issues_found) == 0:
            logger.info("🎉 NO ISSUES FOUND - System is operating perfectly!")
            return True
        else:
            logger.warning(f"⚠️ {len(self.issues_found)} issues found - see diagnostic report")
            return passed >= total * 0.8  # 80% pass rate acceptable
    
    async def generate_diagnostic_report(self, results):
        """Generate detailed diagnostic report"""
        if len(self.issues_found) > 0:
            logger.info("\n🔧 ISSUES FOUND:")
            for issue in self.issues_found:
                logger.info(f"   {issue['severity']}: {issue['component']} - {issue['description']}")
        
        if len(self.recommendations) > 0:
            logger.info("\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(self.recommendations, 1):
                logger.info(f"   {i}. {rec}")

async def main():
    """Main diagnostic function"""
    async with AtlasConnectionDiagnostic() as diagnostic:
        success = await diagnostic.run_comprehensive_diagnostic()
        return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
