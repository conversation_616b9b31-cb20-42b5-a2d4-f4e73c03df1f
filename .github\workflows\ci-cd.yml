name: A.T.L.A.S. Institutional CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 6 * * *'  # Daily institutional testing at 6 AM

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: atlas-trading-system

jobs:
  # ===== CODE QUALITY & SECURITY =====
  code-quality:
    name: 🔍 Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
          
      - name: Install dependencies
        run: |
          pip install --upgrade pip
          pip install -r requirements.txt
          pip install black flake8 mypy bandit safety pytest-cov
          
      - name: Code formatting check
        run: black --check --diff .
        
      - name: Linting
        run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        
      - name: Type checking
        run: mypy . --ignore-missing-imports
        
      - name: Security scan
        run: |
          bandit -r . -f json -o security-report.json
          safety check --json --output safety-report.json
          
      - name: Upload security reports
        uses: actions/upload-artifact@v3
        with:
          name: security-reports
          path: |
            security-report.json
            safety-report.json

  # ===== UNIT & INTEGRATION TESTS =====
  test-suite:
    name: 🧪 Comprehensive Test Suite
    runs-on: ubuntu-latest
    needs: code-quality
    strategy:
      matrix:
        test-group: [core, institutional, performance, integration]
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: atlas_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
          
      - name: Install dependencies
        run: |
          pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio pytest-mock
          
      - name: Setup test environment
        run: |
          cp config/test.env .env
          python setup_test_db.py
          
      - name: Run ${{ matrix.test-group }} tests
        run: |
          case "${{ matrix.test-group }}" in
            "core")
              pytest tests/core/ -v --cov=./ --cov-report=xml --cov-report=html
              ;;
            "institutional")
              pytest tests/institutional/ -v --cov=./ --cov-report=xml
              ;;
            "performance")
              pytest tests/performance/ -v --benchmark-only
              ;;
            "integration")
              pytest tests/integration/ -v --slow
              ;;
          esac
          
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: ${{ matrix.test-group }}
          name: codecov-${{ matrix.test-group }}

  # ===== DOCKER BUILD & SECURITY SCAN =====
  docker-build:
    name: 🐳 Docker Build & Security Scan
    runs-on: ubuntu-latest
    needs: test-suite
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
            
      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}:latest
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # ===== PERFORMANCE & LOAD TESTING =====
  performance-testing:
    name: ⚡ Performance & Load Testing
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js for Artillery
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install Artillery
        run: npm install -g artillery@latest
        
      - name: Start test environment
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Wait for services to be ready
          
      - name: Run load tests
        run: |
          artillery run tests/load/api-load-test.yml --output load-test-report.json
          artillery report load-test-report.json --output load-test-report.html
          
      - name: Upload performance reports
        uses: actions/upload-artifact@v3
        with:
          name: performance-reports
          path: |
            load-test-report.json
            load-test-report.html
            
      - name: Cleanup test environment
        run: docker-compose -f docker-compose.test.yml down

  # ===== DEPLOYMENT TO STAGING =====
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker-build, performance-testing]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name atlas-staging-cluster
        
      - name: Deploy to staging
        run: |
          envsubst < k8s/staging/deployment.yml | kubectl apply -f -
          kubectl rollout status deployment/atlas-trading-system -n staging
          
      - name: Run smoke tests
        run: |
          kubectl wait --for=condition=ready pod -l app=atlas -n staging --timeout=300s
          python tests/smoke/staging_smoke_tests.py

  # ===== DEPLOYMENT TO PRODUCTION =====
  deploy-production:
    name: 🏛️ Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker-build, performance-testing]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name atlas-production-cluster
        
      - name: Blue-Green Deployment
        run: |
          # Deploy to green environment
          envsubst < k8s/production/deployment-green.yml | kubectl apply -f -
          kubectl rollout status deployment/atlas-trading-system-green -n production
          
          # Run production smoke tests
          python tests/smoke/production_smoke_tests.py
          
          # Switch traffic to green
          kubectl patch service atlas-trading-system -n production -p '{"spec":{"selector":{"version":"green"}}}'
          
          # Wait and verify
          sleep 60
          python tests/smoke/production_verification.py
          
          # Clean up blue environment
          kubectl delete deployment atlas-trading-system-blue -n production --ignore-not-found=true

  # ===== INSTITUTIONAL COMPLIANCE TESTING =====
  compliance-testing:
    name: 📋 Institutional Compliance Testing
    runs-on: ubuntu-latest
    needs: deploy-production
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Run compliance tests
        run: |
          python tests/compliance/finra_compliance_tests.py
          python tests/compliance/sec_compliance_tests.py
          python tests/compliance/audit_trail_tests.py
          
      - name: Generate compliance report
        run: python scripts/generate_compliance_report.py
        
      - name: Upload compliance reports
        uses: actions/upload-artifact@v3
        with:
          name: compliance-reports
          path: reports/compliance/

  # ===== NOTIFICATION =====
  notify:
    name: 📢 Deployment Notification
    runs-on: ubuntu-latest
    needs: [deploy-production, compliance-testing]
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#atlas-deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
