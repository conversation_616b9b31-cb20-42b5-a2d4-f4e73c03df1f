"""
A.T.L.A.S. Multi-Channel Alert Delivery System
Handles delivery of alerts through desktop, email, WebSocket, SMS, and webhook channels
"""

import asyncio
import logging
import json
import os
import platform
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Any

# Optional imports with fallbacks
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False

try:
    import aiohttp
    HTTP_AVAILABLE = True
except ImportError:
    HTTP_AVAILABLE = False

try:
    import websockets
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
from atlas_alert_engine import AlertSignal, AlertChannel, AlertPriority

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DesktopNotificationHandler:
    """Handle in-app notifications only (no OS-level notifications)"""

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.DesktopNotificationHandler")
        self.system = platform.system().lower()
        self.logger.info(f"[DESKTOP] In-app notification handler initialized (OS notifications disabled)")

    async def send_notification(self, alert_signal: AlertSignal):
        """Send in-app notification only (no OS-level notifications)"""
        try:
            title = self._format_title(alert_signal)
            message = self._format_message(alert_signal)

            # Only log the notification - no OS-level notifications
            self.logger.info(f"[IN-APP] Alert ready for WebSocket delivery: {alert_signal.symbol}")
            self.logger.info(f"[IN-APP] Title: {title}")
            self.logger.info(f"[IN-APP] Message: {message}")

            # The actual in-app notification will be handled by WebSocketAlertHandler

        except Exception as e:
            self.logger.error(f"[IN-APP] Error preparing notification: {e}")
    
    def _format_title(self, alert_signal: AlertSignal) -> str:
        """Format notification title"""
        priority_text = {
            AlertPriority.CRITICAL: "[CRITICAL]",
            AlertPriority.HIGH: "[HIGH]",
            AlertPriority.MEDIUM: "[MEDIUM]",
            AlertPriority.LOW: "[INFO]"
        }

        prefix = priority_text.get(alert_signal.priority, "[ALERT]")
        return f"{prefix} A.T.L.A.S. Alert: {alert_signal.symbol}"
    
    def _format_message(self, alert_signal: AlertSignal) -> str:
        """Format notification message"""
        signal_descriptions = {
            "active_decline_reversal_opportunity": "Active Decline Reversal",
            "moderate_decline_opportunity": "Moderate Decline",
            "bullish_momentum": "Bullish Momentum",
            "bearish_momentum": "Bearish Momentum"
        }
        
        signal_desc = signal_descriptions.get(alert_signal.signal_type.value, alert_signal.signal_type.value)
        
        message = f"""Signal: {signal_desc}
Price: ${alert_signal.current_price:.2f}
Change: {alert_signal.percentage_change:+.2f}%
Confidence: {alert_signal.confidence:.1%}
TTM Squeeze: {alert_signal.ttm_squeeze_status}
Timeframe: {alert_signal.timeframe}"""
        
        return message
    
    # OS-level notification methods removed - using in-app notifications only

class EmailAlertHandler:
    """Handle email alert delivery"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.EmailAlertHandler")
        
        # Email configuration from environment variables
        self.smtp_server = os.getenv('ATLAS_SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('ATLAS_SMTP_PORT', '587'))
        self.email_user = os.getenv('ATLAS_EMAIL_USER', '')
        self.email_password = os.getenv('ATLAS_EMAIL_PASSWORD', '')
        self.recipient_emails = os.getenv('ATLAS_ALERT_EMAILS', '').split(',')
        
        self.enabled = bool(self.email_user and self.email_password and self.recipient_emails[0])
        
        if self.enabled:
            self.logger.info(f"[EMAIL] Email handler initialized - Recipients: {len(self.recipient_emails)}")
        else:
            self.logger.warning("[EMAIL] Email handler disabled - Missing configuration")
    
    async def send_alert(self, alert_signal: AlertSignal):
        """Send email alert"""
        if not self.enabled:
            return
        
        try:
            subject = self._format_subject(alert_signal)
            body = self._format_body(alert_signal)
            
            # Send email in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, 
                self._send_email_sync, 
                subject, 
                body, 
                alert_signal.priority
            )
            
            self.logger.info(f"[EMAIL] Alert sent for {alert_signal.symbol}")
            
        except Exception as e:
            self.logger.error(f"[EMAIL] Error sending email: {e}")
    
    def _format_subject(self, alert_signal: AlertSignal) -> str:
        """Format email subject"""
        priority_prefix = {
            AlertPriority.CRITICAL: "[CRITICAL]",
            AlertPriority.HIGH: "[HIGH]",
            AlertPriority.MEDIUM: "[MEDIUM]",
            AlertPriority.LOW: "[INFO]"
        }
        
        prefix = priority_prefix.get(alert_signal.priority, "[ALERT]")
        return f"{prefix} A.T.L.A.S. Signal: {alert_signal.symbol} - {alert_signal.signal_type.value.replace('_', ' ').title()}"
    
    def _format_body(self, alert_signal: AlertSignal) -> str:
        """Format email body with HTML"""
        priority_colors = {
            AlertPriority.CRITICAL: "#FF4444",
            AlertPriority.HIGH: "#FF8800",
            AlertPriority.MEDIUM: "#4488FF",
            AlertPriority.LOW: "#888888"
        }
        
        color = priority_colors.get(alert_signal.priority, "#000000")
        
        html_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <div style="border-left: 4px solid {color}; padding-left: 20px;">
                <h2 style="color: {color}; margin-top: 0;">A.T.L.A.S. Trading Alert</h2>
                
                <table style="border-collapse: collapse; width: 100%; max-width: 600px;">
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Symbol:</td>
                        <td style="padding: 8px; font-size: 18px; font-weight: bold;">{alert_signal.symbol}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Signal Type:</td>
                        <td style="padding: 8px;">{alert_signal.signal_type.value.replace('_', ' ').title()}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Priority:</td>
                        <td style="padding: 8px; color: {color}; font-weight: bold;">{alert_signal.priority.value.upper()}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Current Price:</td>
                        <td style="padding: 8px;">${alert_signal.current_price:.2f}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Price Change:</td>
                        <td style="padding: 8px; color: {'green' if alert_signal.percentage_change > 0 else 'red'};">
                            {alert_signal.percentage_change:+.2f}%
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Confidence:</td>
                        <td style="padding: 8px;">{alert_signal.confidence:.1%}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">TTM Squeeze:</td>
                        <td style="padding: 8px;">{alert_signal.ttm_squeeze_status}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Timeframe:</td>
                        <td style="padding: 8px;">{alert_signal.timeframe}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Scanner Tier:</td>
                        <td style="padding: 8px;">{alert_signal.scanner_tier}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Consecutive Bars:</td>
                        <td style="padding: 8px;">{alert_signal.consecutive_bars}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; background-color: #f5f5f5;">Timestamp:</td>
                        <td style="padding: 8px;">{alert_signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</td>
                    </tr>
                </table>
                
                <div style="margin-top: 20px; padding: 15px; background-color: #f0f8ff; border-radius: 5px;">
                    <h3 style="margin-top: 0; color: #2c5aa0;">Quick Actions</h3>
                    <p>
                        <a href="http://localhost:8002/analysis/{alert_signal.symbol}" 
                           style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">
                            View Analysis
                        </a>
                        <a href="http://localhost:8002/chart/{alert_signal.symbol}" 
                           style="background-color: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                            View Chart
                        </a>
                    </p>
                </div>
                
                <div style="margin-top: 20px; font-size: 12px; color: #666;">
                    <p>This alert was generated by the A.T.L.A.S. Real-Time Scanner.</p>
                    <p>Alert ID: {alert_signal.id}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_body
    
    def _send_email_sync(self, subject: str, body: str, priority: AlertPriority):
        """Send email synchronously (called from thread pool)"""
        try:
            # Create message
            msg = MimeMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.email_user
            msg['To'] = ', '.join(self.recipient_emails)
            
            # Set priority headers
            if priority == AlertPriority.CRITICAL:
                msg['X-Priority'] = '1'
                msg['X-MSMail-Priority'] = 'High'
            elif priority == AlertPriority.HIGH:
                msg['X-Priority'] = '2'
                msg['X-MSMail-Priority'] = 'High'
            
            # Attach HTML body
            html_part = MimeText(body, 'html')
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.email_user, self.email_password)
                server.send_message(msg)
            
        except Exception as e:
            self.logger.error(f"[EMAIL] SMTP error: {e}")

class WebSocketAlertHandler:
    """Handle WebSocket real-time alert delivery"""
    
    def __init__(self, alert_engine):
        self.logger = logging.getLogger(f"{__name__}.WebSocketAlertHandler")
        self.alert_engine = alert_engine
        self.logger.info("[WEBSOCKET] WebSocket alert handler initialized")
    
    async def send_alert(self, alert_signal: AlertSignal):
        """Send alert to all connected WebSocket clients"""
        if not self.alert_engine.websocket_connections:
            return
        
        try:
            # Format alert data for WebSocket
            alert_data = {
                'type': 'lee_method_alert',
                'data': alert_signal.to_dict(),
                'timestamp': datetime.now().isoformat()
            }
            
            message = json.dumps(alert_data)
            
            # Send to all connected clients
            disconnected_clients = set()
            
            for websocket in self.alert_engine.websocket_connections.copy():
                try:
                    await websocket.send(message)
                except websockets.exceptions.ConnectionClosed:
                    disconnected_clients.add(websocket)
                except Exception as e:
                    self.logger.error(f"[WEBSOCKET] Error sending to client: {e}")
                    disconnected_clients.add(websocket)
            
            # Remove disconnected clients
            for websocket in disconnected_clients:
                self.alert_engine.websocket_connections.discard(websocket)
            
            active_connections = len(self.alert_engine.websocket_connections)
            self.logger.info(f"[WEBSOCKET] Alert sent to {active_connections} clients for {alert_signal.symbol}")
            
        except Exception as e:
            self.logger.error(f"[WEBSOCKET] Error sending WebSocket alert: {e}")

class SMSAlertHandler:
    """Handle SMS alert delivery (optional)"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.SMSAlertHandler")
        
        # SMS configuration (using Twilio as example)
        self.account_sid = os.getenv('TWILIO_ACCOUNT_SID', '')
        self.auth_token = os.getenv('TWILIO_AUTH_TOKEN', '')
        self.from_number = os.getenv('TWILIO_FROM_NUMBER', '')
        self.to_numbers = os.getenv('ATLAS_SMS_NUMBERS', '').split(',')
        
        self.enabled = bool(self.account_sid and self.auth_token and self.from_number and self.to_numbers[0])
        
        if self.enabled:
            self.logger.info(f"[SMS] SMS handler initialized - Recipients: {len(self.to_numbers)}")
        else:
            self.logger.info("[SMS] SMS handler disabled - Missing Twilio configuration")
    
    async def send_alert(self, alert_signal: AlertSignal):
        """Send SMS alert for high-priority signals only"""
        if not self.enabled or alert_signal.priority not in [AlertPriority.CRITICAL, AlertPriority.HIGH]:
            return
        
        try:
            message = self._format_sms_message(alert_signal)
            
            # Send SMS in thread pool
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._send_sms_sync, message)
            
            self.logger.info(f"[SMS] Alert sent for {alert_signal.symbol}")
            
        except Exception as e:
            self.logger.error(f"[SMS] Error sending SMS: {e}")
    
    def _format_sms_message(self, alert_signal: AlertSignal) -> str:
        """Format SMS message (keep it short)"""
        return f"[ALERT] A.T.L.A.S.: {alert_signal.symbol} - {alert_signal.signal_type.value.replace('_', ' ').title()} - ${alert_signal.current_price:.2f} ({alert_signal.percentage_change:+.1f}%) - Confidence: {alert_signal.confidence:.0%}"
    
    def _send_sms_sync(self, message: str):
        """Send SMS synchronously (called from thread pool)"""
        try:
            from twilio.rest import Client
            
            client = Client(self.account_sid, self.auth_token)
            
            for to_number in self.to_numbers:
                if to_number.strip():
                    client.messages.create(
                        body=message,
                        from_=self.from_number,
                        to=to_number.strip()
                    )
            
        except ImportError:
            self.logger.error("[SMS] Twilio library not installed. Install with: pip install twilio")
        except Exception as e:
            self.logger.error(f"[SMS] Twilio error: {e}")

class AlertDeliveryManager:
    """Manages all alert delivery channels"""
    
    def __init__(self, alert_engine):
        self.logger = logging.getLogger(f"{__name__}.AlertDeliveryManager")
        self.alert_engine = alert_engine
        
        # Initialize handlers
        self.desktop_handler = DesktopNotificationHandler()
        self.email_handler = EmailAlertHandler()
        self.websocket_handler = WebSocketAlertHandler(alert_engine)
        self.sms_handler = SMSAlertHandler()
        
        # Register handlers with alert engine
        self._register_handlers()
        
        self.logger.info("[DELIVERY] Alert delivery manager initialized")
    
    def _register_handlers(self):
        """Register all handlers with the alert engine"""
        self.alert_engine.register_channel_handler(
            AlertChannel.DESKTOP, 
            self.desktop_handler.send_notification
        )
        
        self.alert_engine.register_channel_handler(
            AlertChannel.EMAIL, 
            self.email_handler.send_alert
        )
        
        self.alert_engine.register_channel_handler(
            AlertChannel.WEBSOCKET, 
            self.websocket_handler.send_alert
        )
        
        self.alert_engine.register_channel_handler(
            AlertChannel.SMS, 
            self.sms_handler.send_alert
        )
    
    async def test_all_channels(self):
        """Test all delivery channels with a sample alert"""
        from atlas_alert_engine import SignalType
        
        test_alert = AlertSignal(
            id="test-alert-123",
            symbol="TEST",
            signal_type=SignalType.ACTIVE_DECLINE_REVERSAL,
            priority=AlertPriority.HIGH,
            confidence=0.85,
            timeframe="1day",
            current_price=100.50,
            percentage_change=-2.5,
            ttm_squeeze_status="squeeze_released",
            consecutive_bars=3,
            timestamp=datetime.now(),
            scanner_tier="Ultra Priority",
            additional_data={}
        )
        
        self.logger.info("[DELIVERY] Testing all alert channels...")
        
        # Test each channel
        await self.desktop_handler.send_notification(test_alert)
        await self.email_handler.send_alert(test_alert)
        await self.websocket_handler.send_alert(test_alert)
        await self.sms_handler.send_alert(test_alert)
        
        self.logger.info("[DELIVERY] Channel testing completed")
