"""
A.T.L.A.S. Secrets Manager
Secure management of API keys and sensitive configuration data
"""

import os
import json
import logging
import base64
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import threading

# Cryptography imports with fallback
try:
    from cryptography.fernet import <PERSON>rne<PERSON>
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class SecretMetadata:
    """Metadata for stored secrets"""
    name: str
    created_at: datetime
    last_accessed: datetime
    access_count: int
    encrypted: bool
    source: str = "manual"

class SecretEncryption:
    """Handles encryption/decryption of secrets"""
    
    def __init__(self, password: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.encryption_available = CRYPTOGRAPHY_AVAILABLE
        
        if not self.encryption_available:
            self.logger.warning("[SECRETS] Cryptography library not available - using base64 encoding")
            return
        
        # Generate or use provided password
        if not password:
            password = self._generate_default_password()
        
        # Derive key from password
        self.key = self._derive_key(password)
        self.cipher = Fernet(self.key)
    
    def _generate_default_password(self) -> str:
        """Generate a default password based on system info"""
        # Use a combination of system-specific data
        import platform
        system_info = f"{platform.node()}{platform.system()}{platform.release()}"
        return hashlib.sha256(system_info.encode()).hexdigest()[:32]
    
    def _derive_key(self, password: str) -> bytes:
        """Derive encryption key from password"""
        password_bytes = password.encode()
        salt = b'atlas_trading_system_salt_2024'  # Fixed salt for consistency
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            if not self.encryption_available:
                # Fallback to base64 encoding (not secure, but better than plain text)
                return base64.b64encode(data.encode()).decode()
            
            encrypted_data = self.cipher.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
            
        except Exception as e:
            self.logger.error(f"[SECRETS] Encryption failed: {e}")
            return data  # Return original data if encryption fails
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            if not self.encryption_available:
                # Fallback base64 decoding
                return base64.b64decode(encrypted_data.encode()).decode()
            
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher.decrypt(encrypted_bytes)
            return decrypted_data.decode()
            
        except Exception as e:
            self.logger.error(f"[SECRETS] Decryption failed: {e}")
            return encrypted_data  # Return original data if decryption fails

class AtlasSecretsManager:
    """Secure secrets management for Atlas trading system"""
    
    def __init__(self, secrets_file: str = "atlas_secrets.json"):
        self.secrets_file = Path(secrets_file)
        self.secrets: Dict[str, str] = {}
        self.metadata: Dict[str, SecretMetadata] = {}
        self.encryption = SecretEncryption()
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # Load existing secrets
        self._load_secrets()
        
        # Initialize with environment variables
        self._load_from_environment()
    
    def _load_secrets(self):
        """Load secrets from encrypted file"""
        try:
            if not self.secrets_file.exists():
                self.logger.info("[SECRETS] No existing secrets file found")
                return
            
            with open(self.secrets_file, 'r') as f:
                data = json.load(f)
            
            # Load secrets
            encrypted_secrets = data.get('secrets', {})
            for name, encrypted_value in encrypted_secrets.items():
                try:
                    decrypted_value = self.encryption.decrypt(encrypted_value)
                    self.secrets[name] = decrypted_value
                except Exception as e:
                    self.logger.error(f"[SECRETS] Failed to decrypt {name}: {e}")
            
            # Load metadata
            metadata_dict = data.get('metadata', {})
            for name, meta_data in metadata_dict.items():
                self.metadata[name] = SecretMetadata(
                    name=meta_data['name'],
                    created_at=datetime.fromisoformat(meta_data['created_at']),
                    last_accessed=datetime.fromisoformat(meta_data['last_accessed']),
                    access_count=meta_data['access_count'],
                    encrypted=meta_data['encrypted'],
                    source=meta_data.get('source', 'file')
                )
            
            self.logger.info(f"[SECRETS] Loaded {len(self.secrets)} secrets from file")
            
        except Exception as e:
            self.logger.error(f"[SECRETS] Failed to load secrets: {e}")
    
    def _save_secrets(self):
        """Save secrets to encrypted file"""
        try:
            # Encrypt secrets
            encrypted_secrets = {}
            for name, value in self.secrets.items():
                encrypted_secrets[name] = self.encryption.encrypt(value)
            
            # Prepare metadata
            metadata_dict = {}
            for name, metadata in self.metadata.items():
                metadata_dict[name] = {
                    'name': metadata.name,
                    'created_at': metadata.created_at.isoformat(),
                    'last_accessed': metadata.last_accessed.isoformat(),
                    'access_count': metadata.access_count,
                    'encrypted': metadata.encrypted,
                    'source': metadata.source
                }
            
            # Save to file
            data = {
                'secrets': encrypted_secrets,
                'metadata': metadata_dict,
                'version': '1.0',
                'created_at': datetime.now().isoformat()
            }
            
            with open(self.secrets_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.info(f"[SECRETS] Saved {len(self.secrets)} secrets to file")
            
        except Exception as e:
            self.logger.error(f"[SECRETS] Failed to save secrets: {e}")
    
    def _load_from_environment(self):
        """Load API keys from environment variables"""
        env_keys = [
            'FMP_API_KEY',
            'ALPACA_API_KEY',
            'ALPACA_SECRET_KEY',
            'GROK_API_KEY',
            'OPENAI_API_KEY',
            'POLYGON_API_KEY',
            'ALPHA_VANTAGE_API_KEY'
        ]
        
        for key in env_keys:
            value = os.getenv(key)
            if value and key not in self.secrets:
                self.set_secret(key.lower(), value, source="environment")
    
    def set_secret(self, name: str, value: str, source: str = "manual") -> bool:
        """Store a secret securely"""
        try:
            with self.lock:
                self.secrets[name] = value
                
                # Update metadata
                now = datetime.now()
                if name in self.metadata:
                    self.metadata[name].last_accessed = now
                    self.metadata[name].access_count += 1
                else:
                    self.metadata[name] = SecretMetadata(
                        name=name,
                        created_at=now,
                        last_accessed=now,
                        access_count=1,
                        encrypted=True,
                        source=source
                    )
                
                # Save to file
                self._save_secrets()
                
                self.logger.info(f"[SECRETS] Secret '{name}' stored successfully")
                return True
                
        except Exception as e:
            self.logger.error(f"[SECRETS] Failed to store secret '{name}': {e}")
            return False
    
    def get_secret(self, name: str) -> Optional[str]:
        """Retrieve a secret"""
        try:
            with self.lock:
                if name not in self.secrets:
                    # Try alternative names
                    alt_names = [
                        name.upper(),
                        name.lower(),
                        f"{name}_api_key",
                        f"{name.upper()}_API_KEY"
                    ]
                    
                    for alt_name in alt_names:
                        if alt_name in self.secrets:
                            name = alt_name
                            break
                    else:
                        return None
                
                # Update access metadata
                if name in self.metadata:
                    self.metadata[name].last_accessed = datetime.now()
                    self.metadata[name].access_count += 1
                
                return self.secrets[name]
                
        except Exception as e:
            self.logger.error(f"[SECRETS] Failed to retrieve secret '{name}': {e}")
            return None
    
    def delete_secret(self, name: str) -> bool:
        """Delete a secret"""
        try:
            with self.lock:
                if name in self.secrets:
                    del self.secrets[name]
                    if name in self.metadata:
                        del self.metadata[name]
                    
                    self._save_secrets()
                    self.logger.info(f"[SECRETS] Secret '{name}' deleted")
                    return True
                else:
                    self.logger.warning(f"[SECRETS] Secret '{name}' not found")
                    return False
                    
        except Exception as e:
            self.logger.error(f"[SECRETS] Failed to delete secret '{name}': {e}")
            return False
    
    def list_secrets(self) -> List[str]:
        """List all stored secret names"""
        with self.lock:
            return list(self.secrets.keys())
    
    def get_secret_metadata(self, name: str) -> Optional[SecretMetadata]:
        """Get metadata for a secret"""
        return self.metadata.get(name)
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """Validate that required API keys are present"""
        required_keys = [
            'fmp_api_key',
            'alpaca_api_key',
            'alpaca_secret_key'
        ]
        
        optional_keys = [
            'grok_api_key',
            'openai_api_key',
            'polygon_api_key'
        ]
        
        validation_results = {}
        
        # Check required keys
        for key in required_keys:
            value = self.get_secret(key)
            validation_results[key] = bool(value and len(value) > 10)
        
        # Check optional keys
        for key in optional_keys:
            value = self.get_secret(key)
            validation_results[key] = bool(value and len(value) > 10)
        
        return validation_results
    
    def get_api_config(self) -> Dict[str, str]:
        """Get API configuration for Atlas components"""
        config = {}
        
        # Map secret names to config keys
        key_mapping = {
            'fmp_api_key': 'fmp_api_key',
            'alpaca_api_key': 'alpaca_api_key',
            'alpaca_secret_key': 'alpaca_secret_key',
            'grok_api_key': 'grok_api_key',
            'openai_api_key': 'openai_api_key'
        }
        
        for secret_name, config_key in key_mapping.items():
            value = self.get_secret(secret_name)
            if value:
                config[config_key] = value
        
        return config
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get secrets manager status"""
        validation_results = self.validate_api_keys()
        
        return {
            "secrets_count": len(self.secrets),
            "encryption_available": self.encryption.encryption_available,
            "secrets_file_exists": self.secrets_file.exists(),
            "api_key_validation": validation_results,
            "required_keys_valid": all(validation_results[key] for key in ['fmp_api_key', 'alpaca_api_key', 'alpaca_secret_key']),
            "last_updated": max([meta.last_accessed for meta in self.metadata.values()]).isoformat() if self.metadata else None
        }

# Global secrets manager instance
secrets_manager = AtlasSecretsManager()

# Export main components
__all__ = [
    'SecretMetadata',
    'SecretEncryption',
    'AtlasSecretsManager',
    'secrets_manager'
]
