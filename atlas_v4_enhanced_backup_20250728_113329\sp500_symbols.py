"""
S&P 500 Symbol List for A.T.L.A.S. Trading System
Comprehensive list of S&P 500 stocks for market scanning
"""

# S&P 500 symbols (updated list)
SP500_SYMBOLS = [
    # Technology
    "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE",
    "CRM", "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "AMAT", "LRCX", "KLAC",
    "MRVL", "MU", "ADI", "MCHP", "SNPS", "CDNS", "FTNT", "PANW", "CRWD", "ZS",
    "OKTA", "DDOG", "NET", "SNOW", "PLTR", "COIN", "SQ", "PYPL", "SHOP", "UBER",
    "LYFT", "ABNB", "DASH", "ZM", "DOCU", "ROKU", "SPOT", "TWLO", "WORK", "TEAM",
    
    # Healthcare & Pharmaceuticals
    "JNJ", "PFE", "UNH", "ABBV", "MRK", "TMO", "ABT", "DHR", "BMY", "AMGN",
    "GILD", "VRTX", "REGN", "BIIB", "ILMN", "MRNA", "BNTX", "ZTS", "ELV", "CVS",
    "CI", "HUM", "ANTM", "CNC", "MOH", "UHS", "HCA", "THC", "DVA", "DGX",
    "LH", "IQV", "A", "RMD", "ISRG", "SYK", "BSX", "MDT", "EW", "HOLX",
    
    # Financial Services
    "BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "BLK", "SPGI",
    "CME", "ICE", "NDAQ", "MCO", "COF", "USB", "TFC", "PNC", "SCHW", "CB",
    "MMC", "AON", "AJG", "BRO", "PGR", "TRV", "ALL", "AIG", "MET", "PRU",
    "AFL", "L", "LNC", "PFG", "TMK", "RJF", "SIVB", "FITB", "HBAN", "RF",
    
    # Consumer Discretionary
    "AMZN", "TSLA", "HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "ABNB",
    "GM", "F", "UBER", "LYFT", "CCL", "RCL", "NCLH", "MAR", "HLT", "MGM",
    "WYNN", "LVS", "CZR", "PENN", "DRI", "CMG", "QSR", "YUM", "DPZ", "DNKN",
    "MHK", "WHR", "LEN", "DHI", "PHM", "NVR", "TOL", "KBH", "MTH", "TMHC",
    
    # Consumer Staples
    "PG", "KO", "PEP", "WMT", "COST", "MDLZ", "GIS", "K", "CPB", "CAG",
    "HSY", "SJM", "HRL", "TSN", "TYSON", "ADM", "BG", "CF", "MOS", "FMC",
    "CL", "KMB", "CHD", "CLX", "EL", "COTY", "TAP", "STZ", "DEO", "BF.B",
    
    # Energy
    "XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "HES", "DVN",
    "FANG", "APA", "OXY", "BKR", "HAL", "NOV", "FTI", "HP", "RIG", "VAL",
    "KMI", "OKE", "EPD", "ET", "WMB", "TRGP", "ONEOK", "MPLX", "PAA", "EQT",
    
    # Industrials
    "BA", "CAT", "GE", "MMM", "HON", "UPS", "RTX", "LMT", "NOC", "GD",
    "LHX", "TXT", "ETN", "EMR", "ITW", "PH", "CMI", "DE", "FDX", "UNP",
    "CSX", "NSC", "KSU", "CP", "CNI", "ODFL", "CHRW", "EXPD", "JBHT", "KNX",
    
    # Materials
    "LIN", "APD", "ECL", "SHW", "FCX", "NEM", "GOLD", "AA", "X", "CLF",
    "NUE", "STLD", "RS", "MLM", "VMC", "EMN", "DD", "DOW", "LYB", "CF",
    
    # Real Estate
    "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "AVB", "EQR", "UDR", "CPT",
    "MAA", "ESS", "AIV", "BXP", "VTR", "WELL", "PEAK", "O", "STOR", "CXW",
    
    # Utilities
    "NEE", "DUK", "SO", "D", "AEP", "EXC", "XEL", "SRE", "PEG", "ED",
    "FE", "ETR", "ES", "DTE", "PPL", "AES", "LNT", "NI", "EVRG", "CMS",
    
    # Communication Services
    "GOOGL", "GOOG", "META", "NFLX", "DIS", "CMCSA", "VZ", "T", "TMUS", "CHTR",
    "DISH", "FOXA", "FOX", "PARA", "WBD", "NWSA", "NWS", "NYT", "TWTR", "SNAP",
    
    # REITs and Others
    "BRK.A", "BRK.B", "SPY", "QQQ", "IWM", "VTI", "VOO", "VEA", "VWO", "AGG"
]

# Core S&P 500 symbols (most liquid and actively traded)
CORE_SP500_SYMBOLS = [
    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE", "CRM",
    "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "AMAT", "MU", "PYPL", "SQ",
    "JNJ", "PFE", "UNH", "ABBV", "MRK", "TMO", "ABT", "DHR", "BMY", "AMGN",
    "BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "BLK", "SPGI",
    "HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "GM", "F", "MAR",
    "PG", "KO", "PEP", "WMT", "COST", "MDLZ", "GIS", "K", "CPB", "CAG",
    "XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "HES", "DVN",
    "BA", "CAT", "GE", "MMM", "HON", "UPS", "RTX", "LMT", "NOC", "GD",
    "LIN", "APD", "ECL", "SHW", "FCX", "NEM", "NUE", "STLD", "MLM", "VMC",
    "NEE", "DUK", "SO", "D", "AEP", "EXC", "XEL", "SRE", "PEG", "ED"
]

# High-volume trading symbols (most suitable for day trading)
HIGH_VOLUME_SYMBOLS = [
    "SPY", "QQQ", "IWM", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA",
    "NFLX", "AMD", "INTC", "PYPL", "SQ", "UBER", "LYFT", "ABNB", "COIN", "PLTR",
    "JPM", "BAC", "WFC", "GS", "XOM", "CVX", "F", "GM", "HD", "WMT"
]

def get_sp500_symbols() -> list:
    """Get the full S&P 500 symbol list"""
    return SP500_SYMBOLS.copy()

def get_core_sp500_symbols() -> list:
    """Get core S&P 500 symbols (most liquid)"""
    return CORE_SP500_SYMBOLS.copy()

def get_high_volume_symbols() -> list:
    """Get high-volume trading symbols"""
    return HIGH_VOLUME_SYMBOLS.copy()

def get_symbols_by_sector(sector: str) -> list:
    """Get symbols by sector (simplified categorization)"""
    sector_map = {
        'technology': ["AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE"],
        'healthcare': ["JNJ", "PFE", "UNH", "ABBV", "MRK", "TMO", "ABT", "DHR", "BMY", "AMGN"],
        'financial': ["BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "BLK", "SPGI"],
        'consumer': ["HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "PG", "KO", "PEP"],
        'energy': ["XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "HES", "DVN"],
        'industrial': ["BA", "CAT", "GE", "MMM", "HON", "UPS", "RTX", "LMT", "NOC", "GD"]
    }
    return sector_map.get(sector.lower(), [])

# ENHANCED Symbol validation with proper length checking
def is_valid_sp500_symbol(symbol: str) -> bool:
    """Check if symbol is in S&P 500 - FIXED: Must be 2+ characters"""
    if not symbol or len(symbol) < 2:
        return False
    return symbol.upper() in SP500_SYMBOLS

def is_valid_stock_symbol(symbol: str) -> bool:
    """Enhanced stock symbol validation - SYSTEM-WIDE FIX"""
    import re

    # Must be string and 2-5 characters
    if not isinstance(symbol, str) or not (2 <= len(symbol) <= 5):
        return False

    # Must be all uppercase letters
    if not symbol.isupper() or not symbol.isalpha():
        return False

    # Check against known symbol lists
    all_known_symbols = set(SP500_SYMBOLS + HIGH_VOLUME_SYMBOLS + [
        "SPY", "QQQ", "IWM", "VTI", "VOO", "VEA", "VWO", "AGG"
    ])

    if symbol in all_known_symbols:
        return True

    # Additional validation for potential symbols not in our lists
    # Exclude common non-symbol patterns (ENHANCED)
    non_symbol_patterns = [
        r'^[AEIOU]{2,}$',  # All vowels
        r'^(AM|PM|ET|PT|CT|MT|EST|PST|CST|MST)$',  # Time zones (expanded)
        r'^(US|USA|UK|EU|CA|JP|CN|FR|DE|IT|ES)$',  # Country codes (expanded)
        r'^(CEO|CFO|CTO|CMO|COO|VP|GM|HR|PR)$',  # Executive titles (expanded)
        r'^(API|URL|SQL|XML|JSON|CSV|PDF|HTTP|HTML|CSS)$',  # Tech acronyms (expanded)
        r'^(FBI|CIA|SEC|FDA|IRS|DOJ|EPA|FTC)$',  # Government agencies
        r'^(LLC|INC|CORP|LTD)$',  # Business suffixes
    ]

    for pattern in non_symbol_patterns:
        if re.match(pattern, symbol):
            return False

    # If it passes all checks, consider it a potential valid symbol
    return True

def get_symbol_count() -> dict:
    """Get symbol counts by category"""
    return {
        'total_sp500': len(SP500_SYMBOLS),
        'core_sp500': len(CORE_SP500_SYMBOLS),
        'high_volume': len(HIGH_VOLUME_SYMBOLS)
    }

# Market Cap Categories for Expanded Universe
LARGE_CAP_SYMBOLS = [
    # Mega-cap (>$200B) - Keep existing major stocks
    "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "NVDA", "TSLA", "META", "BRK.B", "UNH",
    "JNJ", "V", "XOM", "JPM", "PG", "MA", "HD", "CVX", "ABBV", "PFE", "KO", "AVGO",
    "BAC", "TMO", "COST", "ADBE", "NFLX", "DIS", "ABT", "CRM", "VZ", "CMCSA", "PEP",
    "T", "DHR", "NKE", "TXN", "QCOM", "BMY", "NEE", "PM", "RTX", "HON", "UPS", "LOW"
]

MID_CAP_SYMBOLS = [
    # Mid-cap stocks ($2B - $10B market cap) with good liquidity
    "PLTR", "COIN", "RBLX", "HOOD", "SOFI", "RIVN", "LCID", "SPCE", "NKLA", "OPEN",
    "DKNG", "PENN", "MGM", "WYNN", "LVS", "CZR", "BYD", "CHWY", "ETSY", "PINS",
    "SNAP", "TWTR", "ROKU", "SPOT", "ZM", "DOCU", "WORK", "TEAM", "OKTA", "CRWD",
    "ZS", "NET", "DDOG", "SNOW", "MDB", "FSLY", "ESTC", "SPLK", "NOW", "WDAY",
    "VEEV", "ZEN", "BILL", "SQ", "PYPL", "SHOP", "UBER", "LYFT", "ABNB", "DASH"
]

SMALL_CAP_SYMBOLS = [
    # Small-cap stocks ($300M - $2B) with decent volume
    "AMC", "GME", "BB", "NOK", "SNDL", "TLRY", "CGC", "ACB", "HEXO", "CRON",
    "MVIS", "CLOV", "WISH", "GOEV", "RIDE", "WKHS", "HYLN", "SHLL", "CCIV", "PSTH",
    "SPAC", "IPOE", "IPOF", "CCXX", "THCB", "ACTC", "STPK", "GHIV", "FIII", "AJAX",
    "KCAC", "HCAC", "TRNE", "DMYT", "FUSE", "RICE", "CONX", "BTWN", "CRHC", "PRPB",
    "AVAN", "BENE", "BITE", "BODY", "BTAQ", "BWAC", "CAHC", "CBAH", "CCAC", "CMLF"
]

MICRO_CAP_SYMBOLS = [
    # Micro-cap stocks ($50M - $300M) - carefully selected for liquidity
    "EXPR", "BBBY", "KOSS", "NAKD", "SIRI", "ZYXI", "TOPS", "SHIP", "DRYS", "GLBS",
    "CASI", "JAGX", "OBSV", "TNXP", "XSPA", "AYTU", "BIOC", "BIOL", "BNGO", "CHEK",
    "CIDM", "CLIS", "COCP", "CTRM", "DLPN", "DSGT", "EAST", "EBON", "EGHT", "ENZC",
    "EVFM", "FAMI", "FRSX", "GEVO", "GNUS", "HEPA", "HMHC", "IDEX", "INPX", "IZEA",
    "KTOV", "LKCO", "MARK", "MICT", "NNDM", "OCGN", "ONTX", "OPTI", "OZSC", "PASO"
]

# Sector-specific additions for diversification
BIOTECH_GROWTH_SYMBOLS = [
    "MRNA", "BNTX", "NVAX", "VXRT", "INO", "OCGN", "SRNE", "VBIV", "COCP", "BIOL",
    "TNXP", "JAGX", "OBSV", "AYTU", "BIOC", "CHEK", "CTXR", "SESN", "ADMP", "ATOS"
]

CLEAN_ENERGY_SYMBOLS = [
    "TSLA", "NIO", "XPEV", "LI", "RIVN", "LCID", "FSR", "GOEV", "RIDE", "WKHS",
    "HYLN", "NKLA", "PLUG", "FCEL", "BLDP", "BE", "CLSK", "RIOT", "MARA", "EBON"
]

FINTECH_SYMBOLS = [
    "SQ", "PYPL", "COIN", "HOOD", "SOFI", "AFRM", "UPST", "LC", "TREE", "ENVA",
    "STNE", "PAGS", "NU", "MELI", "OPEN", "RDFN", "COMP", "BILL", "FOUR", "SFIX"
]

def get_expanded_universe_symbols() -> list:
    """Get comprehensive expanded stock universe across all market caps"""
    all_expanded = set()

    # Add all market cap categories
    all_expanded.update(LARGE_CAP_SYMBOLS)
    all_expanded.update(MID_CAP_SYMBOLS)
    all_expanded.update(SMALL_CAP_SYMBOLS)
    all_expanded.update(MICRO_CAP_SYMBOLS)

    # Add sector-specific symbols
    all_expanded.update(BIOTECH_GROWTH_SYMBOLS)
    all_expanded.update(CLEAN_ENERGY_SYMBOLS)
    all_expanded.update(FINTECH_SYMBOLS)

    # Add existing S&P 500 symbols to ensure coverage
    all_expanded.update(SP500_SYMBOLS)

    return sorted(list(all_expanded))

def get_symbols_by_market_cap(market_cap: str) -> list:
    """Get symbols by market capitalization category"""
    market_cap_map = {
        'large': LARGE_CAP_SYMBOLS,
        'mid': MID_CAP_SYMBOLS,
        'small': SMALL_CAP_SYMBOLS,
        'micro': MICRO_CAP_SYMBOLS,
        'mega': LARGE_CAP_SYMBOLS[:20]  # Top 20 largest
    }
    return market_cap_map.get(market_cap.lower(), [])

def get_symbols_by_growth_sector(sector: str) -> list:
    """Get symbols by growth sector"""
    sector_map = {
        'biotech': BIOTECH_GROWTH_SYMBOLS,
        'clean_energy': CLEAN_ENERGY_SYMBOLS,
        'fintech': FINTECH_SYMBOLS
    }
    return sector_map.get(sector.lower(), [])
