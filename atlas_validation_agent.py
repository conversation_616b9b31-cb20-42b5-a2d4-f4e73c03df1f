"""
A.T.L.A.S. Validation Agent
Specialized agent for cross-validation, consistency checking, and quality assurance
"""

import asyncio
import logging
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from pydantic import Field

# CrewAI imports
from crewai.tools import BaseTool

# Core imports
from atlas_multi_agent_core import AtlasBaseAgent, AgentRole, MultiAgentTask

logger = logging.getLogger(__name__)

# ============================================================================
# VALIDATION ENUMS AND MODELS
# ============================================================================

class ValidationResult(Enum):
    """Validation result types"""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    CRITICAL = "critical"

@dataclass
class ValidationCheck:
    """Individual validation check result"""
    check_name: str
    result: ValidationResult
    score: float
    details: str
    recommendations: List[str]
    timestamp: datetime

@dataclass
class CrossValidationReport:
    """Comprehensive cross-validation report"""
    validation_id: str
    agents_validated: List[str]
    total_checks: int
    passed_checks: int
    failed_checks: int
    warning_checks: int
    critical_checks: int
    overall_score: float
    consistency_score: float
    quality_score: float
    compliance_score: float
    validation_checks: List[ValidationCheck]
    final_recommendation: str
    timestamp: datetime

# ============================================================================
# VALIDATION TOOLS
# ============================================================================

class ConsistencyCheckTool(BaseTool):
    """Tool for checking consistency across agent outputs"""
    
    name: str = "consistency_checker"
    description: str = "Checks consistency and coherence across multiple agent outputs"
    
    def _run(self, agent_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """Check consistency across agent outputs"""
        try:
            consistency_checks = []
            overall_consistency = 0.0
            
            # Check data validation consistency
            if "data_validation" in agent_outputs and "pattern_detection" in agent_outputs:
                data_confidence = agent_outputs["data_validation"].get("confidence_score", 0.0)
                pattern_confidence = agent_outputs["pattern_detection"].get("confidence_score", 0.0)
                
                confidence_diff = abs(data_confidence - pattern_confidence)
                consistency_score = max(0, 1 - confidence_diff)
                
                consistency_checks.append(ValidationCheck(
                    check_name="data_pattern_consistency",
                    result=ValidationResult.PASSED if consistency_score > 0.7 else ValidationResult.WARNING,
                    score=consistency_score,
                    details=f"Data validation confidence ({data_confidence:.2f}) vs Pattern detection confidence ({pattern_confidence:.2f})",
                    recommendations=["Review data quality if confidence scores differ significantly"] if consistency_score < 0.7 else [],
                    timestamp=datetime.now()
                ))
                overall_consistency += consistency_score
            
            # Check sentiment-pattern alignment
            if "analysis" in agent_outputs and "pattern_detection" in agent_outputs:
                sentiment_score = agent_outputs["analysis"].get("overall_sentiment", 0.0)
                pattern_signal = agent_outputs["pattern_detection"].get("signal_strength", "neutral")
                
                # Convert pattern signal to numeric for comparison
                pattern_numeric = {"very_strong": 1.0, "strong": 0.7, "moderate": 0.4, "weak": 0.1}.get(pattern_signal, 0.0)
                
                alignment_score = 1 - abs(sentiment_score - pattern_numeric)
                
                consistency_checks.append(ValidationCheck(
                    check_name="sentiment_pattern_alignment",
                    result=ValidationResult.PASSED if alignment_score > 0.6 else ValidationResult.WARNING,
                    score=alignment_score,
                    details=f"Sentiment score ({sentiment_score:.2f}) alignment with pattern strength ({pattern_signal})",
                    recommendations=["Investigate conflicting signals between sentiment and technical analysis"] if alignment_score < 0.6 else [],
                    timestamp=datetime.now()
                ))
                overall_consistency += alignment_score
            
            # Check risk-reward consistency
            if "risk_management" in agent_outputs and "trade_execution" in agent_outputs:
                risk_assessment = agent_outputs["risk_management"].get("overall_assessment", "moderate")
                trade_confidence = agent_outputs["trade_execution"].get("confidence_score", 0.5)
                
                # Risk assessment to numeric conversion
                risk_numeric = {"low_risk": 0.9, "moderate_risk": 0.6, "high_risk": 0.3}.get(risk_assessment, 0.5)
                
                risk_trade_consistency = 1 - abs(risk_numeric - trade_confidence)
                
                consistency_checks.append(ValidationCheck(
                    check_name="risk_trade_consistency",
                    result=ValidationResult.PASSED if risk_trade_consistency > 0.5 else ValidationResult.FAILED,
                    score=risk_trade_consistency,
                    details=f"Risk assessment ({risk_assessment}) consistency with trade confidence ({trade_confidence:.2f})",
                    recommendations=["Recalibrate risk assessment or trade confidence"] if risk_trade_consistency < 0.5 else [],
                    timestamp=datetime.now()
                ))
                overall_consistency += risk_trade_consistency
            
            # Calculate overall consistency score
            if consistency_checks:
                overall_consistency /= len(consistency_checks)
            
            return {
                "consistency_score": overall_consistency,
                "consistency_checks": [check.__dict__ for check in consistency_checks],
                "agents_checked": len(agent_outputs),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

class QualityAssuranceTool(BaseTool):
    """Tool for quality assurance checks on agent outputs"""
    
    name: str = "quality_assurance"
    description: str = "Performs comprehensive quality assurance checks on agent outputs"
    
    def _run(self, agent_outputs: Dict[str, Any], quality_thresholds: Dict[str, float] = None) -> Dict[str, Any]:
        """Perform quality assurance checks"""
        quality_thresholds = quality_thresholds or {
            "min_confidence": 0.7,
            "min_data_quality": 0.8,
            "min_risk_reward": 1.5,
            "max_position_size": 0.1
        }
        
        try:
            quality_checks = []
            overall_quality = 0.0
            
            # Data quality check
            if "data_validation" in agent_outputs:
                data_output = agent_outputs["data_validation"]
                data_quality = data_output.get("confidence_score", 0.0)
                
                quality_checks.append(ValidationCheck(
                    check_name="data_quality",
                    result=ValidationResult.PASSED if data_quality >= quality_thresholds["min_data_quality"] else ValidationResult.FAILED,
                    score=data_quality,
                    details=f"Data quality score: {data_quality:.2f} (threshold: {quality_thresholds['min_data_quality']})",
                    recommendations=["Improve data sources or validation methods"] if data_quality < quality_thresholds["min_data_quality"] else [],
                    timestamp=datetime.now()
                ))
                overall_quality += min(data_quality / quality_thresholds["min_data_quality"], 1.0)
            
            # Pattern detection quality
            if "pattern_detection" in agent_outputs:
                pattern_output = agent_outputs["pattern_detection"]
                pattern_confidence = pattern_output.get("confidence_score", 0.0)
                
                quality_checks.append(ValidationCheck(
                    check_name="pattern_quality",
                    result=ValidationResult.PASSED if pattern_confidence >= quality_thresholds["min_confidence"] else ValidationResult.WARNING,
                    score=pattern_confidence,
                    details=f"Pattern detection confidence: {pattern_confidence:.2f} (threshold: {quality_thresholds['min_confidence']})",
                    recommendations=["Consider additional pattern confirmation"] if pattern_confidence < quality_thresholds["min_confidence"] else [],
                    timestamp=datetime.now()
                ))
                overall_quality += min(pattern_confidence / quality_thresholds["min_confidence"], 1.0)
            
            # Analysis quality
            if "analysis" in agent_outputs:
                analysis_output = agent_outputs["analysis"]
                analysis_confidence = analysis_output.get("confidence_score", 0.0)
                
                quality_checks.append(ValidationCheck(
                    check_name="analysis_quality",
                    result=ValidationResult.PASSED if analysis_confidence >= quality_thresholds["min_confidence"] else ValidationResult.WARNING,
                    score=analysis_confidence,
                    details=f"Analysis confidence: {analysis_confidence:.2f} (threshold: {quality_thresholds['min_confidence']})",
                    recommendations=["Enhance analysis with additional data sources"] if analysis_confidence < quality_thresholds["min_confidence"] else [],
                    timestamp=datetime.now()
                ))
                overall_quality += min(analysis_confidence / quality_thresholds["min_confidence"], 1.0)
            
            # Risk management quality
            if "risk_management" in agent_outputs:
                risk_output = agent_outputs["risk_management"]
                risk_metrics = risk_output.get("risk_metrics", {})
                
                # Check risk-reward ratio
                risk_reward = risk_metrics.get("risk_reward_ratio", 0.0)
                quality_checks.append(ValidationCheck(
                    check_name="risk_reward_quality",
                    result=ValidationResult.PASSED if risk_reward >= quality_thresholds["min_risk_reward"] else ValidationResult.FAILED,
                    score=min(risk_reward / quality_thresholds["min_risk_reward"], 1.0),
                    details=f"Risk-reward ratio: {risk_reward:.2f} (minimum: {quality_thresholds['min_risk_reward']})",
                    recommendations=["Adjust position sizing or targets to improve risk-reward"] if risk_reward < quality_thresholds["min_risk_reward"] else [],
                    timestamp=datetime.now()
                ))
                overall_quality += min(risk_reward / quality_thresholds["min_risk_reward"], 1.0)
            
            # Trade execution quality
            if "trade_execution" in agent_outputs:
                trade_output = agent_outputs["trade_execution"]
                recommendation = trade_output.get("recommendation", {})
                
                # Check position size
                position_percentage = recommendation.get("position_percentage", 0.0) / 100
                quality_checks.append(ValidationCheck(
                    check_name="position_size_quality",
                    result=ValidationResult.PASSED if position_percentage <= quality_thresholds["max_position_size"] else ValidationResult.CRITICAL,
                    score=1.0 if position_percentage <= quality_thresholds["max_position_size"] else 0.0,
                    details=f"Position size: {position_percentage*100:.1f}% (maximum: {quality_thresholds['max_position_size']*100:.1f}%)",
                    recommendations=["Reduce position size to comply with risk limits"] if position_percentage > quality_thresholds["max_position_size"] else [],
                    timestamp=datetime.now()
                ))
                overall_quality += 1.0 if position_percentage <= quality_thresholds["max_position_size"] else 0.0
            
            # Calculate overall quality score
            if quality_checks:
                overall_quality /= len(quality_checks)
            
            return {
                "quality_score": overall_quality,
                "quality_checks": [check.__dict__ for check in quality_checks],
                "quality_level": "high" if overall_quality > 0.8 else "medium" if overall_quality > 0.6 else "low",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

class ComplianceValidationTool(BaseTool):
    """Tool for comprehensive compliance validation"""
    
    name: str = "compliance_validator"
    description: str = "Validates all outputs against regulatory and internal compliance requirements"
    
    def _run(self, agent_outputs: Dict[str, Any], compliance_rules: Dict[str, Any] = None) -> Dict[str, Any]:
        """Validate compliance across all agent outputs"""
        compliance_rules = compliance_rules or {
            "max_leverage": 2.0,
            "max_concentration": 0.2,
            "required_documentation": True,
            "risk_disclosure": True
        }
        
        try:
            compliance_checks = []
            overall_compliance = 0.0
            
            # Documentation compliance
            documentation_score = 1.0
            for agent_name, output in agent_outputs.items():
                if not output.get("timestamp") or not output.get("agent_id"):
                    documentation_score = 0.0
                    break
            
            compliance_checks.append(ValidationCheck(
                check_name="documentation_compliance",
                result=ValidationResult.PASSED if documentation_score > 0 else ValidationResult.CRITICAL,
                score=documentation_score,
                details="All agent outputs must include timestamp and agent identification",
                recommendations=["Ensure all agents provide proper documentation"] if documentation_score == 0 else [],
                timestamp=datetime.now()
            ))
            overall_compliance += documentation_score
            
            # Risk disclosure compliance
            if "risk_management" in agent_outputs:
                risk_output = agent_outputs["risk_management"]
                has_risk_disclosure = "risk_assessment" in risk_output or "risk_metrics" in risk_output
                
                compliance_checks.append(ValidationCheck(
                    check_name="risk_disclosure_compliance",
                    result=ValidationResult.PASSED if has_risk_disclosure else ValidationResult.CRITICAL,
                    score=1.0 if has_risk_disclosure else 0.0,
                    details="Risk disclosure and assessment must be provided",
                    recommendations=["Include comprehensive risk assessment"] if not has_risk_disclosure else [],
                    timestamp=datetime.now()
                ))
                overall_compliance += 1.0 if has_risk_disclosure else 0.0
            
            # Position concentration compliance
            if "trade_execution" in agent_outputs:
                trade_output = agent_outputs["trade_execution"]
                recommendation = trade_output.get("recommendation", {})
                position_percentage = recommendation.get("position_percentage", 0.0) / 100
                
                concentration_compliant = position_percentage <= compliance_rules["max_concentration"]
                
                compliance_checks.append(ValidationCheck(
                    check_name="concentration_compliance",
                    result=ValidationResult.PASSED if concentration_compliant else ValidationResult.CRITICAL,
                    score=1.0 if concentration_compliant else 0.0,
                    details=f"Position concentration: {position_percentage*100:.1f}% (limit: {compliance_rules['max_concentration']*100:.1f}%)",
                    recommendations=["Reduce position size to meet concentration limits"] if not concentration_compliant else [],
                    timestamp=datetime.now()
                ))
                overall_compliance += 1.0 if concentration_compliant else 0.0
            
            # Data validation compliance
            if "data_validation" in agent_outputs:
                data_output = agent_outputs["data_validation"]
                data_validated = data_output.get("passed_validation", False)
                
                compliance_checks.append(ValidationCheck(
                    check_name="data_validation_compliance",
                    result=ValidationResult.PASSED if data_validated else ValidationResult.FAILED,
                    score=1.0 if data_validated else 0.0,
                    details="Market data must pass validation checks",
                    recommendations=["Resolve data quality issues before proceeding"] if not data_validated else [],
                    timestamp=datetime.now()
                ))
                overall_compliance += 1.0 if data_validated else 0.0
            
            # Calculate overall compliance score
            if compliance_checks:
                overall_compliance /= len(compliance_checks)
            
            return {
                "compliance_score": overall_compliance,
                "compliance_checks": [check.__dict__ for check in compliance_checks],
                "compliance_status": "compliant" if overall_compliance > 0.8 else "non_compliant",
                "critical_violations": len([c for c in compliance_checks if c.result == ValidationResult.CRITICAL]),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

# ============================================================================
# VALIDATION AGENT
# ============================================================================

class AtlasValidationAgent(AtlasBaseAgent):
    """Specialized agent for cross-validation and quality assurance"""
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        agent_id = agent_id or f"validation_supervisor_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        super().__init__(agent_id, AgentRole.VALIDATION_SUPERVISOR, config)
        
        self.validation_thresholds = {
            "min_overall_score": 0.8,
            "min_consistency_score": 0.7,
            "min_quality_score": 0.75,
            "min_compliance_score": 0.9
        }
    
    async def _initialize_tools(self):
        """Initialize validation specific tools"""
        try:
            # Initialize validation tools
            self.tools = [
                ConsistencyCheckTool(),
                QualityAssuranceTool(),
                ComplianceValidationTool()
            ]
            
            self.logger.info(f"Initialized {len(self.tools)} validation tools")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize validation tools: {e}")
            raise
    
    def _get_agent_goal(self) -> str:
        """Get the validation agent's primary goal"""
        return ("Ensure the highest quality and consistency of all multi-agent outputs "
                "through comprehensive cross-validation, quality assurance, and compliance "
                "verification to maintain system integrity and regulatory compliance.")
    
    def _get_agent_backstory(self) -> str:
        """Get the validation agent's backstory"""
        return ("You are an expert quality assurance and compliance specialist with "
                "extensive experience in financial systems validation. You have deep "
                "knowledge of regulatory requirements, risk management standards, and "
                "quality control processes. Your role is critical in ensuring that all "
                "multi-agent outputs meet the highest standards of accuracy, consistency, "
                "and compliance before any trading decisions are made. You are meticulous, "
                "thorough, and have zero tolerance for compliance violations or quality issues.")
    
    async def process_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Process a validation task"""
        try:
            task_type = task.input_data.get("task_type", "comprehensive_validation")
            
            if task_type == "consistency_check":
                return await self._check_consistency(task)
            elif task_type == "quality_assurance":
                return await self._perform_quality_assurance(task)
            elif task_type == "compliance_validation":
                return await self._validate_compliance(task)
            elif task_type == "comprehensive_validation":
                return await self._comprehensive_validation(task)
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
                
        except Exception as e:
            self.logger.error(f"Error processing validation task: {e}")
            raise
    
    async def _check_consistency(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Check consistency across agent outputs"""
        agent_outputs = task.input_data.get("agent_outputs", {})
        
        if not agent_outputs:
            raise ValueError("Agent outputs are required for consistency checking")
        
        # Use the consistency check tool
        consistency_tool = self.tools[0]  # ConsistencyCheckTool
        result = consistency_tool._run(agent_outputs)
        
        if "error" in result:
            raise Exception(result["error"])
        
        consistency_score = result.get("consistency_score", 0.0)
        
        return {
            "validation_type": "consistency",
            "consistency_result": result,
            "consistency_score": consistency_score,
            "consistency_passed": consistency_score >= self.validation_thresholds["min_consistency_score"],
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _perform_quality_assurance(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Perform quality assurance on agent outputs"""
        agent_outputs = task.input_data.get("agent_outputs", {})
        quality_thresholds = task.input_data.get("quality_thresholds")
        
        if not agent_outputs:
            raise ValueError("Agent outputs are required for quality assurance")
        
        # Use the quality assurance tool
        qa_tool = self.tools[1]  # QualityAssuranceTool
        result = qa_tool._run(agent_outputs, quality_thresholds)
        
        if "error" in result:
            raise Exception(result["error"])
        
        quality_score = result.get("quality_score", 0.0)
        
        return {
            "validation_type": "quality_assurance",
            "quality_result": result,
            "quality_score": quality_score,
            "quality_passed": quality_score >= self.validation_thresholds["min_quality_score"],
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _validate_compliance(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Validate compliance across agent outputs"""
        agent_outputs = task.input_data.get("agent_outputs", {})
        compliance_rules = task.input_data.get("compliance_rules")
        
        if not agent_outputs:
            raise ValueError("Agent outputs are required for compliance validation")
        
        # Use the compliance validation tool
        compliance_tool = self.tools[2]  # ComplianceValidationTool
        result = compliance_tool._run(agent_outputs, compliance_rules)
        
        if "error" in result:
            raise Exception(result["error"])
        
        compliance_score = result.get("compliance_score", 0.0)
        critical_violations = result.get("critical_violations", 0)
        
        return {
            "validation_type": "compliance",
            "compliance_result": result,
            "compliance_score": compliance_score,
            "compliance_passed": compliance_score >= self.validation_thresholds["min_compliance_score"] and critical_violations == 0,
            "critical_violations": critical_violations,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _comprehensive_validation(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Perform comprehensive validation including all validation types"""
        agent_outputs = task.input_data.get("agent_outputs", {})
        quality_thresholds = task.input_data.get("quality_thresholds")
        compliance_rules = task.input_data.get("compliance_rules")
        
        if not agent_outputs:
            raise ValueError("Agent outputs are required for comprehensive validation")
        
        validation_results = {}
        overall_scores = []
        
        # Perform consistency check
        try:
            consistency_task = MultiAgentTask(
                task_id=f"{task.task_id}_consistency",
                description="Consistency validation",
                priority=task.priority,
                required_agents=[AgentRole.VALIDATION_SUPERVISOR],
                input_data={"task_type": "consistency_check", "agent_outputs": agent_outputs},
                expected_output={}
            )
            validation_results["consistency"] = await self._check_consistency(consistency_task)
            overall_scores.append(validation_results["consistency"]["consistency_score"])
        except Exception as e:
            self.logger.error(f"Consistency check failed: {e}")
            validation_results["consistency"] = {"error": str(e)}
        
        # Perform quality assurance
        try:
            qa_task = MultiAgentTask(
                task_id=f"{task.task_id}_quality",
                description="Quality assurance validation",
                priority=task.priority,
                required_agents=[AgentRole.VALIDATION_SUPERVISOR],
                input_data={"task_type": "quality_assurance", "agent_outputs": agent_outputs, "quality_thresholds": quality_thresholds},
                expected_output={}
            )
            validation_results["quality"] = await self._perform_quality_assurance(qa_task)
            overall_scores.append(validation_results["quality"]["quality_score"])
        except Exception as e:
            self.logger.error(f"Quality assurance failed: {e}")
            validation_results["quality"] = {"error": str(e)}
        
        # Perform compliance validation
        try:
            compliance_task = MultiAgentTask(
                task_id=f"{task.task_id}_compliance",
                description="Compliance validation",
                priority=task.priority,
                required_agents=[AgentRole.VALIDATION_SUPERVISOR],
                input_data={"task_type": "compliance_validation", "agent_outputs": agent_outputs, "compliance_rules": compliance_rules},
                expected_output={}
            )
            validation_results["compliance"] = await self._validate_compliance(compliance_task)
            overall_scores.append(validation_results["compliance"]["compliance_score"])
        except Exception as e:
            self.logger.error(f"Compliance validation failed: {e}")
            validation_results["compliance"] = {"error": str(e)}
        
        # Calculate overall validation score
        overall_score = np.mean(overall_scores) if overall_scores else 0.0
        
        # Determine final validation status
        consistency_passed = validation_results.get("consistency", {}).get("consistency_passed", False)
        quality_passed = validation_results.get("quality", {}).get("quality_passed", False)
        compliance_passed = validation_results.get("compliance", {}).get("compliance_passed", False)
        
        all_validations_passed = consistency_passed and quality_passed and compliance_passed
        
        # Generate final recommendation
        if all_validations_passed and overall_score >= self.validation_thresholds["min_overall_score"]:
            final_recommendation = "APPROVED"
        elif overall_score >= 0.6:
            final_recommendation = "CONDITIONAL_APPROVAL"
        else:
            final_recommendation = "REJECTED"
        
        # Update agent metrics
        self.metrics.confidence_score = (
            (self.metrics.confidence_score * self.metrics.tasks_completed + overall_score) 
            / (self.metrics.tasks_completed + 1)
        )
        
        return {
            "validation_type": "comprehensive",
            "overall_score": overall_score,
            "validation_results": validation_results,
            "all_validations_passed": all_validations_passed,
            "final_recommendation": final_recommendation,
            "agents_validated": list(agent_outputs.keys()),
            "validation_summary": {
                "consistency_passed": consistency_passed,
                "quality_passed": quality_passed,
                "compliance_passed": compliance_passed
            },
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
