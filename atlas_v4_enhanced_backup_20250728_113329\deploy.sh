#!/bin/bash

# A.T.L.A.S. Multi-Agent System Deployment Script
# Production deployment automation for Kubernetes

set -euo pipefail

# Configuration
NAMESPACE="atlas-trading"
APP_NAME="atlas-multi-agent-system"
VERSION="${VERSION:-v4-enhanced}"
DOCKER_REGISTRY="${DOCKER_REGISTRY:-your-registry.com}"
DOCKER_IMAGE="${DOCKER_REGISTRY}/${APP_NAME}:${VERSION}"
KUBECTL_CONTEXT="${KUBECTL_CONTEXT:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check kubectl context
    current_context=$(kubectl config current-context)
    if [[ "$current_context" != "$KUBECTL_CONTEXT" ]]; then
        log_warning "Current kubectl context is '$current_context', expected '$KUBECTL_CONTEXT'"
        read -p "Continue with current context? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Deployment cancelled"
            exit 1
        fi
    fi
    
    log_success "Prerequisites check passed"
}

# Build Docker image
build_image() {
    log_info "Building Docker image..."
    
    # Build the image
    docker build \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse HEAD)" \
        --build-arg VERSION="$VERSION" \
        --target production \
        -t "$DOCKER_IMAGE" \
        .
    
    log_success "Docker image built: $DOCKER_IMAGE"
}

# Push Docker image
push_image() {
    log_info "Pushing Docker image to registry..."
    
    docker push "$DOCKER_IMAGE"
    
    log_success "Docker image pushed: $DOCKER_IMAGE"
}

# Create namespace and apply base configuration
setup_namespace() {
    log_info "Setting up namespace and base configuration..."
    
    # Apply namespace configuration
    kubectl apply -f k8s/namespace.yaml
    
    # Wait for namespace to be ready
    kubectl wait --for=condition=Ready namespace/$NAMESPACE --timeout=60s
    
    log_success "Namespace setup completed"
}

# Apply secrets (with validation)
apply_secrets() {
    log_info "Applying secrets..."
    
    # Check if secrets file exists
    if [[ ! -f "k8s/secrets.yaml" ]]; then
        log_error "Secrets file not found: k8s/secrets.yaml"
        log_error "Please create the secrets file with your API keys"
        exit 1
    fi
    
    # Validate that secrets are not using default values
    if grep -q "eW91cl9ncm9rX2FwaV9rZXlfaGVyZQ==" k8s/secrets.yaml; then
        log_warning "Secrets file contains default values"
        log_warning "Please update k8s/secrets.yaml with your actual API keys"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Deployment cancelled"
            exit 1
        fi
    fi
    
    kubectl apply -f k8s/secrets.yaml
    
    log_success "Secrets applied"
}

# Apply configuration
apply_config() {
    log_info "Applying configuration..."
    
    kubectl apply -f k8s/configmap.yaml
    
    log_success "Configuration applied"
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."
    
    # Update image in deployment
    sed -i.bak "s|image: atlas-multi-agent-system:v4-enhanced|image: $DOCKER_IMAGE|g" k8s/deployment.yaml
    
    # Apply deployment
    kubectl apply -f k8s/deployment.yaml
    
    # Restore original deployment file
    mv k8s/deployment.yaml.bak k8s/deployment.yaml
    
    log_success "Application deployment applied"
}

# Apply services and networking
apply_services() {
    log_info "Applying services and networking..."
    
    kubectl apply -f k8s/service.yaml
    
    log_success "Services and networking applied"
}

# Apply autoscaling configuration
apply_autoscaling() {
    log_info "Applying autoscaling configuration..."
    
    kubectl apply -f k8s/autoscaling.yaml
    
    log_success "Autoscaling configuration applied"
}

# Deploy monitoring
deploy_monitoring() {
    log_info "Deploying monitoring stack..."
    
    kubectl apply -f k8s/monitoring.yaml
    
    log_success "Monitoring stack deployed"
}

# Wait for deployment to be ready
wait_for_deployment() {
    log_info "Waiting for deployment to be ready..."
    
    # Wait for deployment to be ready
    kubectl rollout status deployment/$APP_NAME -n $NAMESPACE --timeout=600s
    
    # Wait for pods to be ready
    kubectl wait --for=condition=Ready pods -l app=$APP_NAME -n $NAMESPACE --timeout=300s
    
    log_success "Deployment is ready"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check pod status
    log_info "Pod status:"
    kubectl get pods -n $NAMESPACE -l app=$APP_NAME
    
    # Check service status
    log_info "Service status:"
    kubectl get services -n $NAMESPACE
    
    # Check HPA status
    log_info "HPA status:"
    kubectl get hpa -n $NAMESPACE
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    kubectl port-forward -n $NAMESPACE service/atlas-service 8080:80 &
    PORT_FORWARD_PID=$!
    
    sleep 5
    
    if curl -f http://localhost:8080/api/v1/monitoring/health > /dev/null 2>&1; then
        log_success "Health check passed"
    else
        log_warning "Health check failed - this might be normal during startup"
    fi
    
    kill $PORT_FORWARD_PID 2>/dev/null || true
    
    log_success "Deployment verification completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    kill $PORT_FORWARD_PID 2>/dev/null || true
}

# Main deployment function
main() {
    log_info "Starting A.T.L.A.S. Multi-Agent System deployment..."
    log_info "Version: $VERSION"
    log_info "Namespace: $NAMESPACE"
    log_info "Docker Image: $DOCKER_IMAGE"
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Run deployment steps
    check_prerequisites
    build_image
    push_image
    setup_namespace
    apply_secrets
    apply_config
    deploy_application
    apply_services
    apply_autoscaling
    deploy_monitoring
    wait_for_deployment
    verify_deployment
    
    log_success "🎉 A.T.L.A.S. Multi-Agent System deployment completed successfully!"
    log_info "Access the API at: https://atlas-api.yourdomain.com"
    log_info "Monitor metrics at: http://prometheus.yourdomain.com"
    log_info "View logs with: kubectl logs -n $NAMESPACE -l app=$APP_NAME -f"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "build")
        check_prerequisites
        build_image
        ;;
    "push")
        check_prerequisites
        push_image
        ;;
    "update")
        check_prerequisites
        build_image
        push_image
        deploy_application
        wait_for_deployment
        verify_deployment
        ;;
    "rollback")
        log_info "Rolling back deployment..."
        kubectl rollout undo deployment/$APP_NAME -n $NAMESPACE
        kubectl rollout status deployment/$APP_NAME -n $NAMESPACE
        log_success "Rollback completed"
        ;;
    "status")
        kubectl get all -n $NAMESPACE
        ;;
    "logs")
        kubectl logs -n $NAMESPACE -l app=$APP_NAME -f
        ;;
    "cleanup")
        log_warning "This will delete all resources in namespace $NAMESPACE"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kubectl delete namespace $NAMESPACE
            log_success "Cleanup completed"
        else
            log_info "Cleanup cancelled"
        fi
        ;;
    *)
        echo "Usage: $0 {deploy|build|push|update|rollback|status|logs|cleanup}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment (default)"
        echo "  build    - Build Docker image only"
        echo "  push     - Push Docker image only"
        echo "  update   - Build, push, and update deployment"
        echo "  rollback - Rollback to previous deployment"
        echo "  status   - Show deployment status"
        echo "  logs     - Show application logs"
        echo "  cleanup  - Delete all resources"
        exit 1
        ;;
esac
