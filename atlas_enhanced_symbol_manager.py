"""
A.T.L.A.S. Enhanced Symbol Management System
Intelligent symbol selection, prioritization, and management for 250+ symbol scanning
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import random

from sp500_symbols import get_sp500_symbols, get_core_sp500_symbols, get_high_volume_symbols

logger = logging.getLogger(__name__)


class SymbolTier(Enum):
    """Symbol priority tiers for multi-tier scanning"""
    ULTRA_PRIORITY = 1  # Top 25 symbols - 2 second intervals
    HIGH_PRIORITY = 2   # Next 75 symbols - 5 second intervals  
    MEDIUM_PRIORITY = 3 # Next 100 symbols - 15 second intervals
    LOW_PRIORITY = 4    # Remaining symbols - 60 second intervals


@dataclass
class SymbolMetrics:
    """Metrics for symbol prioritization"""
    symbol: str
    volume_rank: int = 0
    volatility: float = 0.0
    market_cap_rank: int = 0
    pattern_history_score: float = 0.0
    news_sentiment_score: float = 0.0
    sector_momentum: float = 0.0
    liquidity_score: float = 0.0
    last_signal_time: Optional[datetime] = None
    signal_success_rate: float = 0.0
    tier: SymbolTier = SymbolTier.LOW_PRIORITY


class EnhancedSymbolManager:
    """Advanced symbol management with AI-driven prioritization"""
    
    def __init__(self, max_symbols: int = 250):
        self.max_symbols = max_symbols
        self.symbol_metrics: Dict[str, SymbolMetrics] = {}
        self.tier_allocations = {
            SymbolTier.ULTRA_PRIORITY: 25,   # 25 symbols
            SymbolTier.HIGH_PRIORITY: 75,    # 75 symbols
            SymbolTier.MEDIUM_PRIORITY: 100, # 100 symbols
            SymbolTier.LOW_PRIORITY: 50      # 50 symbols
        }
        
        # Symbol pools
        self.all_symbols = set()
        self.active_symbols = set()
        self.tier_symbols: Dict[SymbolTier, List[str]] = {
            tier: [] for tier in SymbolTier
        }
        
        # Performance tracking
        self.symbol_performance: Dict[str, Dict[str, Any]] = {}
        self.last_rebalance = datetime.now()
        self.rebalance_interval = timedelta(hours=1)  # Rebalance every hour
        
        # Market data cache
        self.market_data_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5 minutes
        
        logger.info(f"Enhanced Symbol Manager initialized for {max_symbols} symbols")
    
    async def initialize(self) -> bool:
        """Initialize symbol manager with comprehensive symbol universe"""
        try:
            # Load comprehensive symbol universe
            await self._load_symbol_universe()
            
            # Calculate initial metrics
            await self._calculate_initial_metrics()
            
            # Perform initial tier assignment
            await self._assign_symbol_tiers()
            
            logger.info(f"Symbol manager initialized with {len(self.active_symbols)} active symbols")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize symbol manager: {e}")
            return False
    
    async def _load_symbol_universe(self):
        """Load comprehensive symbol universe from multiple sources including expanded universe"""
        try:
            logger.info("🚀 Loading A.T.L.A.S. v5.0 Expanded Stock Universe...")

            # Try to load expanded universe first
            try:
                from atlas_expanded_universe import initialize_expanded_universe, get_expanded_symbols

                # Initialize expanded universe with quality filtering
                universe_summary = await initialize_expanded_universe()
                expanded_symbols = set(get_expanded_symbols())

                if expanded_symbols and len(expanded_symbols) > 500:
                    self.all_symbols = expanded_symbols
                    logger.info(f"✅ Loaded expanded universe: {len(self.all_symbols)} symbols")
                    logger.info(f"📊 Universe summary: {universe_summary.get('total_symbols', 0)} total, "
                              f"{universe_summary.get('sp500_symbols', 0)} S&P 500")
                    return
                else:
                    logger.warning("⚠️ Expanded universe too small, falling back to traditional sources")

            except ImportError:
                logger.warning("⚠️ Expanded universe module not available, using traditional sources")
            except Exception as e:
                logger.warning(f"⚠️ Failed to load expanded universe: {e}, using traditional sources")

            # Fallback to traditional symbol loading
            logger.info("📈 Loading traditional symbol universe...")

            # Start with S&P 500 as base
            sp500_symbols = set(get_sp500_symbols())

            # Add high-volume symbols
            high_volume = set(get_high_volume_symbols())

            # Add core symbols
            core_symbols = set(get_core_sp500_symbols())

            # Combine all sources
            self.all_symbols = sp500_symbols.union(high_volume).union(core_symbols)

            # Add popular ETFs and indices
            popular_etfs = {
                'SPY', 'QQQ', 'IWM', 'VTI', 'VOO', 'VEA', 'VWO', 'AGG', 'BND',
                'GLD', 'SLV', 'USO', 'TLT', 'HYG', 'LQD', 'EEM', 'FXI', 'EWJ',
                'XLF', 'XLK', 'XLE', 'XLV', 'XLI', 'XLP', 'XLY', 'XLU', 'XLRE'
            }
            self.all_symbols.update(popular_etfs)

            # Add trending stocks (would be dynamically updated in production)
            trending_stocks = {
                'NVDA', 'AMD', 'TSLA', 'PLTR', 'COIN', 'RBLX', 'HOOD', 'SOFI',
                'RIVN', 'LCID', 'F', 'GM', 'NKLA', 'SPCE', 'ARKK', 'SQQQ', 'TQQQ'
            }
            self.all_symbols.update(trending_stocks)

            # Add additional multi-cap symbols for broader coverage
            additional_symbols = {
                # Mid-cap growth stocks
                'DKNG', 'PENN', 'MGM', 'WYNN', 'CHWY', 'ETSY', 'PINS', 'ROKU', 'SPOT',
                'ZM', 'DOCU', 'OKTA', 'CRWD', 'ZS', 'NET', 'DDOG', 'SNOW', 'MDB',
                # Small-cap opportunities
                'AMC', 'GME', 'BB', 'NOK', 'MVIS', 'CLOV', 'WISH', 'GOEV', 'RIDE',
                # Biotech growth
                'MRNA', 'BNTX', 'NVAX', 'INO', 'OCGN', 'SRNE', 'TNXP', 'JAGX',
                # Clean energy
                'NIO', 'XPEV', 'LI', 'FSR', 'PLUG', 'FCEL', 'BLDP', 'BE', 'CLSK',
                # Fintech
                'AFRM', 'UPST', 'LC', 'STNE', 'PAGS', 'NU', 'MELI', 'OPEN', 'RDFN'
            }
            self.all_symbols.update(additional_symbols)

            logger.info(f"✅ Loaded traditional universe: {len(self.all_symbols)} symbols from multiple sources")

        except Exception as e:
            logger.error(f"❌ Error loading symbol universe: {e}")
            # Fallback to core symbols
            self.all_symbols = set(get_core_sp500_symbols()[:100])
            logger.info(f"🔄 Fallback to core symbols: {len(self.all_symbols)} symbols")
    
    async def _calculate_initial_metrics(self):
        """Calculate initial metrics for all symbols"""
        try:
            high_volume_symbols = set(get_high_volume_symbols())
            core_symbols = set(get_core_sp500_symbols())
            
            for i, symbol in enumerate(self.all_symbols):
                # Create initial metrics
                metrics = SymbolMetrics(symbol=symbol)
                
                # Volume ranking (higher is better)
                if symbol in high_volume_symbols:
                    metrics.volume_rank = high_volume_symbols.index(symbol) + 1
                else:
                    metrics.volume_rank = 100  # Default for unknown symbols
                
                # Market cap ranking (approximate based on symbol lists)
                if symbol in core_symbols:
                    metrics.market_cap_rank = core_symbols.index(symbol) + 1
                else:
                    metrics.market_cap_rank = 200
                
                # Initial volatility estimate (would be calculated from real data)
                metrics.volatility = random.uniform(0.01, 0.05)  # Placeholder
                
                # Liquidity score (based on known liquid symbols)
                if symbol in high_volume_symbols[:30]:
                    metrics.liquidity_score = 1.0
                elif symbol in core_symbols[:100]:
                    metrics.liquidity_score = 0.8
                else:
                    metrics.liquidity_score = 0.5
                
                # Pattern history score (placeholder - would be from historical analysis)
                metrics.pattern_history_score = random.uniform(0.3, 0.9)
                
                # Sector momentum (placeholder)
                metrics.sector_momentum = random.uniform(-0.2, 0.3)
                
                self.symbol_metrics[symbol] = metrics
            
            logger.info(f"Calculated initial metrics for {len(self.symbol_metrics)} symbols")
            
        except Exception as e:
            logger.error(f"Error calculating initial metrics: {e}")
    
    async def _assign_symbol_tiers(self):
        """Assign symbols to tiers based on comprehensive scoring"""
        try:
            # Calculate composite scores for all symbols
            scored_symbols = []
            
            for symbol, metrics in self.symbol_metrics.items():
                # Composite scoring algorithm
                score = (
                    (1.0 / max(metrics.volume_rank, 1)) * 0.25 +           # Volume weight
                    (1.0 / max(metrics.market_cap_rank, 1)) * 0.20 +       # Market cap weight
                    metrics.volatility * 0.15 +                            # Volatility weight
                    metrics.pattern_history_score * 0.20 +                 # Pattern success weight
                    metrics.liquidity_score * 0.10 +                       # Liquidity weight
                    max(metrics.sector_momentum, 0) * 0.10                 # Sector momentum weight
                )
                
                scored_symbols.append((symbol, score, metrics))
            
            # Sort by score (highest first)
            scored_symbols.sort(key=lambda x: x[1], reverse=True)
            
            # Assign to tiers
            current_index = 0
            for tier in [SymbolTier.ULTRA_PRIORITY, SymbolTier.HIGH_PRIORITY, 
                        SymbolTier.MEDIUM_PRIORITY, SymbolTier.LOW_PRIORITY]:
                
                tier_size = self.tier_allocations[tier]
                tier_symbols = []
                
                for i in range(current_index, min(current_index + tier_size, len(scored_symbols))):
                    symbol, score, metrics = scored_symbols[i]
                    metrics.tier = tier
                    tier_symbols.append(symbol)
                    self.active_symbols.add(symbol)
                
                self.tier_symbols[tier] = tier_symbols
                current_index += tier_size
                
                logger.info(f"Assigned {len(tier_symbols)} symbols to {tier.name}")
            
            # Log tier assignments
            for tier, symbols in self.tier_symbols.items():
                logger.info(f"{tier.name}: {len(symbols)} symbols - {symbols[:5]}...")
            
        except Exception as e:
            logger.error(f"Error assigning symbol tiers: {e}")
    
    def get_symbols_by_tier(self, tier: SymbolTier) -> List[str]:
        """Get symbols for specific tier"""
        return self.tier_symbols.get(tier, [])
    
    def get_all_active_symbols(self) -> List[str]:
        """Get all active symbols across all tiers"""
        return list(self.active_symbols)
    
    def get_symbol_tier(self, symbol: str) -> Optional[SymbolTier]:
        """Get tier for specific symbol"""
        metrics = self.symbol_metrics.get(symbol)
        return metrics.tier if metrics else None
    
    async def update_symbol_performance(self, symbol: str, pattern_found: bool, 
                                      confidence: float, success: bool = None):
        """Update symbol performance metrics"""
        try:
            if symbol not in self.symbol_performance:
                self.symbol_performance[symbol] = {
                    'total_scans': 0,
                    'patterns_found': 0,
                    'successful_signals': 0,
                    'last_signal': None,
                    'confidence_sum': 0.0
                }
            
            perf = self.symbol_performance[symbol]
            perf['total_scans'] += 1
            
            if pattern_found:
                perf['patterns_found'] += 1
                perf['confidence_sum'] += confidence
                perf['last_signal'] = datetime.now()
                
                if success is not None and success:
                    perf['successful_signals'] += 1
            
            # Update symbol metrics
            if symbol in self.symbol_metrics:
                metrics = self.symbol_metrics[symbol]
                if perf['patterns_found'] > 0:
                    metrics.signal_success_rate = perf['successful_signals'] / perf['patterns_found']
                    metrics.pattern_history_score = min(perf['confidence_sum'] / perf['patterns_found'], 1.0)
                    metrics.last_signal_time = perf['last_signal']
            
        except Exception as e:
            logger.error(f"Error updating symbol performance for {symbol}: {e}")
    
    async def rebalance_tiers(self) -> bool:
        """Rebalance symbol tiers based on performance"""
        try:
            if datetime.now() - self.last_rebalance < self.rebalance_interval:
                return False
            
            logger.info("Starting symbol tier rebalancing...")
            
            # Recalculate metrics based on performance
            await self._update_metrics_from_performance()
            
            # Reassign tiers
            await self._assign_symbol_tiers()
            
            self.last_rebalance = datetime.now()
            logger.info("Symbol tier rebalancing completed")
            return True
            
        except Exception as e:
            logger.error(f"Error during tier rebalancing: {e}")
            return False
    
    async def _update_metrics_from_performance(self):
        """Update symbol metrics based on recent performance"""
        try:
            for symbol, perf in self.symbol_performance.items():
                if symbol in self.symbol_metrics:
                    metrics = self.symbol_metrics[symbol]
                    
                    # Update pattern history score based on actual performance
                    if perf['patterns_found'] > 0:
                        avg_confidence = perf['confidence_sum'] / perf['patterns_found']
                        success_rate = perf['successful_signals'] / perf['patterns_found']
                        metrics.pattern_history_score = (avg_confidence + success_rate) / 2
                    
                    # Boost score for recently active symbols
                    if perf['last_signal'] and (datetime.now() - perf['last_signal']).seconds < 3600:
                        metrics.pattern_history_score *= 1.2  # 20% boost for recent activity
            
        except Exception as e:
            logger.error(f"Error updating metrics from performance: {e}")
    
    def get_tier_intervals(self) -> Dict[SymbolTier, int]:
        """Get scanning intervals for each tier"""
        return {
            SymbolTier.ULTRA_PRIORITY: 2,   # 2 seconds
            SymbolTier.HIGH_PRIORITY: 5,    # 5 seconds
            SymbolTier.MEDIUM_PRIORITY: 15, # 15 seconds
            SymbolTier.LOW_PRIORITY: 60     # 60 seconds
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get symbol manager statistics"""
        return {
            'total_symbols': len(self.all_symbols),
            'active_symbols': len(self.active_symbols),
            'tier_distribution': {tier.name: len(symbols) for tier, symbols in self.tier_symbols.items()},
            'last_rebalance': self.last_rebalance.isoformat(),
            'performance_tracked': len(self.symbol_performance)
        }
