#!/usr/bin/env python3
"""
Test script to verify Atlas V5 Enhanced connectivity restoration and full functionality
"""

import requests
import json
import time

def test_connectivity_restoration():
    """Test that all Atlas V5 Enhanced endpoints are accessible and functional"""
    
    base_url = "http://localhost:8002"
    
    print("🔧 Testing Atlas V5 Enhanced Connectivity Restoration")
    print("=" * 70)
    
    # Test cases for different endpoints
    test_cases = [
        {
            "name": "Main Web Interface",
            "method": "GET",
            "url": f"{base_url}/",
            "expected_status": 200,
            "check_content": ["Atlas", "Trading", "interface"]
        },
        {
            "name": "Health Check API",
            "method": "GET", 
            "url": f"{base_url}/api/v1/health",
            "expected_status": 200,
            "check_content": ["status"]
        },
        {
            "name": "Chat API",
            "method": "POST",
            "url": f"{base_url}/api/v1/chat",
            "data": {"message": "Hello Atlas", "conversation_id": "test_connectivity"},
            "expected_status": 200,
            "check_content": ["response"]
        },
        {
            "name": "Lee Method Signals API",
            "method": "GET",
            "url": f"{base_url}/api/v1/lee_method/signals",
            "expected_status": 200,
            "check_content": ["signals"]
        },
        {
            "name": "Data Status API",
            "method": "GET",
            "url": f"{base_url}/api/data-status",
            "expected_status": 200,
            "check_content": ["status"]
        },
        {
            "name": "Beginner Query Test",
            "method": "POST",
            "url": f"{base_url}/api/v1/chat",
            "data": {"message": "I'm a beginner, help me make $100 today", "conversation_id": "test_beginner"},
            "expected_status": 200,
            "check_content": ["response", "plan"]
        }
    ]
    
    results = []
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n📝 TEST {i}: {test['name']}")
        print("-" * 50)
        
        try:
            start_time = time.time()
            
            if test['method'] == 'GET':
                response = requests.get(test['url'], timeout=15)
            elif test['method'] == 'POST':
                response = requests.post(test['url'], json=test.get('data', {}), timeout=30)
            
            response_time = time.time() - start_time
            
            # Check status code
            status_ok = response.status_code == test['expected_status']
            print(f"Status Code: {response.status_code} {'✅' if status_ok else '❌'}")
            print(f"Response Time: {response_time:.2f}s")
            
            # Check content
            content_checks = []
            if test.get('check_content'):
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        content = response.json()
                        content_str = json.dumps(content).lower()
                    else:
                        content_str = response.text.lower()
                    
                    for check in test['check_content']:
                        found = check.lower() in content_str
                        content_checks.append(found)
                        print(f"Content Check '{check}': {'✅' if found else '❌'}")
                        
                except Exception as e:
                    print(f"Content Check Error: {e}")
                    content_checks = [False] * len(test['check_content'])
            
            # Overall result
            all_content_ok = all(content_checks) if content_checks else True
            test_passed = status_ok and all_content_ok
            
            print(f"Overall Result: {'✅ PASSED' if test_passed else '❌ FAILED'}")
            
            results.append({
                'name': test['name'],
                'passed': test_passed,
                'status_code': response.status_code,
                'response_time': response_time,
                'content_checks_passed': sum(content_checks) if content_checks else 0,
                'total_content_checks': len(content_checks) if content_checks else 0
            })
            
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append({
                'name': test['name'],
                'passed': False,
                'error': str(e)
            })
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 CONNECTIVITY RESTORATION TEST RESULTS")
    print("=" * 70)
    
    passed_tests = [r for r in results if r.get('passed', False)]
    total_tests = len(results)
    
    print(f"Overall Success Rate: {len(passed_tests)}/{total_tests} ({len(passed_tests)/total_tests*100:.1f}%)")
    
    if passed_tests:
        avg_response_time = sum(r.get('response_time', 0) for r in passed_tests) / len(passed_tests)
        print(f"Average Response Time: {avg_response_time:.2f} seconds")
    
    print("\n📋 Individual Test Results:")
    for result in results:
        if result.get('passed'):
            content_info = ""
            if result.get('total_content_checks', 0) > 0:
                content_info = f" ({result['content_checks_passed']}/{result['total_content_checks']} content checks)"
            print(f"✅ {result['name']}: {result['response_time']:.2f}s{content_info}")
        else:
            error_info = result.get('error', f"Status: {result.get('status_code', 'Unknown')}")
            print(f"❌ {result['name']}: {error_info}")
    
    # Final assessment
    print("\n" + "=" * 70)
    if len(passed_tests) >= total_tests * 0.8:  # 80% success rate
        print("🎉 CONNECTIVITY RESTORATION SUCCESSFUL!")
        print("✅ Atlas V5 Enhanced is fully accessible on port 8002")
        print("✅ All major endpoints are responding correctly")
        print("✅ Web interface is functional")
        print("✅ API endpoints are working")
        print("✅ Beginner Grok system is operational")
        
        print(f"\n🌐 System ready at: http://localhost:8002")
        print("🔧 Ready for integration improvements!")
        
    else:
        print("⚠️  CONNECTIVITY ISSUES REMAIN")
        print("Some endpoints are not responding correctly")
        print("Review the failed tests above")
    
    return len(passed_tests) >= total_tests * 0.8

if __name__ == "__main__":
    success = test_connectivity_restoration()
    exit(0 if success else 1)
