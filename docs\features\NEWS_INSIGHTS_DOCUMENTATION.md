# A.T.L.A.S. News Insights Module Documentation

## 📰 Overview

The A.T.L.A.S. News Insights Module provides real-time market-moving news analysis with functional progress tracking and optimized response flows. It integrates seamlessly with the existing A.T.L.A.S. AI core and maintains the 35%+ returns performance standard.

## 🏗️ Architecture

### Core Components

1. **News Insights Engine** (`atlas_news_insights_engine.py`)
   - Real-time news ingestion from multiple sources
   - Advanced sentiment analysis with DistilBERT
   - Market impact scoring and volatility prediction
   - Theme clustering and narrative detection

2. **Progress Tracking Integration**
   - Functional progress bars for all news operations
   - Real-time WebSocket updates
   - Optimized response flows (fast path vs full pipeline)

3. **AI Core Integration**
   - Intent detection for news queries
   - Response routing optimization
   - Conversational context awareness

4. **API Endpoints**
   - RESTful news analysis endpoints
   - Real-time alert system
   - WebSocket integration for live updates

## 🔧 Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# News Insights Configuration
NEWS_INSIGHTS_ENABLED=true

# Financial News APIs
ALPHA_VANTAGE_NEWS_API_KEY=your_alpha_vantage_key
BLOOMBERG_API_KEY=your_bloomberg_key
REUTERS_API_KEY=your_reuters_key

# Economic Data APIs
FRED_API_KEY=your_fred_key
TRADING_ECONOMICS_API_KEY=your_trading_economics_key

# SEC EDGAR API Configuration
SEC_EDGAR_USER_AGENT="ATLAS-Trading-System <EMAIL>"

# News Processing Configuration
NEWS_CACHE_TTL=300  # 5 minutes
NEWS_SENTIMENT_THRESHOLD=0.8
NEWS_VOLUME_THRESHOLD=10  # 10x normal volume
NEWS_IMPACT_THRESHOLD=0.7

# Vector Database Configuration
VECTOR_DB_ENABLED=true
PINECONE_API_KEY=your_pinecone_key
PINECONE_ENVIRONMENT=us-west1-gcp
WEAVIATE_URL=your_weaviate_url

# News Alert Configuration
ALERT_COOLDOWN_SECONDS=300  # 5 minutes
MAX_ALERTS_PER_HOUR=20
```

### Configuration Options

| Setting | Default | Description |
|---------|---------|-------------|
| `NEWS_INSIGHTS_ENABLED` | `true` | Enable/disable news insights module |
| `NEWS_CACHE_TTL` | `300` | Cache time-to-live in seconds |
| `NEWS_SENTIMENT_THRESHOLD` | `0.8` | Threshold for high-impact sentiment alerts |
| `NEWS_VOLUME_THRESHOLD` | `10` | Volume multiplier for anomaly detection |
| `NEWS_IMPACT_THRESHOLD` | `0.7` | Market impact threshold for alerts |
| `ALERT_COOLDOWN_SECONDS` | `300` | Minimum time between similar alerts |
| `MAX_ALERTS_PER_HOUR` | `20` | Maximum alerts per hour limit |

## 🚀 Quick Start

### 1. Installation

The News Insights module is automatically included with A.T.L.A.S. v5.0. Ensure you have the required dependencies:

```bash
pip install transformers sentence-transformers scikit-learn asyncpg redis
```

### 2. Basic Usage

```python
from atlas_orchestrator import AtlasOrchestrator

# Initialize orchestrator (includes news insights)
orchestrator = AtlasOrchestrator()
await orchestrator.initialize()

# Get news insights for specific symbols
news_data = await orchestrator.get_news_insights(["AAPL", "MSFT"])

# Analyze news sentiment
sentiment_data = await orchestrator.analyze_news_sentiment(["AAPL"])

# Get market news alerts
alerts = await orchestrator.get_market_news_alerts(severity="high")
```

### 3. Conversational Interface

The module integrates with A.T.L.A.S. conversational AI:

```
User: "Any market news today?"
A.T.L.A.S.: [Fast path - cached headlines in <2 seconds]

User: "Analyze comprehensive news sentiment for AAPL"
A.T.L.A.S.: [Full pipeline - multi-source analysis in <10 seconds]
```

## 📡 API Endpoints

### News Alerts

```http
GET /api/v1/news/alerts?timeframe=1h&severity=medium&symbols=AAPL,MSFT
```

**Response:**
```json
{
  "success": true,
  "alerts": [
    {
      "id": "alert_001",
      "severity": "high",
      "title": "Federal Reserve Policy Update",
      "description": "Latest Fed announcement may impact market sentiment",
      "symbols": ["SPY", "QQQ"],
      "sentiment_score": 0.8,
      "market_impact": 0.9,
      "timestamp": "2025-07-18T14:30:00Z"
    }
  ],
  "alert_count": 1,
  "timestamp": "2025-07-18T14:30:00Z"
}
```

### News Summary

```http
POST /api/v1/news/summary
Content-Type: application/json

{
  "symbols": ["AAPL", "MSFT"],
  "date_range": "1d",
  "categories": ["Fed_Policy", "Earnings_Surprise"],
  "analysis_type": "comprehensive"
}
```

### News Sentiment Analysis

```http
GET /api/v1/news/sentiment/AAPL?timeframe=1d
```

**Response:**
```json
{
  "success": true,
  "symbol": "AAPL",
  "sentiment_analysis": {
    "overall_sentiment": "positive",
    "sentiment_score": 0.65,
    "confidence": 0.82,
    "articles_analyzed": 15,
    "high_impact_articles": 3
  },
  "timestamp": "2025-07-18T14:30:00Z"
}
```

### Comprehensive News Insights

```http
GET /api/v1/news/insights?symbols=AAPL,MSFT&analysis_type=comprehensive
```

### News Engine Status

```http
GET /api/v1/news/status
```

## 🔄 Data Sources

### Primary Sources

1. **Financial Modeling Prep (FMP)**
   - Real-time stock news
   - Company-specific updates
   - Earnings announcements

2. **Alpha Vantage**
   - Market news feed
   - Sentiment scores
   - Economic indicators

3. **SEC EDGAR**
   - Regulatory filings (8-K, 10-K, 10-Q)
   - Real-time filing alerts
   - Compliance updates

### Secondary Sources

4. **Federal Reserve Economic Data (FRED)**
   - Economic calendar events
   - Policy announcements
   - Interest rate updates

5. **Social Media Monitoring**
   - Twitter/X verified financial accounts
   - Reddit r/investing sentiment
   - Market buzz tracking

### Data Processing Pipeline

```mermaid
graph LR
    A[Data Sources] --> B[Ingestion Pipeline]
    B --> C[Sentiment Analysis]
    C --> D[Market Impact Scoring]
    D --> E[Theme Clustering]
    E --> F[Alert Generation]
    F --> G[Real-time Delivery]
```

## 📊 Progress Tracking

### Operation Types

The module includes specialized progress tracking for:

- `NEWS_INGESTION`: Multi-source data collection
- `NEWS_SENTIMENT_ANALYSIS`: DistilBERT processing
- `NEWS_IMPACT_SCORING`: Market correlation analysis
- `NEWS_THEME_CLUSTERING`: Narrative detection
- `NEWS_ALERT_GENERATION`: Real-time alert creation
- `NEWS_COMPREHENSIVE_ANALYSIS`: Full pipeline execution

### Progress Stages

Each operation shows real-time progress through functional stages:

1. **Data Ingestion** (25%): "Connecting to News APIs 🔗"
2. **Processing** (50%): "Analyzing Sentiment 🤖"
3. **Analysis** (75%): "Computing Impact Scores 📊"
4. **Completion** (100%): "Generating Insights ✅"

## 🎯 Response Flow Optimization

### Fast Path (< 2 seconds)
- Simple news queries: "any news today?"
- Cached headlines and status checks
- Basic market updates

### Full Pipeline (< 10 seconds)
- Complex analysis: "comprehensive sentiment analysis"
- Multi-source correlation
- Detailed impact scoring
- Theme clustering and trends

### Routing Logic

```python
# Simple queries use fast path
if is_simple_news_query(message):
    return cached_headlines()

# Complex queries use full pipeline
else:
    return comprehensive_analysis()
```

## 🔧 Maintenance Procedures

### Daily Maintenance

1. **Cache Cleanup**
   ```bash
   # Clear expired news cache
   redis-cli FLUSHDB
   ```

2. **Database Optimization**
   ```sql
   -- Optimize news articles table
   VACUUM ANALYZE news_articles;
   
   -- Clean old articles (>2 years)
   DELETE FROM news_articles 
   WHERE published_at < NOW() - INTERVAL '2 years';
   ```

### Weekly Maintenance

1. **API Key Rotation**
   - Update API keys in environment variables
   - Test all data source connections
   - Verify rate limit compliance

2. **Model Performance Review**
   - Check sentiment analysis accuracy
   - Review false positive/negative rates
   - Update model if needed

### Monthly Maintenance

1. **Performance Optimization**
   - Analyze response time metrics
   - Optimize database queries
   - Review caching strategies

2. **Data Source Evaluation**
   - Assess source reliability scores
   - Add/remove data sources as needed
   - Update source weighting algorithms

## 🚨 Troubleshooting

### Common Issues

#### 1. News Engine Not Available
**Symptoms:** API returns "News Insights engine not available"
**Solution:**
```bash
# Check engine status
curl http://localhost:8001/api/v1/news/status

# Restart orchestrator
python atlas_server.py
```

#### 2. Slow Response Times
**Symptoms:** Queries taking >10 seconds
**Solutions:**
- Check Redis cache connection
- Verify API rate limits
- Review database query performance

#### 3. Sentiment Analysis Errors
**Symptoms:** Incorrect sentiment scores
**Solutions:**
- Verify DistilBERT model loading
- Check text preprocessing
- Review confidence thresholds

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `NEWS_001` | API key missing | Add required API keys to .env |
| `NEWS_002` | Rate limit exceeded | Implement exponential backoff |
| `NEWS_003` | Database connection failed | Check PostgreSQL connection |
| `NEWS_004` | Model loading failed | Verify transformers installation |
| `NEWS_005` | Cache connection failed | Check Redis configuration |

## 📈 Performance Metrics

### Success Criteria

- **News sentiment accuracy**: >85% correlation with actual market movements
- **Alert latency**: <30 seconds from news publication to user notification
- **API response times**: <2 seconds for cached queries, <10 seconds for complex analysis
- **Zero downtime**: During market hours (8:30 AM - 3:00 PM CT)
- **Integration success**: Seamless with existing Lee Method scans and options analysis

### Monitoring

```python
# Check system health
status = await orchestrator.get_news_insights_status()
print(f"Engine Status: {status['engine_status']}")
print(f"Data Sources: {status['data_sources']}")
print(f"Capabilities: {status['capabilities']}")
```

## 🔐 Security Considerations

### API Key Management
- Store all API keys in environment variables
- Rotate keys regularly (monthly)
- Use different keys for development/production

### Data Privacy
- No personal data collection
- Anonymized social media monitoring
- Compliance with financial data regulations

### Rate Limiting
- Implement exponential backoff for all APIs
- Monitor usage against provider limits
- Graceful degradation when limits exceeded

## 📚 Usage Examples

### Example 1: Basic News Query
```python
# Simple news check (fast path)
response = await ai_engine.process_message(
    message="Any market news today?",
    session_id="user_123"
)
# Returns cached headlines in <2 seconds
```

### Example 2: Complex Analysis
```python
# Comprehensive analysis (full pipeline)
response = await ai_engine.process_message(
    message="Analyze comprehensive news sentiment impact for tech stocks",
    session_id="user_123"
)
# Returns detailed multi-source analysis in <10 seconds
```

### Example 3: Symbol-Specific News
```python
# Get news for specific symbols
news_data = await orchestrator.get_news_insights(
    symbols=["AAPL", "MSFT", "GOOGL"],
    analysis_type="comprehensive"
)

# Analyze sentiment
sentiment_data = await orchestrator.analyze_news_sentiment(
    symbols=["AAPL"]
)
```

## 🎯 Integration with Existing A.T.L.A.S. Features

### Lee Method Scanner
- News sentiment influences pattern strength scoring
- Breaking news alerts trigger additional scans
- Economic calendar events affect scan timing

### Options Analysis
- News impact affects volatility predictions
- Earnings announcements trigger options flow analysis
- Fed policy news influences interest rate models

### Risk Management
- High-impact news triggers risk reassessment
- Geopolitical events affect position sizing
- Market sentiment influences portfolio allocation

---

**📞 Support**: For technical support, contact the A.T.L.A.S. development team or refer to the main system documentation.

**🔄 Updates**: This documentation is updated with each A.T.L.A.S. release. Check the version number and changelog for recent changes.
