"""
A.T.L.A.S. v5.0 Grok Chatbot Interaction Monitor
Real-time monitoring and quality assessment of Grok-powered chatbot responses
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import time

logger = logging.getLogger(__name__)

class ResponseQuality(Enum):
    """Response quality ratings"""
    EXCELLENT = "excellent"
    GOOD = "good"
    ADEQUATE = "adequate"
    POOR = "poor"
    FAILED = "failed"

class DataIntegration(Enum):
    """Data integration assessment"""
    FULL_INTEGRATION = "full_integration"      # Uses real market data, expanded universe
    PARTIAL_INTEGRATION = "partial_integration" # Uses some real data
    LIMITED_INTEGRATION = "limited_integration" # Minimal real data usage
    NO_INTEGRATION = "no_integration"          # Generic responses only

@dataclass
class InteractionAssessment:
    """Assessment of a single chatbot interaction"""
    timestamp: str
    user_query: str
    bot_response: str
    response_time: float
    technical_accuracy: ResponseQuality
    communication_style: ResponseQuality
    data_integration: DataIntegration
    market_data_usage: bool
    expanded_universe_usage: bool
    actionable_insights: bool
    overall_rating: ResponseQuality
    improvement_notes: List[str]
    confidence_score: float

class AtlasGrokMonitor:
    """Monitor and assess Grok chatbot interactions"""
    
    def __init__(self):
        self.is_monitoring = False
        self.interaction_history: List[InteractionAssessment] = []
        self.system_status = {}
        self.quality_standards = {
            'min_confidence_score': 0.7,
            'max_response_time': 10.0,
            'require_market_data': True,
            'require_actionable_insights': True
        }
        
    async def start_monitoring(self):
        """Start monitoring Grok chatbot interactions"""
        if self.is_monitoring:
            logger.warning("Monitor already running")
            return
            
        self.is_monitoring = True
        logger.info("🔍 Starting Grok chatbot interaction monitoring...")
        
        # Get current system status
        await self._update_system_status()
        
        print("=" * 80)
        print("🤖 A.T.L.A.S. v5.0 GROK CHATBOT MONITOR ACTIVE")
        print("=" * 80)
        print(f"Monitor Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"System Status: {self.system_status.get('system_health', 0):.0f}% Health")
        print(f"Expanded Universe: {self.system_status.get('total_symbols', 0)} symbols")
        print(f"Quality Standards: {self.quality_standards}")
        
        print("\n📊 MONITORING OBJECTIVES:")
        print("• Technical Accuracy: Factually correct using real market data")
        print("• Communication Style: Professional yet accessible language")
        print("• Data Integration: Proper use of FMP, Alpaca, expanded universe")
        print("• Actionable Insights: Practical trading recommendations")
        
        print("\n🎯 READY TO MONITOR INTERACTIONS")
        print("=" * 80)
        
        # Start monitoring loop
        await self._monitoring_loop()
    
    async def _update_system_status(self):
        """Update current system status"""
        try:
            from launch_atlas_v5 import get_system_status
            self.system_status = get_system_status()
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            self.system_status = {'system_health': 0, 'total_symbols': 0}
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # In a real implementation, this would capture actual chatbot interactions
                # For now, we'll simulate the monitoring framework
                await asyncio.sleep(1)
                
                # Check for new interactions (placeholder)
                # In production, this would integrate with the actual chatbot interface
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(5)
    
    async def assess_interaction(self, user_query: str, bot_response: str, 
                               response_time: float) -> InteractionAssessment:
        """Assess a single chatbot interaction"""
        
        start_time = time.time()
        
        # Analyze technical accuracy
        technical_accuracy = await self._assess_technical_accuracy(user_query, bot_response)
        
        # Analyze communication style
        communication_style = await self._assess_communication_style(bot_response)
        
        # Analyze data integration
        data_integration = await self._assess_data_integration(bot_response)
        
        # Check market data usage
        market_data_usage = await self._check_market_data_usage(bot_response)
        
        # Check expanded universe usage
        expanded_universe_usage = await self._check_expanded_universe_usage(bot_response)
        
        # Check for actionable insights
        actionable_insights = await self._check_actionable_insights(bot_response)
        
        # Calculate overall rating
        overall_rating = await self._calculate_overall_rating(
            technical_accuracy, communication_style, data_integration,
            market_data_usage, expanded_universe_usage, actionable_insights
        )
        
        # Generate improvement notes
        improvement_notes = await self._generate_improvement_notes(
            technical_accuracy, communication_style, data_integration,
            market_data_usage, expanded_universe_usage, actionable_insights
        )
        
        # Calculate confidence score
        confidence_score = await self._calculate_confidence_score(
            technical_accuracy, communication_style, data_integration
        )
        
        assessment = InteractionAssessment(
            timestamp=datetime.now().isoformat(),
            user_query=user_query,
            bot_response=bot_response,
            response_time=response_time,
            technical_accuracy=technical_accuracy,
            communication_style=communication_style,
            data_integration=data_integration,
            market_data_usage=market_data_usage,
            expanded_universe_usage=expanded_universe_usage,
            actionable_insights=actionable_insights,
            overall_rating=overall_rating,
            improvement_notes=improvement_notes,
            confidence_score=confidence_score
        )
        
        # Store assessment
        self.interaction_history.append(assessment)
        
        # Log assessment
        await self._log_assessment(assessment)
        
        return assessment
    
    async def _assess_technical_accuracy(self, query: str, response: str) -> ResponseQuality:
        """Assess technical accuracy of the response"""
        
        # Check for market data references
        market_indicators = ['price', 'volume', 'market cap', 'P/E ratio', 'earnings', 'revenue']
        has_market_data = any(indicator in response.lower() for indicator in market_indicators)
        
        # Check for specific stock symbols
        stock_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'META', 'AMZN']
        has_stock_symbols = any(symbol in response.upper() for symbol in stock_symbols)
        
        # Check for technical analysis terms
        technical_terms = ['support', 'resistance', 'trend', 'moving average', 'RSI', 'MACD']
        has_technical_analysis = any(term in response.lower() for term in technical_terms)
        
        # Score based on criteria
        score = 0
        if has_market_data:
            score += 1
        if has_stock_symbols:
            score += 1
        if has_technical_analysis:
            score += 1
        
        if score >= 3:
            return ResponseQuality.EXCELLENT
        elif score >= 2:
            return ResponseQuality.GOOD
        elif score >= 1:
            return ResponseQuality.ADEQUATE
        else:
            return ResponseQuality.POOR
    
    async def _assess_communication_style(self, response: str) -> ResponseQuality:
        """Assess communication style appropriateness"""
        
        # Check for professional terminology
        professional_terms = ['analysis', 'recommendation', 'strategy', 'risk', 'opportunity']
        has_professional_tone = any(term in response.lower() for term in professional_terms)
        
        # Check for accessibility (avoiding overly complex jargon)
        accessible_phrases = ['in simple terms', 'this means', 'for example', 'to put it simply']
        has_accessible_language = any(phrase in response.lower() for phrase in accessible_phrases)
        
        # Check response length (not too short, not too verbose)
        word_count = len(response.split())
        appropriate_length = 50 <= word_count <= 300
        
        score = 0
        if has_professional_tone:
            score += 1
        if has_accessible_language:
            score += 1
        if appropriate_length:
            score += 1
        
        if score >= 3:
            return ResponseQuality.EXCELLENT
        elif score >= 2:
            return ResponseQuality.GOOD
        elif score >= 1:
            return ResponseQuality.ADEQUATE
        else:
            return ResponseQuality.POOR
    
    async def _assess_data_integration(self, response: str) -> DataIntegration:
        """Assess level of data integration in response"""
        
        # Check for real-time data references
        realtime_indicators = ['current price', 'today', 'latest', 'real-time', 'live data']
        has_realtime = any(indicator in response.lower() for indicator in realtime_indicators)
        
        # Check for API data source mentions
        api_sources = ['FMP', 'Alpaca', 'Financial Modeling Prep', 'market data']
        has_api_sources = any(source in response for source in api_sources)
        
        # Check for expanded universe references
        universe_indicators = ['small-cap', 'mid-cap', 'micro-cap', 'expanded universe', 'broader market']
        has_expanded_universe = any(indicator in response.lower() for indicator in universe_indicators)
        
        integration_score = 0
        if has_realtime:
            integration_score += 2
        if has_api_sources:
            integration_score += 2
        if has_expanded_universe:
            integration_score += 1
        
        if integration_score >= 4:
            return DataIntegration.FULL_INTEGRATION
        elif integration_score >= 3:
            return DataIntegration.PARTIAL_INTEGRATION
        elif integration_score >= 1:
            return DataIntegration.LIMITED_INTEGRATION
        else:
            return DataIntegration.NO_INTEGRATION
    
    async def _check_market_data_usage(self, response: str) -> bool:
        """Check if response uses actual market data"""
        market_data_indicators = [
            'price', 'volume', 'market cap', 'P/E', 'earnings', 'revenue',
            'dividend', 'yield', 'beta', 'volatility', 'trading volume'
        ]
        return any(indicator in response.lower() for indicator in market_data_indicators)
    
    async def _check_expanded_universe_usage(self, response: str) -> bool:
        """Check if response references expanded universe capabilities"""
        universe_indicators = [
            'small-cap', 'mid-cap', 'micro-cap', 'growth stocks', 'value stocks',
            'biotech', 'fintech', 'clean energy', 'emerging companies'
        ]
        return any(indicator in response.lower() for indicator in universe_indicators)
    
    async def _check_actionable_insights(self, response: str) -> bool:
        """Check if response provides actionable trading insights"""
        actionable_phrases = [
            'recommend', 'suggest', 'consider', 'opportunity', 'strategy',
            'buy', 'sell', 'hold', 'watch', 'monitor', 'target price'
        ]
        return any(phrase in response.lower() for phrase in actionable_phrases)
    
    async def _calculate_overall_rating(self, technical_accuracy: ResponseQuality,
                                      communication_style: ResponseQuality,
                                      data_integration: DataIntegration,
                                      market_data_usage: bool,
                                      expanded_universe_usage: bool,
                                      actionable_insights: bool) -> ResponseQuality:
        """Calculate overall response quality rating"""
        
        # Convert quality ratings to scores
        quality_scores = {
            ResponseQuality.EXCELLENT: 4,
            ResponseQuality.GOOD: 3,
            ResponseQuality.ADEQUATE: 2,
            ResponseQuality.POOR: 1,
            ResponseQuality.FAILED: 0
        }
        
        integration_scores = {
            DataIntegration.FULL_INTEGRATION: 4,
            DataIntegration.PARTIAL_INTEGRATION: 3,
            DataIntegration.LIMITED_INTEGRATION: 2,
            DataIntegration.NO_INTEGRATION: 1
        }
        
        # Calculate weighted score
        total_score = (
            quality_scores[technical_accuracy] * 0.3 +
            quality_scores[communication_style] * 0.2 +
            integration_scores[data_integration] * 0.3 +
            (1 if market_data_usage else 0) * 0.1 +
            (1 if expanded_universe_usage else 0) * 0.05 +
            (1 if actionable_insights else 0) * 0.05
        )
        
        # Convert back to quality rating
        if total_score >= 3.5:
            return ResponseQuality.EXCELLENT
        elif total_score >= 2.5:
            return ResponseQuality.GOOD
        elif total_score >= 1.5:
            return ResponseQuality.ADEQUATE
        elif total_score >= 0.5:
            return ResponseQuality.POOR
        else:
            return ResponseQuality.FAILED
    
    async def _generate_improvement_notes(self, technical_accuracy: ResponseQuality,
                                        communication_style: ResponseQuality,
                                        data_integration: DataIntegration,
                                        market_data_usage: bool,
                                        expanded_universe_usage: bool,
                                        actionable_insights: bool) -> List[str]:
        """Generate improvement recommendations"""
        notes = []
        
        if technical_accuracy in [ResponseQuality.POOR, ResponseQuality.ADEQUATE]:
            notes.append("Improve technical accuracy with more specific market data")
        
        if communication_style in [ResponseQuality.POOR, ResponseQuality.ADEQUATE]:
            notes.append("Balance professional terminology with accessible language")
        
        if data_integration in [DataIntegration.NO_INTEGRATION, DataIntegration.LIMITED_INTEGRATION]:
            notes.append("Integrate more real-time market data from FMP/Alpaca APIs")
        
        if not market_data_usage:
            notes.append("Include specific market data points (prices, volumes, ratios)")
        
        if not expanded_universe_usage:
            notes.append("Leverage expanded universe capabilities (multi-cap coverage)")
        
        if not actionable_insights:
            notes.append("Provide more actionable trading recommendations")
        
        return notes
    
    async def _calculate_confidence_score(self, technical_accuracy: ResponseQuality,
                                        communication_style: ResponseQuality,
                                        data_integration: DataIntegration) -> float:
        """Calculate confidence score (0-1)"""
        
        quality_weights = {
            ResponseQuality.EXCELLENT: 1.0,
            ResponseQuality.GOOD: 0.8,
            ResponseQuality.ADEQUATE: 0.6,
            ResponseQuality.POOR: 0.4,
            ResponseQuality.FAILED: 0.0
        }
        
        integration_weights = {
            DataIntegration.FULL_INTEGRATION: 1.0,
            DataIntegration.PARTIAL_INTEGRATION: 0.75,
            DataIntegration.LIMITED_INTEGRATION: 0.5,
            DataIntegration.NO_INTEGRATION: 0.25
        }
        
        return (
            quality_weights[technical_accuracy] * 0.4 +
            quality_weights[communication_style] * 0.3 +
            integration_weights[data_integration] * 0.3
        )
    
    async def _log_assessment(self, assessment: InteractionAssessment):
        """Log the interaction assessment"""
        
        print(f"\n🔍 INTERACTION ASSESSMENT - {assessment.timestamp}")
        print("-" * 60)
        print(f"Query: {assessment.user_query[:100]}...")
        print(f"Response Time: {assessment.response_time:.2f}s")
        print(f"Technical Accuracy: {assessment.technical_accuracy.value.upper()}")
        print(f"Communication Style: {assessment.communication_style.value.upper()}")
        print(f"Data Integration: {assessment.data_integration.value.upper()}")
        print(f"Market Data Usage: {'✅' if assessment.market_data_usage else '❌'}")
        print(f"Expanded Universe: {'✅' if assessment.expanded_universe_usage else '❌'}")
        print(f"Actionable Insights: {'✅' if assessment.actionable_insights else '❌'}")
        print(f"Overall Rating: {assessment.overall_rating.value.upper()}")
        print(f"Confidence Score: {assessment.confidence_score:.2f}")
        
        if assessment.improvement_notes:
            print("Improvement Notes:")
            for note in assessment.improvement_notes:
                print(f"  • {note}")
        
        print("-" * 60)
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get summary of monitoring results"""
        if not self.interaction_history:
            return {"status": "no_interactions", "total_interactions": 0}
        
        total_interactions = len(self.interaction_history)
        avg_confidence = sum(a.confidence_score for a in self.interaction_history) / total_interactions
        
        # Count ratings
        rating_counts = {}
        for rating in ResponseQuality:
            rating_counts[rating.value] = sum(1 for a in self.interaction_history 
                                            if a.overall_rating == rating)
        
        return {
            "status": "active",
            "total_interactions": total_interactions,
            "average_confidence": avg_confidence,
            "rating_distribution": rating_counts,
            "system_health": self.system_status.get('system_health', 0),
            "monitoring_since": self.interaction_history[0].timestamp if self.interaction_history else None
        }

# Global monitor instance
grok_monitor = AtlasGrokMonitor()

async def start_grok_monitoring():
    """Start Grok chatbot monitoring"""
    await grok_monitor.start_monitoring()

async def assess_grok_interaction(user_query: str, bot_response: str, response_time: float):
    """Assess a Grok chatbot interaction"""
    return await grok_monitor.assess_interaction(user_query, bot_response, response_time)

def get_monitoring_summary():
    """Get monitoring summary"""
    return grok_monitor.get_monitoring_summary()
