"""
A.T.L.A.S. User Safety & Alert System
Comprehensive user warning system for data quality issues, stale data detection, and decision-making safeguards
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "INFO"
    WARNING = "WARNING"
    CRITICAL = "CRITICAL"
    EMERGENCY = "EMERGENCY"

class SafetyCheckType(Enum):
    """Types of safety checks"""
    DATA_QUALITY = "DATA_QUALITY"
    DATA_FRESHNESS = "DATA_FRESHNESS"
    CALCULATION_ACCURACY = "CALCULATION_ACCURACY"
    TRADING_RISK = "TRADING_RISK"
    SYSTEM_HEALTH = "SYSTEM_HEALTH"
    COMPLIANCE = "COMPLIANCE"

@dataclass
class SafetyAlert:
    """Individual safety alert"""
    alert_id: str
    alert_type: SafetyCheckType
    severity: AlertSeverity
    title: str
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    acknowledged: bool = False
    user_action_required: bool = False
    auto_dismiss_after: Optional[int] = None  # seconds

@dataclass
class DataQualityWarning:
    """Data quality warning for users"""
    symbol: str
    data_source: str
    issue_type: str
    severity: AlertSeverity
    message: str
    recommendation: str
    timestamp: datetime

class AtlasUserSafetySystem:
    """Comprehensive user safety and alert management system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Active alerts and warnings
        self.active_alerts: Dict[str, SafetyAlert] = {}
        self.alert_history: List[SafetyAlert] = []
        self.data_quality_warnings: Dict[str, DataQualityWarning] = {}
        
        # Safety thresholds
        self.safety_thresholds = {
            'data_staleness_warning': 60,      # 1 minute warning
            'data_staleness_critical': 300,    # 5 minutes critical
            'price_change_warning': 0.10,      # 10% price change warning
            'price_change_critical': 0.25,     # 25% price change critical
            'volume_anomaly_warning': 3.0,     # 3x normal volume warning
            'volume_anomaly_critical': 10.0,   # 10x normal volume critical
            'calculation_error_threshold': 0.05, # 5% calculation error threshold
            'system_response_warning': 5.0,    # 5 second response warning
            'system_response_critical': 15.0   # 15 second response critical
        }
        
        # User notification preferences
        self.notification_settings = {
            'show_data_quality_warnings': True,
            'show_calculation_warnings': True,
            'show_system_health_warnings': True,
            'auto_dismiss_info_alerts': True,
            'require_acknowledgment_critical': True,
            'sound_alerts_enabled': False,
            'email_alerts_enabled': False
        }
        
        # Safety disclaimers and warnings
        self.safety_disclaimers = {
            'paper_trading': "⚠️ PAPER TRADING MODE: No real money is at risk. All trades are simulated.",
            'data_delay': "⚠️ DATA DELAY: Market data may be delayed. Verify current prices before making decisions.",
            'ai_analysis': "⚠️ AI ANALYSIS: AI-generated analysis is for educational purposes only. Not financial advice.",
            'risk_warning': "⚠️ RISK WARNING: Trading involves substantial risk of loss. Never invest more than you can afford to lose.",
            'compliance': "⚠️ COMPLIANCE: This system provides educational information only. Consult a financial advisor for investment decisions."
        }
        
        self.logger.info("[USER_SAFETY] User Safety System initialized")
    
    async def check_data_safety(self, symbol: str, data: Dict[str, Any], 
                              source: str) -> Tuple[bool, List[DataQualityWarning]]:
        """Comprehensive data safety check for user protection"""
        try:
            warnings = []
            is_safe = True
            
            # 1. Data freshness check
            freshness_warning = await self._check_data_freshness(symbol, data, source)
            if freshness_warning:
                warnings.append(freshness_warning)
                if freshness_warning.severity in [AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]:
                    is_safe = False
            
            # 2. Price anomaly check
            price_warning = await self._check_price_anomalies(symbol, data, source)
            if price_warning:
                warnings.append(price_warning)
            
            # 3. Volume anomaly check
            volume_warning = await self._check_volume_anomalies(symbol, data, source)
            if volume_warning:
                warnings.append(volume_warning)
            
            # 4. Data completeness check
            completeness_warning = await self._check_data_completeness(symbol, data, source)
            if completeness_warning:
                warnings.append(completeness_warning)
                if completeness_warning.severity == AlertSeverity.CRITICAL:
                    is_safe = False
            
            # Store warnings for user display
            for warning in warnings:
                warning_key = f"{symbol}_{source}_{warning.issue_type}"
                self.data_quality_warnings[warning_key] = warning
            
            return is_safe, warnings
            
        except Exception as e:
            self.logger.error(f"Data safety check error: {e}")
            # Create emergency warning for system error
            emergency_warning = DataQualityWarning(
                symbol=symbol,
                data_source=source,
                issue_type="SYSTEM_ERROR",
                severity=AlertSeverity.EMERGENCY,
                message=f"System error during data validation: {str(e)}",
                recommendation="Do not make trading decisions based on this data. Contact support.",
                timestamp=datetime.now()
            )
            return False, [emergency_warning]
    
    async def _check_data_freshness(self, symbol: str, data: Dict[str, Any], 
                                  source: str) -> Optional[DataQualityWarning]:
        """Check data freshness and generate warnings"""
        try:
            timestamp = data.get('timestamp')
            if not timestamp:
                return DataQualityWarning(
                    symbol=symbol,
                    data_source=source,
                    issue_type="MISSING_TIMESTAMP",
                    severity=AlertSeverity.CRITICAL,
                    message="Market data missing timestamp - cannot verify freshness",
                    recommendation="Do not rely on this data for trading decisions",
                    timestamp=datetime.now()
                )
            
            # Convert to datetime if needed
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            
            age_seconds = (datetime.now() - timestamp.replace(tzinfo=None)).total_seconds()
            
            if age_seconds > self.safety_thresholds['data_staleness_critical']:
                return DataQualityWarning(
                    symbol=symbol,
                    data_source=source,
                    issue_type="STALE_DATA",
                    severity=AlertSeverity.CRITICAL,
                    message=f"Market data is {age_seconds/60:.1f} minutes old - potentially outdated",
                    recommendation="Verify current market prices before making any trading decisions",
                    timestamp=datetime.now()
                )
            elif age_seconds > self.safety_thresholds['data_staleness_warning']:
                return DataQualityWarning(
                    symbol=symbol,
                    data_source=source,
                    issue_type="DATA_AGING",
                    severity=AlertSeverity.WARNING,
                    message=f"Market data is {age_seconds:.0f} seconds old",
                    recommendation="Consider refreshing data for most current prices",
                    timestamp=datetime.now()
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Data freshness check error: {e}")
            return None
    
    async def _check_price_anomalies(self, symbol: str, data: Dict[str, Any], 
                                   source: str) -> Optional[DataQualityWarning]:
        """Check for price anomalies that could indicate data issues"""
        try:
            price = float(data.get('price', 0))
            if price <= 0:
                return DataQualityWarning(
                    symbol=symbol,
                    data_source=source,
                    issue_type="INVALID_PRICE",
                    severity=AlertSeverity.CRITICAL,
                    message=f"Invalid price data: ${price}",
                    recommendation="Do not use this data - price appears corrupted",
                    timestamp=datetime.now()
                )
            
            # Check for extreme prices (basic sanity check)
            if price > 100000:  # $100k per share
                return DataQualityWarning(
                    symbol=symbol,
                    data_source=source,
                    issue_type="EXTREME_PRICE",
                    severity=AlertSeverity.WARNING,
                    message=f"Unusually high price: ${price:,.2f}",
                    recommendation="Verify this price is correct before making decisions",
                    timestamp=datetime.now()
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Price anomaly check error: {e}")
            return None
    
    async def _check_volume_anomalies(self, symbol: str, data: Dict[str, Any], 
                                    source: str) -> Optional[DataQualityWarning]:
        """Check for volume anomalies"""
        try:
            volume = data.get('volume', 0)
            if volume and volume > 1000000000:  # 1B shares
                return DataQualityWarning(
                    symbol=symbol,
                    data_source=source,
                    issue_type="EXTREME_VOLUME",
                    severity=AlertSeverity.WARNING,
                    message=f"Unusually high volume: {volume:,} shares",
                    recommendation="Verify volume data accuracy - may indicate market event",
                    timestamp=datetime.now()
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Volume anomaly check error: {e}")
            return None
    
    async def _check_data_completeness(self, symbol: str, data: Dict[str, Any], 
                                     source: str) -> Optional[DataQualityWarning]:
        """Check data completeness"""
        try:
            required_fields = ['symbol', 'price', 'timestamp']
            missing_fields = [field for field in required_fields if field not in data or not data[field]]
            
            if missing_fields:
                return DataQualityWarning(
                    symbol=symbol,
                    data_source=source,
                    issue_type="INCOMPLETE_DATA",
                    severity=AlertSeverity.CRITICAL,
                    message=f"Missing required data fields: {', '.join(missing_fields)}",
                    recommendation="Do not use incomplete data for trading decisions",
                    timestamp=datetime.now()
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Data completeness check error: {e}")
            return None
    
    async def generate_user_alert(self, alert_type: SafetyCheckType, severity: AlertSeverity,
                                title: str, message: str, details: Dict[str, Any] = None,
                                user_action_required: bool = False) -> str:
        """Generate user safety alert"""
        try:
            alert_id = f"{alert_type.value}_{int(datetime.now().timestamp())}"
            
            alert = SafetyAlert(
                alert_id=alert_id,
                alert_type=alert_type,
                severity=severity,
                title=title,
                message=message,
                details=details or {},
                timestamp=datetime.now(),
                user_action_required=user_action_required,
                auto_dismiss_after=300 if severity == AlertSeverity.INFO else None  # Auto-dismiss info alerts after 5 minutes
            )
            
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            self.logger.info(f"[USER_ALERT] {severity.value}: {title}")
            
            return alert_id
            
        except Exception as e:
            self.logger.error(f"Error generating user alert: {e}")
            return ""
    
    async def acknowledge_alert(self, alert_id: str, user_id: str = "system") -> bool:
        """Acknowledge user alert"""
        try:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].acknowledged = True
                self.logger.info(f"[ALERT_ACKNOWLEDGED] {alert_id} by {user_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error acknowledging alert: {e}")
            return False
    
    def get_user_safety_status(self) -> Dict[str, Any]:
        """Get comprehensive user safety status"""
        try:
            current_time = datetime.now()
            
            # Active alerts by severity
            alerts_by_severity = {
                'INFO': [],
                'WARNING': [],
                'CRITICAL': [],
                'EMERGENCY': []
            }
            
            for alert in self.active_alerts.values():
                if not alert.acknowledged:
                    alerts_by_severity[alert.severity.value].append({
                        'alert_id': alert.alert_id,
                        'title': alert.title,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat(),
                        'user_action_required': alert.user_action_required
                    })
            
            # Recent data quality warnings
            recent_warnings = []
            for warning in self.data_quality_warnings.values():
                if (current_time - warning.timestamp).total_seconds() < 3600:  # Last hour
                    recent_warnings.append({
                        'symbol': warning.symbol,
                        'data_source': warning.data_source,
                        'issue_type': warning.issue_type,
                        'severity': warning.severity.value,
                        'message': warning.message,
                        'recommendation': warning.recommendation,
                        'timestamp': warning.timestamp.isoformat()
                    })
            
            # Safety status summary
            has_critical_alerts = len(alerts_by_severity['CRITICAL']) > 0 or len(alerts_by_severity['EMERGENCY']) > 0
            has_warnings = len(alerts_by_severity['WARNING']) > 0
            
            if has_critical_alerts:
                safety_status = "CRITICAL"
            elif has_warnings:
                safety_status = "WARNING"
            else:
                safety_status = "SAFE"
            
            return {
                'timestamp': current_time.isoformat(),
                'safety_status': safety_status,
                'active_alerts': alerts_by_severity,
                'data_quality_warnings': recent_warnings,
                'safety_disclaimers': self.safety_disclaimers,
                'total_active_alerts': sum(len(alerts) for alerts in alerts_by_severity.values()),
                'paper_trading_active': True,  # Always true for safety
                'system_monitoring_active': True
            }
            
        except Exception as e:
            self.logger.error(f"Error getting safety status: {e}")
            return {'error': str(e)}
    
    def get_safety_recommendations(self, context: str = "general") -> List[str]:
        """Get context-specific safety recommendations"""
        try:
            recommendations = []
            
            if context == "trading":
                recommendations.extend([
                    "Always verify current market prices before making trading decisions",
                    "Use stop-loss orders to limit potential losses",
                    "Never risk more than 2% of your portfolio on a single trade",
                    "Paper trading mode is active - no real money is at risk",
                    "Consider market conditions and volatility before trading"
                ])
            elif context == "data_analysis":
                recommendations.extend([
                    "Check data timestamps to ensure information is current",
                    "Cross-reference data from multiple sources when possible",
                    "Be aware of market hours and data availability",
                    "Understand that AI analysis is for educational purposes only"
                ])
            elif context == "system_health":
                recommendations.extend([
                    "Monitor system alerts and warnings regularly",
                    "Report any unusual system behavior immediately",
                    "Keep the system updated with latest configurations",
                    "Ensure stable internet connection for real-time data"
                ])
            else:  # general
                recommendations.extend([
                    "This system is for educational and analysis purposes only",
                    "Always consult with a qualified financial advisor before making investment decisions",
                    "Past performance does not guarantee future results",
                    "Trading involves substantial risk of loss",
                    "Never invest money you cannot afford to lose"
                ])
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error getting safety recommendations: {e}")
            return ["Error retrieving safety recommendations"]
    
    async def cleanup_old_alerts(self):
        """Clean up old alerts and warnings"""
        try:
            current_time = datetime.now()
            
            # Remove auto-dismissible alerts
            alerts_to_remove = []
            for alert_id, alert in self.active_alerts.items():
                if alert.auto_dismiss_after:
                    age_seconds = (current_time - alert.timestamp).total_seconds()
                    if age_seconds > alert.auto_dismiss_after:
                        alerts_to_remove.append(alert_id)
            
            for alert_id in alerts_to_remove:
                del self.active_alerts[alert_id]
            
            # Remove old data quality warnings (older than 24 hours)
            warnings_to_remove = []
            for warning_key, warning in self.data_quality_warnings.items():
                if (current_time - warning.timestamp).total_seconds() > 86400:
                    warnings_to_remove.append(warning_key)
            
            for warning_key in warnings_to_remove:
                del self.data_quality_warnings[warning_key]
            
            # Limit alert history size
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-500:]  # Keep last 500
            
        except Exception as e:
            self.logger.error(f"Error cleaning up alerts: {e}")

# Global instance
user_safety_system = AtlasUserSafetySystem()
