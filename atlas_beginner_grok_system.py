#!/usr/bin/env python3
"""
Atlas Beginner-Friendly Grok System
Converts beginner trading questions into intelligent Grok prompts with structured responses
"""

import asyncio
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

# Core imports
from atlas_grok_integration import (
    AtlasGrokIntegrationEngine, GrokRequest, GrokResponse,
    GrokTaskType, GrokCapability
)

logger = logging.getLogger(__name__)

@dataclass
class BeginnerQuery:
    """Structure for beginner trading queries"""
    original_message: str
    query_type: str
    extracted_params: Dict[str, Any]
    risk_level: str
    experience_level: str
    confidence: float

@dataclass
class StructuredTradingResponse:
    """6-point structured trading response format"""
    why_this_plan: str
    win_loss_probability: str
    money_in_out: str
    smart_stop_plans: str
    market_context: str
    confidence_score: str
    educational_notes: List[str]

class BeginnerGrokSystem:
    """Intelligent system for converting beginner queries to structured Grok prompts"""
    
    def __init__(self):
        self.grok_engine = None
        self.query_patterns = self._initialize_query_patterns()
        self.response_templates = self._initialize_response_templates()
        
    async def initialize(self):
        """Initialize the Grok integration"""
        try:
            self.grok_engine = AtlasGrokIntegrationEngine()
            success = await self.grok_engine.initialize()
            
            if success:
                logger.info("✅ Beginner Grok System initialized with Grok integration")
                return True
            else:
                logger.warning("⚠️ Grok unavailable - using educational fallbacks")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize Beginner Grok System: {e}")
            return False
    
    def _initialize_query_patterns(self) -> Dict[str, Dict]:
        """Initialize patterns for detecting beginner query types"""
        return {
            'profit_target': {
                'patterns': [
                    r'make.*\$?(\d+(?:,\d+)?)\s*(?:dollars?|bucks?)?.*in\s*(\d+)\s*(days?|weeks?|months?)',
                    r'earn.*\$?(\d+(?:,\d+)?)\s*(?:dollars?|bucks?)?.*in\s*(\d+)\s*(days?|weeks?|months?)',
                    r'profit.*\$?(\d+(?:,\d+)?)\s*(?:dollars?|bucks?)?.*in\s*(\d+)\s*(days?|weeks?|months?)',
                    r'generate.*\$?(\d+(?:,\d+)?)\s*(?:dollars?|bucks?)?.*in\s*(\d+)\s*(days?|weeks?|months?)',
                    r'target.*\$?(\d+(?:,\d+)?)\s*(?:dollars?|bucks?)?.*in\s*(\d+)\s*(days?|weeks?|months?)',
                    r'want.*\$?(\d+(?:,\d+)?)\s*(?:dollars?|bucks?)?.*in\s*(\d+)\s*(days?|weeks?|months?)',
                    r'make.*\$?(\d+(?:,\d+)?)',
                    r'earn.*\$?(\d+(?:,\d+)?)',
                    r'profit.*\$?(\d+(?:,\d+)?)',
                    r'generate.*\$?(\d+(?:,\d+)?)',
                    r'target.*\$?(\d+(?:,\d+)?)',
                    r'\$(\d+(?:,\d+)?)'
                ],
                'keywords': ['make money', 'profit', 'earn', 'generate income', 'trading plan', 'target', 'goal'],
                'risk_level': 'moderate',
                'grok_template': 'profit_focused_plan'
            },
            'market_scan': {
                'patterns': [
                    r'scan.*market',
                    r'find.*setups?',
                    r'look.*for.*opportunities',
                    r'lee method.*signals?'
                ],
                'keywords': ['scan', 'find', 'opportunities', 'setups', 'signals'],
                'risk_level': 'low',
                'grok_template': 'market_scanning'
            },
            'investment_advice': {
                'patterns': [
                    r'what.*should.*invest',
                    r'good.*stocks?.*buy',
                    r'recommendations?',
                    r'invest.*this week'
                ],
                'keywords': ['invest', 'buy', 'recommendations', 'advice', 'stocks'],
                'risk_level': 'conservative',
                'grok_template': 'investment_guidance'
            },
            'learning_request': {
                'patterns': [
                    r'how.*does.*work',
                    r'explain.*trading',
                    r'what.*is.*(?:rsi|macd|lee method)',
                    r'teach.*me'
                ],
                'keywords': ['explain', 'how', 'what is', 'teach', 'learn'],
                'risk_level': 'educational',
                'grok_template': 'educational_response'
            }
        }
    
    def _initialize_response_templates(self) -> Dict[str, str]:
        """Initialize Grok prompt templates for different query types"""
        return {
            'profit_focused_plan': """
Act as an expert trading mentor for a beginner trader. Create a comprehensive trading plan with these requirements:

TARGET: Make ${target_amount} profit in {timeframe}
CAPITAL: ${capital} (assume $10,000 if not specified)
RISK LEVEL: {risk_level}
EXPERIENCE: Beginner trader

Provide your response in this EXACT 6-point format:

**WHY THIS PLAN**
[Explain the strategy logic and why it fits the target/timeframe]

**WIN/LOSS PROBABILITY** 
[Give realistic win rate percentages and expected outcomes]

**MONEY IN/OUT**
[Show position sizes, entry costs, profit targets, max loss]

**SMART STOP PLANS**
[Specific stop-loss levels and exit strategies]

**MARKET CONTEXT**
[Current market conditions affecting this plan]

**CONFIDENCE SCORE**
[Rate 1-10 with justification]

IMPORTANT: Explain all trading terms. Keep risk moderate. Use real current market data when possible.
""",
            
            'market_scanning': """
Act as a professional market scanner for a beginner trader. Scan the current U.S. stock market for opportunities:

SCAN CRITERIA:
- Lee Method signals (volume spike + momentum pattern)
- Current earnings potential
- Beginner-friendly setups
- Risk level: {risk_level}

Provide 3-5 specific opportunities in this format:

**SYMBOL - TRADE TYPE**
• Entry: $X.XX
• Target: $X.XX  
• Stop: $X.XX
• Confidence: X/10
• Why: [Brief explanation of setup]

Then add:
**MARKET CONTEXT**: [Overall market conditions]
**BEGINNER NOTES**: [Key terms explained, risk warnings]

Use real current market data. Explain all technical terms.
""",
            
            'investment_guidance': """
Act as a patient investment advisor for a complete beginner. Provide investment guidance:

INVESTOR PROFILE:
- Experience: Beginner
- Risk tolerance: {risk_level}
- Time horizon: {timeframe}
- Goal: {goal}

Provide guidance in this format:

**RECOMMENDED APPROACH**
[Overall investment strategy for beginners]

**SPECIFIC OPPORTUNITIES**
[3-5 beginner-friendly stocks/ETFs with reasoning]

**RISK MANAGEMENT**
[How to protect capital and manage positions]

**LEARNING PATH**
[What to study next, key concepts to understand]

**MARKET TIMING**
[Current market conditions and timing considerations]

**CONFIDENCE & NEXT STEPS**
[Action plan with confidence level]

Explain all terms. Focus on education over quick profits.
""",
            
            'educational_response': """
Act as a patient trading educator. Explain the requested concept clearly for a complete beginner:

TOPIC: {topic}
EXPERIENCE LEVEL: Complete beginner

Provide explanation in this format:

**SIMPLE EXPLANATION**
[What it is in plain English]

**HOW IT WORKS**
[Step-by-step process or calculation]

**REAL EXAMPLE**
[Concrete example with actual numbers/stocks]

**WHEN TO USE IT**
[Practical applications in trading]

**BEGINNER MISTAKES**
[Common errors to avoid]

**NEXT STEPS**
[What to learn next, how to practice]

Use simple language. Include visual descriptions. Give actionable advice.
"""
        }
    
    async def analyze_beginner_query(self, message: str) -> BeginnerQuery:
        """Analyze a message to determine if it's a beginner query and extract parameters"""
        message_lower = message.lower()
        
        # Enhanced financial amount extraction with flexible patterns
        amount_patterns = [
            r'\$(\d+(?:,\d+)*(?:\.\d{2})?)',
            r'(\d+(?:,\d+)*(?:\.\d{2})?)\s*dollars?',
            r'(\d+(?:,\d+)*(?:\.\d{2})?)\s*bucks?',
            r'make.*?(\d+(?:,\d+)*(?:\.\d{2})?)',
            r'earn.*?(\d+(?:,\d+)*(?:\.\d{2})?)',
            r'profit.*?(\d+(?:,\d+)*(?:\.\d{2})?)',
            r'target.*?(\d+(?:,\d+)*(?:\.\d{2})?)',
            r'generate.*?(\d+(?:,\d+)*(?:\.\d{2})?)'
        ]

        amounts = []
        for pattern in amount_patterns:
            matches = re.findall(pattern, message)
            amounts.extend([match for match in matches if match and match.replace(',', '').replace('.', '').isdigit()])

        # Enhanced timeframe extraction with flexible patterns
        timeframe_patterns = [
            r'in\s*(\d+)\s*(days?|weeks?|months?)',
            r'within\s*(\d+)\s*(days?|weeks?|months?)',
            r'over\s*(\d+)\s*(days?|weeks?|months?)',
            r'by\s*(\d+)\s*(days?|weeks?|months?)',
            r'(\d+)\s*(days?|weeks?|months?)'
        ]

        timeframes = []
        for pattern in timeframe_patterns:
            matches = re.findall(pattern, message)
            timeframes.extend(matches)
        
        # Determine query type
        query_type = 'general'
        confidence = 0.5
        extracted_params = {}
        
        for qtype, config in self.query_patterns.items():
            # Check patterns
            pattern_matches = any(re.search(pattern, message_lower) for pattern in config['patterns'])
            # Check keywords
            keyword_matches = any(keyword in message_lower for keyword in config['keywords'])
            
            if pattern_matches or keyword_matches:
                query_type = qtype
                confidence = 0.8 if pattern_matches else 0.6
                
                # Extract specific parameters
                if amounts:
                    extracted_params['target_amount'] = amounts[0].replace(',', '')
                if timeframes:
                    extracted_params['timeframe'] = f"{timeframes[0][0]} {timeframes[0][1]}"
                
                extracted_params['risk_level'] = config['risk_level']
                break
        
        # Determine experience level from language patterns
        beginner_indicators = ['beginner', 'new', 'start', 'learn', 'how', 'what is', 'explain']
        experience_level = 'beginner' if any(indicator in message_lower for indicator in beginner_indicators) else 'intermediate'
        
        return BeginnerQuery(
            original_message=message,
            query_type=query_type,
            extracted_params=extracted_params,
            risk_level=extracted_params.get('risk_level', 'moderate'),
            experience_level=experience_level,
            confidence=confidence
        )
    
    async def create_grok_prompt(self, query: BeginnerQuery) -> str:
        """Create an intelligent Grok prompt based on the beginner query"""
        template_key = self.query_patterns.get(query.query_type, {}).get('grok_template', 'educational_response')
        template = self.response_templates.get(template_key, self.response_templates['educational_response'])
        
        # Fill in template parameters with intelligent defaults
        target_amount = query.extracted_params.get('target_amount')
        timeframe = query.extracted_params.get('timeframe')
        capital = query.extracted_params.get('capital', '10000')

        # Provide intelligent defaults based on context
        if not target_amount:
            # Analyze message for context clues
            message_lower = query.original_message.lower()
            if any(word in message_lower for word in ['small', 'little', 'modest']):
                target_amount = '100'
            elif any(word in message_lower for word in ['big', 'large', 'substantial']):
                target_amount = '1000'
            else:
                target_amount = '250'  # Moderate default

        if not timeframe:
            # Intelligent timeframe based on target amount
            target_num = float(target_amount.replace(',', ''))
            if target_num <= 100:
                timeframe = '1 day'
            elif target_num <= 500:
                timeframe = '1 week'
            elif target_num <= 2000:
                timeframe = '2 weeks'
            else:
                timeframe = '1 month'

        prompt = template.format(
            target_amount=target_amount,
            timeframe=timeframe,
            capital=capital,
            risk_level=query.risk_level,
            goal=query.extracted_params.get('goal', 'profitable trading'),
            topic=query.original_message
        )
        
        return prompt
    
    async def process_beginner_query(self, message: str) -> Dict[str, Any]:
        """Main method to process a beginner query and return structured response"""
        try:
            # Analyze the query
            query = await self.analyze_beginner_query(message)
            
            # Create intelligent Grok prompt
            grok_prompt = await self.create_grok_prompt(query)
            
            # Send to Grok if available
            if self.grok_engine:
                grok_request = GrokRequest(
                    task_type=GrokTaskType.LOGICAL_REASONING,
                    capability=GrokCapability.REASONING,
                    prompt=grok_prompt,
                    temperature=0.1,  # Lower temperature for more consistent educational content
                    max_tokens=2000
                )
                
                grok_response = await self.grok_engine.make_request(grok_request)
                
                if grok_response.success:
                    return {
                        'success': True,
                        'response': grok_response.content,
                        'query_type': query.query_type,
                        'confidence': query.confidence,
                        'educational_level': 'beginner_friendly',
                        'structured_format': True,
                        'parameters_extracted': query.extracted_params
                    }
            
            # Fallback educational response
            return self._create_fallback_response(query)
            
        except Exception as e:
            logger.error(f"Error processing beginner query: {e}")
            return {
                'success': False,
                'error': str(e),
                'fallback_message': "I'd be happy to help with your trading question. Could you provide more details about what you'd like to learn or achieve?"
            }
    
    def _create_fallback_response(self, query: BeginnerQuery) -> Dict[str, Any]:
        """Create educational fallback response when Grok is unavailable"""
        fallback_responses = {
            'profit_target': f"""
**BEGINNER TRADING PLAN GUIDANCE**

To work toward your goal of ${query.extracted_params.get('target_amount', 'X')} in {query.extracted_params.get('timeframe', 'your timeframe')}, here's what you need to know:

**REALISTIC EXPECTATIONS**
• Professional traders average 10-20% monthly returns
• Beginners should start with paper trading
• Risk management is more important than profit targets

**RECOMMENDED APPROACH**
• Start with $1,000-2,000 real money after paper trading
• Focus on learning before earning
• Use stop-losses on every trade

**NEXT STEPS**
• Practice with a demo account first
• Learn basic technical analysis
• Study risk management principles

This is educational guidance. Always do your own research and consider consulting a financial advisor.
""",
            'market_scan': """
**MARKET SCANNING FOR BEGINNERS**

Here's how to approach market scanning safely:

**BEGINNER-FRIENDLY APPROACH**
• Start with large-cap stocks (Apple, Microsoft, Google)
• Look for stocks near support levels
• Check earnings calendars for upcoming events

**KEY INDICATORS TO WATCH**
• Volume: Higher than average indicates interest
• RSI: Below 30 (oversold) or above 70 (overbought)
• Moving averages: Price above 50-day MA is bullish

**RISK MANAGEMENT**
• Never risk more than 2% of your account per trade
• Always set stop-losses before entering
• Start with small position sizes

Practice identifying these patterns before risking real money.
""",
            'general': """
**BEGINNER TRADING GUIDANCE**

Welcome to trading! Here's how to start safely:

**EDUCATION FIRST**
• Learn basic chart reading
• Understand risk management
• Practice with paper trading

**START SMALL**
• Begin with index funds or ETFs
• Use only money you can afford to lose
• Focus on learning, not earning initially

**KEY PRINCIPLES**
• Cut losses short, let profits run
• Never trade with emotion
• Keep a trading journal

Would you like me to explain any specific trading concepts or help you create a learning plan?
"""
        }
        
        response_text = fallback_responses.get(query.query_type, fallback_responses['general'])
        
        return {
            'success': True,
            'response': response_text,
            'query_type': query.query_type,
            'confidence': query.confidence,
            'educational_level': 'beginner_friendly',
            'structured_format': True,
            'fallback_used': True,
            'parameters_extracted': query.extracted_params
        }

# Global instance
beginner_grok_system = BeginnerGrokSystem()
