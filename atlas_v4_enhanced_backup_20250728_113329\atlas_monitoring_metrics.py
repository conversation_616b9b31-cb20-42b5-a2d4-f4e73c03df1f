"""
Atlas Monitoring and Metrics Module
Provides system monitoring and performance metrics for the Atlas trading system.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import threading

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MetricType(Enum):
    """Metric types"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"

class AlertLevel(Enum):
    """Alert levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class Metric:
    """Metric data structure"""
    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime
    tags: Dict[str, str]
    unit: str = ""

@dataclass
class Alert:
    """Alert data structure"""
    alert_id: str
    message: str
    level: AlertLevel
    timestamp: datetime
    metric_name: str
    threshold: float
    current_value: float

class AtlasMonitoringMetrics:
    """Atlas Monitoring and Metrics Engine"""
    
    def __init__(self):
        """Initialize monitoring and metrics engine"""
        self.metrics = {}
        self.alerts = []
        self.thresholds = {}
        self.start_time = datetime.now()
        self.lock = threading.Lock()
        
        # Initialize default metrics
        self._initialize_default_metrics()
        
        logger.info("[MONITORING] Monitoring and metrics engine initialized")
    
    def _initialize_default_metrics(self):
        """Initialize default system metrics"""
        default_metrics = [
            ("system.uptime", 0.0, MetricType.GAUGE, "seconds"),
            ("api.requests.total", 0.0, MetricType.COUNTER, "requests"),
            ("trading.orders.total", 0.0, MetricType.COUNTER, "orders"),
            ("market.data.updates", 0.0, MetricType.COUNTER, "updates"),
            ("system.memory.usage", 0.0, MetricType.GAUGE, "MB"),
            ("system.cpu.usage", 0.0, MetricType.GAUGE, "percent")
        ]
        
        for name, value, metric_type, unit in default_metrics:
            self.record_metric(name, value, metric_type, unit=unit)
    
    def record_metric(self, name: str, value: float, metric_type: MetricType = MetricType.GAUGE, 
                     tags: Optional[Dict[str, str]] = None, unit: str = "") -> Metric:
        """Record a metric"""
        with self.lock:
            metric = Metric(
                name=name,
                value=value,
                metric_type=metric_type,
                timestamp=datetime.now(),
                tags=tags or {},
                unit=unit
            )
            
            self.metrics[name] = metric
            
            # Check thresholds
            self._check_threshold(metric)
            
            return metric
    
    def increment_counter(self, name: str, value: float = 1.0, 
                         tags: Optional[Dict[str, str]] = None) -> Metric:
        """Increment a counter metric"""
        current_value = self.get_metric_value(name, 0.0)
        return self.record_metric(name, current_value + value, MetricType.COUNTER, tags)
    
    def set_gauge(self, name: str, value: float, 
                  tags: Optional[Dict[str, str]] = None, unit: str = "") -> Metric:
        """Set a gauge metric"""
        return self.record_metric(name, value, MetricType.GAUGE, tags, unit)
    
    def get_metric_value(self, name: str, default: float = 0.0) -> float:
        """Get current metric value"""
        with self.lock:
            metric = self.metrics.get(name)
            return metric.value if metric else default
    
    def get_metric(self, name: str) -> Optional[Metric]:
        """Get metric by name"""
        with self.lock:
            return self.metrics.get(name)
    
    def get_all_metrics(self) -> Dict[str, Metric]:
        """Get all metrics"""
        with self.lock:
            return self.metrics.copy()
    
    def set_threshold(self, metric_name: str, threshold: float, 
                     alert_level: AlertLevel = AlertLevel.WARNING):
        """Set threshold for metric alerting"""
        self.thresholds[metric_name] = {
            "threshold": threshold,
            "level": alert_level
        }
    
    def _check_threshold(self, metric: Metric):
        """Check if metric exceeds threshold"""
        threshold_config = self.thresholds.get(metric.name)
        if not threshold_config:
            return
        
        threshold = threshold_config["threshold"]
        level = threshold_config["level"]
        
        if metric.value > threshold:
            alert = Alert(
                alert_id=f"alert_{len(self.alerts) + 1}",
                message=f"Metric {metric.name} exceeded threshold: {metric.value} > {threshold}",
                level=level,
                timestamp=datetime.now(),
                metric_name=metric.name,
                threshold=threshold,
                current_value=metric.value
            )
            
            self.alerts.append(alert)
            logger.warning(f"[ALERT] {alert.message}")
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health"""
        uptime = (datetime.now() - self.start_time).total_seconds()
        self.set_gauge("system.uptime", uptime, unit="seconds")
        
        total_alerts = len(self.alerts)
        critical_alerts = len([a for a in self.alerts if a.level == AlertLevel.CRITICAL])
        
        health_score = max(0, 100 - (critical_alerts * 20) - (total_alerts * 5))
        
        return {
            "health_score": health_score,
            "status": "healthy" if health_score > 80 else "degraded" if health_score > 50 else "unhealthy",
            "uptime_seconds": uptime,
            "total_metrics": len(self.metrics),
            "total_alerts": total_alerts,
            "critical_alerts": critical_alerts,
            "last_updated": datetime.now().isoformat()
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        return {
            "api_requests": self.get_metric_value("api.requests.total"),
            "trading_orders": self.get_metric_value("trading.orders.total"),
            "market_updates": self.get_metric_value("market.data.updates"),
            "memory_usage_mb": self.get_metric_value("system.memory.usage"),
            "cpu_usage_percent": self.get_metric_value("system.cpu.usage"),
            "uptime_seconds": self.get_metric_value("system.uptime")
        }
    
    def clear_old_alerts(self, hours: int = 24):
        """Clear alerts older than specified hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        self.alerts = [a for a in self.alerts if a.timestamp > cutoff]

class AtlasMonitoringSystem:
    """Atlas Monitoring System - Alias for compatibility"""

    def __init__(self):
        """Initialize monitoring system"""
        self.metrics_engine = AtlasMonitoringMetrics()
        logger.info("[MONITORING] Atlas monitoring system initialized")

    def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        return self.metrics_engine.get_system_health()

    def record_event(self, event_type: str, data: Dict[str, Any]):
        """Record monitoring event"""
        self.metrics_engine.increment_counter(f"events.{event_type}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self.metrics_engine.get_performance_summary()

# Global instances
monitoring_metrics = AtlasMonitoringMetrics()
monitoring_system = AtlasMonitoringSystem()

def record_metric(name: str, value: float, metric_type: MetricType = MetricType.GAUGE, 
                 tags: Optional[Dict[str, str]] = None, unit: str = "") -> Metric:
    """Record a metric"""
    return monitoring_metrics.record_metric(name, value, metric_type, tags, unit)

def increment_counter(name: str, value: float = 1.0, 
                     tags: Optional[Dict[str, str]] = None) -> Metric:
    """Increment a counter"""
    return monitoring_metrics.increment_counter(name, value, tags)

def set_gauge(name: str, value: float, 
              tags: Optional[Dict[str, str]] = None, unit: str = "") -> Metric:
    """Set a gauge"""
    return monitoring_metrics.set_gauge(name, value, tags, unit)

def get_system_health() -> Dict[str, Any]:
    """Get system health"""
    return monitoring_metrics.get_system_health()

def get_performance_summary() -> Dict[str, Any]:
    """Get performance summary"""
    return monitoring_metrics.get_performance_summary()

if __name__ == "__main__":
    # Test the monitoring system
    print("Testing Atlas Monitoring Metrics...")
    
    # Test metric recording
    monitoring_metrics.record_metric("test.metric", 42.0, MetricType.GAUGE)
    print(f"Test metric value: {monitoring_metrics.get_metric_value('test.metric')}")
    
    # Test counter
    monitoring_metrics.increment_counter("test.counter", 5.0)
    print(f"Test counter value: {monitoring_metrics.get_metric_value('test.counter')}")
    
    # Test system health
    health = monitoring_metrics.get_system_health()
    print(f"System health: {health}")
    
    print("Monitoring metrics system working!")
