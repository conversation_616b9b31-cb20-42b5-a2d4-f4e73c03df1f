#!/usr/bin/env python3
"""
A.T.L.A.S. Chat Interface Integration for Morning Briefings
Handles automatic briefing delivery and on-demand requests via chat
"""

import asyncio
import logging
import re
from datetime import datetime, time as dt_time
from typing import Dict, List, Any, Optional, Callable
import pytz

from atlas_morning_briefing import morning_briefing, AtlasMorningBriefing

logger = logging.getLogger(__name__)

class AtlasChatBriefingHandler:
    """Handles morning briefing integration with chat interface"""
    
    def __init__(self):
        self.briefing_system = morning_briefing
        self.auto_briefing_sent = False
        self.chat_callback: Optional[Callable] = None
        self.monitoring_active = False
        
        # Keywords that trigger briefing requests
        self.briefing_keywords = [
            'morning briefing', 'market briefing', 'market snapshot', 
            'market update', 'daily briefing', 'market overview',
            'trade setups', 'market analysis', 'morning update',
            'what\'s happening', 'market status', 'trading ideas'
        ]
        
        logger.info("✅ A.T.L.A.S. Chat Briefing Handler initialized")
    
    def register_chat_callback(self, callback: Callable):
        """Register callback function to send messages to chat"""
        self.chat_callback = callback
        logger.info("✅ Chat callback registered for briefing delivery")
    
    async def start_monitoring(self):
        """Start monitoring for market open and auto-briefing delivery"""
        if self.monitoring_active:
            logger.warning("Briefing monitoring already active")
            return
        
        self.monitoring_active = True
        logger.info("🔄 Starting morning briefing monitoring...")
        
        # Start background monitoring task
        asyncio.create_task(self._monitor_market_open())
    
    async def stop_monitoring(self):
        """Stop monitoring for market open"""
        self.monitoring_active = False
        logger.info("⏹️ Stopped morning briefing monitoring")
    
    async def _monitor_market_open(self):
        """Enhanced monitoring for market open and automated briefing delivery"""
        while self.monitoring_active:
            try:
                # Check if we should send auto briefing
                if await self._should_send_auto_briefing():
                    await self._send_auto_briefing()

                # Check for weekly summary (Friday after close)
                await self.schedule_weekly_briefing_summary()

                # Check for mid-day updates if significant market moves
                await self._check_midday_updates()

                # Check every 30 seconds during market hours, 5 minutes otherwise
                market_status = self.briefing_system.scanner.get_market_status()
                sleep_interval = 30 if market_status['market_open'] else 300
                await asyncio.sleep(sleep_interval)

            except Exception as e:
                logger.error(f"Error in briefing monitoring: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _check_midday_updates(self):
        """Check if significant market moves warrant a mid-day update"""
        try:
            et_tz = pytz.timezone('US/Eastern')
            current_time = datetime.now(et_tz).time()

            # Only check during market hours, after 12 PM ET
            if current_time >= dt_time(12, 0) and current_time <= dt_time(15, 30):
                # Get current market data
                briefing = await self.briefing_system.generate_morning_briefing()

                # Check for significant moves (>2% in major indexes)
                significant_moves = []
                for index_name, index_data in briefing.major_indexes.items():
                    change = abs(index_data.get('change', 0))
                    if change >= 2.0:
                        significant_moves.append(f"{index_name}: {change:+.1f}%")

                # Check VIX spike (>25)
                if briefing.vix_level > 25:
                    significant_moves.append(f"VIX spike: {briefing.vix_level:.1f}")

                # Send alert if significant moves detected
                if significant_moves and self.chat_callback:
                    alert_message = f"🚨 **Market Alert - {current_time.strftime('%H:%M')} ET**\n"
                    alert_message += f"Significant market moves detected:\n"
                    for move in significant_moves:
                        alert_message += f"• {move}\n"
                    alert_message += f"\nUse 'market update' for full briefing."

                    await self.chat_callback(alert_message)
                    logger.info(f"📊 Mid-day market alert sent: {len(significant_moves)} significant moves")

        except Exception as e:
            logger.error(f"Error checking mid-day updates: {e}")
    
    async def _should_send_auto_briefing(self) -> bool:
        """Check if automatic briefing should be sent with enhanced scheduling"""
        try:
            # Get market status
            market_status = self.briefing_system.scanner.get_market_status()

            # Check if market just opened and we haven't sent briefing today
            if market_status['market_open'] and not self.auto_briefing_sent:
                # Enhanced timing check
                et_tz = pytz.timezone('US/Eastern')
                current_time = datetime.now(et_tz).time()
                current_date = datetime.now(et_tz)

                # Market open times
                market_open_time = dt_time(9, 30)  # 9:30 AM ET
                briefing_cutoff = dt_time(10, 0)   # 10:00 AM ET

                # Check if it's a weekday
                is_weekday = current_date.weekday() < 5  # Monday-Friday

                # Check if within briefing window
                in_briefing_window = market_open_time <= current_time <= briefing_cutoff

                if is_weekday and in_briefing_window:
                    logger.info(f"📊 Auto briefing conditions met: {current_time.strftime('%H:%M')} ET")
                    return True
                else:
                    logger.debug(f"Outside briefing window: {current_time.strftime('%H:%M')} ET")

            # Reset flag when market closes or on weekends
            if not market_status['market_open']:
                if self.auto_briefing_sent:
                    logger.info("🌙 Market closed - resetting briefing flag for next trading day")
                self.auto_briefing_sent = False

            return False

        except Exception as e:
            logger.error(f"Error checking auto briefing conditions: {e}")
            return False

    async def schedule_weekly_briefing_summary(self):
        """Schedule weekly market summary (Friday after market close)"""
        try:
            et_tz = pytz.timezone('US/Eastern')
            current_time = datetime.now(et_tz)

            # Check if it's Friday after 4 PM ET
            if current_time.weekday() == 4 and current_time.time() >= dt_time(16, 30):
                logger.info("📊 Generating weekly market summary...")

                # Generate enhanced briefing with weekly context
                briefing_text = await self.briefing_system.get_briefing_for_chat()
                weekly_summary = f"📅 **Weekly Market Summary - {current_time.strftime('%B %d, %Y')}**\n\n{briefing_text}"

                if self.chat_callback:
                    await self.chat_callback(weekly_summary)
                    logger.info("✅ Weekly summary sent")

                return True

            return False

        except Exception as e:
            logger.error(f"Error in weekly briefing: {e}")
            return False
    
    async def _send_auto_briefing(self):
        """Send automatic morning briefing to chat"""
        try:
            logger.info("📊 Sending automatic morning briefing...")
            
            # Generate briefing
            briefing_text = await self.briefing_system.get_briefing_for_chat()
            
            # Add auto-briefing header
            auto_message = "🌅 **Good morning! Here's your automated market briefing:**\n\n" + briefing_text
            
            # Send to chat if callback is registered
            if self.chat_callback:
                await self.chat_callback(auto_message)
                logger.info("✅ Automatic morning briefing sent to chat")
            else:
                logger.warning("⚠️ No chat callback registered - briefing not sent")
            
            self.auto_briefing_sent = True
            
        except Exception as e:
            logger.error(f"❌ Error sending automatic briefing: {e}")
    
    async def handle_chat_message(self, message: str, user_id: str = None) -> Optional[str]:
        """
        Handle incoming chat messages and respond with briefing if requested
        
        Args:
            message: The chat message text
            user_id: Optional user identifier
            
        Returns:
            Briefing response if triggered, None otherwise
        """
        try:
            # Normalize message for keyword matching
            message_lower = message.lower().strip()
            
            # Check for briefing keywords
            if self._is_briefing_request(message_lower):
                logger.info(f"📊 Briefing requested via chat: '{message[:50]}...'")
                
                # Generate and return briefing
                briefing_text = await self.briefing_system.get_briefing_for_chat()
                
                # Add response header
                response = "📊 **Here's your current market briefing:**\n\n" + briefing_text
                
                logger.info("✅ Briefing delivered via chat request")
                return response
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error handling chat message: {e}")
            return "❌ Sorry, I couldn't generate the market briefing right now. Please try again."
    
    def _is_briefing_request(self, message: str) -> bool:
        """Check if message is requesting a briefing"""
        # Direct keyword matches
        for keyword in self.briefing_keywords:
            if keyword in message:
                return True
        
        # Pattern matches for common requests
        briefing_patterns = [
            r'\b(show|give|get|send)\s+(me\s+)?(the\s+)?market\b',
            r'\bwhat\'?s\s+(the\s+)?market\s+(doing|like)\b',
            r'\bmarket\s+(today|now|current)\b',
            r'\b(any\s+)?trade\s+(ideas|setups|opportunities)\b',
            r'\bhow\s+(is\s+)?the\s+market\b',
            r'\bmorning\s+(report|summary)\b'
        ]
        
        for pattern in briefing_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        
        return False
    
    async def get_briefing_status(self) -> Dict[str, Any]:
        """Get current briefing system status"""
        market_status = self.briefing_system.scanner.get_market_status()
        
        return {
            "monitoring_active": self.monitoring_active,
            "auto_briefing_sent_today": self.auto_briefing_sent,
            "market_open": market_status['market_open'],
            "market_status": market_status['market_status'],
            "chat_callback_registered": self.chat_callback is not None,
            "last_briefing_time": self.briefing_system.last_briefing.timestamp.isoformat() if self.briefing_system.last_briefing else None
        }
    
    async def force_briefing_generation(self) -> str:
        """Force generation of new briefing (admin/debug function)"""
        try:
            logger.info("🔄 Forcing briefing generation...")
            briefing = await self.briefing_system.generate_morning_briefing()
            response = self.briefing_system.format_briefing_for_chat(briefing)
            logger.info("✅ Forced briefing generation completed")
            return "🔄 **Forced Briefing Update:**\n\n" + response
        except Exception as e:
            logger.error(f"❌ Error in forced briefing generation: {e}")
            return f"❌ Error generating briefing: {e}"

# Example integration with existing chat system
class AtlasChatIntegration:
    """Example integration with A.T.L.A.S. chat system"""
    
    def __init__(self):
        self.briefing_handler = AtlasChatBriefingHandler()
        
        # Register this class's send method as the chat callback
        self.briefing_handler.register_chat_callback(self.send_to_chat)
        
        logger.info("✅ A.T.L.A.S. Chat Integration initialized")
    
    async def start(self):
        """Start the chat integration"""
        await self.briefing_handler.start_monitoring()
        logger.info("🚀 Chat integration started - monitoring for market open")
    
    async def stop(self):
        """Stop the chat integration"""
        await self.briefing_handler.stop_monitoring()
        logger.info("⏹️ Chat integration stopped")
    
    async def send_to_chat(self, message: str):
        """Send message to chat interface (implement based on your chat system)"""
        # This would integrate with your actual chat system
        # For now, just log the message
        logger.info("💬 CHAT MESSAGE:")
        logger.info(message)
        
        # In a real implementation, this might:
        # - Send to WebSocket clients
        # - Store in chat database
        # - Send push notifications
        # - Update web interface
    
    async def process_user_message(self, message: str, user_id: str = None) -> Optional[str]:
        """Process incoming user message and return response if applicable"""
        return await self.briefing_handler.handle_chat_message(message, user_id)
    
    async def get_status(self) -> Dict[str, Any]:
        """Get integration status"""
        return await self.briefing_handler.get_briefing_status()

# Global instance for easy access
chat_integration = AtlasChatIntegration()

# Export main components
__all__ = ['AtlasChatBriefingHandler', 'AtlasChatIntegration', 'chat_integration']
