#!/usr/bin/env python3
"""
Test Terminal-to-Web Integration for A.T.L.A.S. v5.0
Verifies that terminal operations are reflected in the web interface
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AtlasTerminalWebTester:
    """Test terminal-to-web integration"""
    
    def __init__(self, base_url="http://localhost:8002"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_api_endpoint(self, endpoint, method="GET", data=None):
        """Test an API endpoint and return response"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == "GET":
                async with self.session.get(url) as response:
                    result = await response.json()
                    logger.info(f"✅ {endpoint}: {response.status}")
                    return result
            elif method == "POST":
                async with self.session.post(url, json=data) as response:
                    result = await response.json()
                    logger.info(f"✅ {endpoint}: {response.status}")
                    return result
                    
        except Exception as e:
            logger.error(f"❌ {endpoint} failed: {e}")
            return None
    
    async def test_chat_interaction(self):
        """Test chat interaction that should appear in web interface"""
        logger.info("🗣️ Testing chat interaction...")
        
        chat_message = {
            "message": "What is the current market status?",
            "session_id": "terminal_test"
        }
        
        response = await self.test_api_endpoint("/api/v1/chat", "POST", chat_message)
        
        if response and "response" in response:
            logger.info(f"💬 Chat response received: {response['response'][:100]}...")
            return True
        else:
            logger.error("❌ Chat interaction failed")
            return False
    
    async def test_scanner_operations(self):
        """Test scanner operations that should update web interface"""
        logger.info("🔍 Testing scanner operations...")
        
        # Test Lee Method signals
        signals = await self.test_api_endpoint("/api/v1/lee_method/signals")
        if signals:
            logger.info(f"📊 Lee Method signals: {signals.get('total_signals', 0)} active")
        
        # Test scanner stats
        stats = await self.test_api_endpoint("/api/v1/lee_method/stats")
        if stats:
            logger.info("📈 Scanner stats retrieved successfully")
        
        return signals is not None and stats is not None
    
    async def test_trading_data(self):
        """Test trading data that should display in web interface"""
        logger.info("💰 Testing trading data...")
        
        # Test trading positions
        positions = await self.test_api_endpoint("/api/v1/trading/positions")
        if positions:
            logger.info(f"💼 Trading positions: {len(positions.get('positions', []))} positions")
            logger.info(f"💵 Account value: ${positions.get('total_value', 0):,.2f}")
        
        return positions is not None
    
    async def test_system_status(self):
        """Test system status that should be visible in web interface"""
        logger.info("⚙️ Testing system status...")
        
        # Test health endpoint
        health = await self.test_api_endpoint("/api/v1/health")
        if health:
            logger.info(f"❤️ System health: {health.get('status', 'unknown')}")
        
        # Test WebSocket metrics
        ws_metrics = await self.test_api_endpoint("/api/v1/websocket-metrics")
        if ws_metrics:
            metrics = ws_metrics.get('websocket_metrics', {})
            logger.info(f"🔗 WebSocket connections: {metrics.get('total_connections', 0)}")
        
        return health is not None and ws_metrics is not None
    
    async def test_real_time_updates(self):
        """Test that real-time updates work"""
        logger.info("⚡ Testing real-time updates...")
        
        # Trigger multiple API calls to simulate activity
        tasks = []
        for i in range(3):
            tasks.append(self.test_api_endpoint("/api/v1/health"))
            tasks.append(self.test_api_endpoint("/api/v1/lee_method/signals"))
            await asyncio.sleep(1)  # Small delay between calls
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        successful = sum(1 for result in results if result is not None)
        
        logger.info(f"⚡ Real-time updates: {successful}/{len(tasks)} successful")
        return successful > len(tasks) * 0.8  # 80% success rate
    
    async def run_comprehensive_test(self):
        """Run comprehensive terminal-to-web integration test"""
        logger.info("🚀 Starting Terminal-to-Web Integration Test")
        
        tests = [
            ("Chat Interaction", self.test_chat_interaction),
            ("Scanner Operations", self.test_scanner_operations),
            ("Trading Data", self.test_trading_data),
            ("System Status", self.test_system_status),
            ("Real-time Updates", self.test_real_time_updates)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 Running: {test_name}")
            try:
                result = await test_func()
                results[test_name] = result
                logger.info(f"{'✅ PASS' if result else '❌ FAIL'}: {test_name}")
            except Exception as e:
                logger.error(f"❌ ERROR in {test_name}: {e}")
                results[test_name] = False
        
        # Summary
        logger.info("\n📊 Integration Test Results:")
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"   {test_name}: {status}")
        
        logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All terminal-to-web integration tests PASSED!")
            return True
        else:
            logger.error("❌ Some integration tests FAILED!")
            return False

async def main():
    """Main test function"""
    async with AtlasTerminalWebTester() as tester:
        success = await tester.run_comprehensive_test()
        return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
