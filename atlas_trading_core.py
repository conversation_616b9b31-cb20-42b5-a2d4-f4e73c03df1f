"""
A.T.L.A.S. Trading Core - Compatibility Bridge
This file provides backward compatibility by importing from the consolidated trading engine.
"""

import logging

logger = logging.getLogger(__name__)

# Note: atlas_v5_consolidated directory does not exist - using fallback implementation
logger.info("[BRIDGE] Using fallback trading engine implementation")

# Fallback implementation for critical functionality
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

class TradeType(Enum):
    """Trade type enumeration"""
    BUY = "buy"
    SELL = "sell"
    SHORT = "short"
    COVER = "cover"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class TradeStatus(Enum):
    """Trade status enumeration"""
    PENDING = "pending"
    FILLED = "filled"
    PARTIAL = "partial"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
@dataclass
class Trade:
    """Trade data structure"""
    symbol: str
    trade_type: TradeType
    order_type: OrderType
    quantity: float
    price: Optional[float]
    stop_price: Optional[float]
    status: TradeStatus
    timestamp: datetime
    trade_id: Optional[str] = None
    filled_quantity: float = 0
    filled_price: Optional[float] = None

@dataclass
class Position:
    """Position data structure"""
    symbol: str
    quantity: float
    average_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime

class AtlasTradingEngine:
    """Fallback implementation of AtlasTradingEngine"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.is_connected = False
        self.logger.warning("[FALLBACK] Using fallback AtlasTradingEngine implementation")

    async def initialize(self):
        """Initialize the trading engine"""
        self.logger.info("[FALLBACK] Trading engine initialized in fallback mode")
        self.is_connected = True
        return True

    async def connect(self):
        """Connect to trading platform"""
        self.is_connected = True
        self.logger.info("[FALLBACK] Trading engine connected (simulated)")
        return True

    async def disconnect(self):
        """Disconnect from trading platform"""
        self.is_connected = False
        self.logger.info("[FALLBACK] Trading engine disconnected")

    async def place_order(self, symbol: str, trade_type: TradeType, quantity: float,
                        order_type: OrderType = OrderType.MARKET,
                        price: Optional[float] = None,
                        stop_price: Optional[float] = None) -> Trade:
        """Place a trading order"""
        trade = Trade(
            symbol=symbol,
            trade_type=trade_type,
            order_type=order_type,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            status=TradeStatus.PENDING,
            timestamp=datetime.now(),
            trade_id=f"FALLBACK_{len(self.trades) + 1:06d}"
        )

        self.trades.append(trade)
        self.logger.info(f"[FALLBACK] Order placed: {trade.trade_id} - {trade_type.value} {quantity} {symbol}")

        # Simulate immediate fill for market orders
        if order_type == OrderType.MARKET:
            trade.status = TradeStatus.FILLED
            trade.filled_quantity = quantity
            trade.filled_price = price or 100.0  # Default price

            # Update position
            await self._update_position(trade)

        return trade

    async def _update_position(self, trade: Trade):
        """Update position based on filled trade"""
        symbol = trade.symbol

        if symbol not in self.positions:
            # New position
            if trade.trade_type in [TradeType.BUY, TradeType.COVER]:
                quantity = trade.filled_quantity
            else:  # SELL or SHORT
                quantity = -trade.filled_quantity

            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=quantity,
                average_price=trade.filled_price or 0,
                market_value=quantity * (trade.filled_price or 0),
                unrealized_pnl=0,
                realized_pnl=0,
                timestamp=datetime.now()
            )
        else:
            # Update existing position
            position = self.positions[symbol]

            if trade.trade_type in [TradeType.BUY, TradeType.COVER]:
                new_quantity = position.quantity + trade.filled_quantity
            else:  # SELL or SHORT
                new_quantity = position.quantity - trade.filled_quantity

            # Update average price if adding to position
            if (position.quantity > 0 and trade.trade_type == TradeType.BUY) or \
               (position.quantity < 0 and trade.trade_type == TradeType.SHORT):
                total_cost = (position.quantity * position.average_price) + \
                           (trade.filled_quantity * (trade.filled_price or 0))
                position.average_price = total_cost / new_quantity if new_quantity != 0 else 0

            position.quantity = new_quantity
            position.market_value = new_quantity * (trade.filled_price or 0)
            position.timestamp = datetime.now()

            # Remove position if quantity is zero
            if abs(new_quantity) < 0.001:
                del self.positions[symbol]

    async def get_positions(self) -> Dict[str, Position]:
        """Get all current positions"""
        return self.positions.copy()

    async def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for a specific symbol"""
        return self.positions.get(symbol)

    async def get_trades(self, symbol: Optional[str] = None) -> List[Trade]:
        """Get trade history"""
        if symbol:
            return [trade for trade in self.trades if trade.symbol == symbol]
        return self.trades.copy()

    async def cancel_order(self, trade_id: str) -> bool:
        """Cancel a pending order"""
        for trade in self.trades:
            if trade.trade_id == trade_id and trade.status == TradeStatus.PENDING:
                trade.status = TradeStatus.CANCELLED
                self.logger.info(f"[FALLBACK] Order cancelled: {trade_id}")
                return True

        self.logger.warning(f"[FALLBACK] Order not found or cannot be cancelled: {trade_id}")
        return False

    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        total_value = sum(pos.market_value for pos in self.positions.values())
        total_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())

        return {
            "account_id": "FALLBACK_ACCOUNT",
            "buying_power": 100000.0,  # Default buying power
            "cash": 50000.0,           # Default cash
            "portfolio_value": total_value,
            "total_pnl": total_pnl,
            "positions_count": len(self.positions),
            "trades_count": len(self.trades),
            "connected": self.is_connected
        }

    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine status"""
        return {
            "status": "fallback",
            "mode": "fallback",
            "connected": self.is_connected,
            "positions": len(self.positions),
            "trades": len(self.trades),
            "platform": "simulation"
        }

# Export the main classes for compatibility
__all__ = [
    'AtlasTradingEngine',
    'TradeType',
    'OrderType',
    'TradeStatus',
    'Trade',
    'Position'
]
